import type { Session } from "next-auth";
import type { TrpcSessionUser } from "@calcom/trpc/server/types";
type MyStatsOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
        session: Session;
    };
};
export declare const myStatsHandler: ({ ctx }: MyStatsOptions) => Promise<{
    id: number;
    sumOfBookings: number | undefined;
    sumOfCalendars: number | undefined;
    sumOfTeams: number | undefined;
    sumOfEventTypes: number | undefined;
    sumOfTeamEventTypes: number | undefined;
}>;
export {};
