import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TNoShowInputSchema } from "./markNoShow.schema";
type NoShowOptions = {
    input: TNoShowInputSchema;
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
};
export declare const markNoShow: ({ ctx, input }: NoShowOptions) => Promise<{
    attendees: import("@calcom/features/handleMarkNoShow").NoShowAttendees;
    noShowHost: boolean;
    message: string;
}>;
export {};
