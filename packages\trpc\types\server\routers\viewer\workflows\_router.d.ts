export declare const workflowsRouter: import("@trpc/server/unstable-core-do-not-import").CreateRouterInner<import("@trpc/server/unstable-core-do-not-import").RootConfig<{
    ctx: import("../../../createContext").InnerContext;
    meta: object;
    errorShape: {
        message: string;
        code: number;
        data: {
            code: string;
            httpStatus: number;
            path?: string;
            [key: string]: unknown;
        };
    };
    transformer: {
        stringify: (object: any) => string;
        parse: <T = unknown>(string: string) => T;
        serialize: (object: any) => import("superjson/dist/types").SuperJSONResult;
        deserialize: <T_1 = unknown>(payload: import("superjson/dist/types").SuperJSONResult) => T_1;
        registerClass: (v: import("superjson/dist/types").Class, options?: string | import("superjson/dist/class-registry").RegisterOptions | undefined) => void;
        registerSymbol: (v: Symbol, identifier?: string | undefined) => void;
        registerCustom: <I, O extends import("superjson/dist/types").JSONValue>(transformer: Omit<import("superjson/dist/custom-transformer-registry").CustomTransfomer<I, O>, "name">, name: string) => void;
        allowErrorProps: (...props: string[]) => void;
    };
}>, {
    list: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            teamId?: number | undefined;
            userId?: number | undefined;
        } | undefined;
        output: {
            workflows: import("@calcom/ee/workflows/components/WorkflowListPage").WorkflowType[];
        };
    }>;
    get: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            id: number;
        };
        output: ({
            name: string;
            id: number;
            time: number | null;
            userId: number | null;
            team: {
                name: string;
                id: number;
                slug: string | null;
                isOrganization: boolean;
                members: {
                    id: number;
                    role: import(".prisma/client").$Enums.MembershipRole;
                    userId: number;
                    createdAt: Date | null;
                    updatedAt: Date | null;
                    disableImpersonation: boolean;
                    teamId: number;
                    accepted: boolean;
                    customRoleId: string | null;
                }[];
            } | null;
            teamId: number | null;
            steps: {
                id: number;
                template: import(".prisma/client").$Enums.WorkflowTemplates;
                action: import(".prisma/client").$Enums.WorkflowActions;
                stepNumber: number;
                workflowId: number;
                sendTo: string | null;
                reminderBody: string | null;
                emailSubject: string | null;
                numberRequired: boolean | null;
                sender: string | null;
                numberVerificationPending: boolean;
                includeCalendarEvent: boolean;
                verifiedAt: Date | null;
                agentId: string | null;
            }[];
            trigger: import(".prisma/client").$Enums.WorkflowTriggerEvents;
            timeUnit: import(".prisma/client").$Enums.TimeUnit | null;
            isActiveOnAll: boolean;
            activeOn: {
                eventType: {
                    id: number;
                    length: number;
                    title: string;
                    metadata: import(".prisma/client").Prisma.JsonValue;
                    description: string | null;
                    userId: number | null;
                    timeZone: string | null;
                    slug: string;
                    parentId: number | null;
                    bookingLimits: import(".prisma/client").Prisma.JsonValue;
                    teamId: number | null;
                    hidden: boolean;
                    interfaceLanguage: string | null;
                    position: number;
                    locations: import(".prisma/client").Prisma.JsonValue;
                    offsetStart: number;
                    profileId: number | null;
                    useEventLevelSelectedCalendars: boolean;
                    eventName: string | null;
                    bookingFields: import(".prisma/client").Prisma.JsonValue;
                    periodType: import(".prisma/client").$Enums.PeriodType;
                    periodStartDate: Date | null;
                    periodEndDate: Date | null;
                    periodDays: number | null;
                    periodCountCalendarDays: boolean | null;
                    lockTimeZoneToggleOnBookingPage: boolean;
                    lockedTimeZone: string | null;
                    requiresConfirmation: boolean;
                    requiresConfirmationWillBlockSlot: boolean;
                    requiresConfirmationForFreeEmail: boolean;
                    requiresBookerEmailVerification: boolean;
                    canSendCalVideoTranscriptionEmails: boolean;
                    autoTranslateDescriptionEnabled: boolean;
                    recurringEvent: import(".prisma/client").Prisma.JsonValue;
                    disableGuests: boolean;
                    hideCalendarNotes: boolean;
                    hideCalendarEventDetails: boolean;
                    minimumBookingNotice: number;
                    beforeEventBuffer: number;
                    afterEventBuffer: number;
                    seatsPerTimeSlot: number | null;
                    onlyShowFirstAvailableSlot: boolean;
                    disableCancelling: boolean | null;
                    disableRescheduling: boolean | null;
                    seatsShowAttendees: boolean | null;
                    seatsShowAvailabilityCount: boolean | null;
                    schedulingType: import(".prisma/client").$Enums.SchedulingType | null;
                    scheduleId: number | null;
                    allowReschedulingCancelledBookings: boolean | null;
                    price: number;
                    currency: string;
                    slotInterval: number | null;
                    successRedirectUrl: string | null;
                    forwardParamsSuccessRedirect: boolean | null;
                    durationLimits: import(".prisma/client").Prisma.JsonValue;
                    isInstantEvent: boolean;
                    instantMeetingExpiryTimeOffsetInSeconds: number;
                    instantMeetingScheduleId: number | null;
                    instantMeetingParameters: string[];
                    assignAllTeamMembers: boolean;
                    assignRRMembersUsingSegment: boolean;
                    rrSegmentQueryValue: import(".prisma/client").Prisma.JsonValue;
                    useEventTypeDestinationCalendarEmail: boolean;
                    isRRWeightsEnabled: boolean;
                    maxLeadThreshold: number | null;
                    includeNoShowInRRCalculation: boolean;
                    allowReschedulingPastBookings: boolean;
                    hideOrganizerEmail: boolean;
                    maxActiveBookingsPerBooker: number | null;
                    maxActiveBookingPerBookerOfferReschedule: boolean;
                    customReplyToEmail: string | null;
                    eventTypeColor: import(".prisma/client").Prisma.JsonValue;
                    rescheduleWithSameRoundRobinHost: boolean;
                    secondaryEmailId: number | null;
                    useBookerTimezone: boolean;
                    restrictionScheduleId: number | null;
                    bookingRequiresAuthentication: boolean;
                };
            }[];
            activeOnTeams: {
                team: {
                    name: string;
                    id: number;
                    metadata: import(".prisma/client").Prisma.JsonValue;
                    createdAt: Date;
                    bio: string | null;
                    timeZone: string;
                    weekStart: string;
                    hideBranding: boolean;
                    theme: string | null;
                    timeFormat: number | null;
                    brandColor: string | null;
                    darkBrandColor: string | null;
                    smsLockState: import(".prisma/client").$Enums.SMSLockState;
                    smsLockReviewedByAdmin: boolean;
                    slug: string | null;
                    logoUrl: string | null;
                    calVideoLogo: string | null;
                    appLogo: string | null;
                    appIconLogo: string | null;
                    hideTeamProfileLink: boolean;
                    isPrivate: boolean;
                    hideBookATeamMember: boolean;
                    rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
                    rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
                    bannerUrl: string | null;
                    parentId: number | null;
                    isOrganization: boolean;
                    pendingPayment: boolean;
                    isPlatform: boolean;
                    createdByOAuthClientId: string | null;
                    bookingLimits: import(".prisma/client").Prisma.JsonValue;
                    includeManagedEventsInLimits: boolean;
                };
            }[];
        } & {
            permissions: import("@calcom/lib/server/repository/workflow-permissions").WorkflowPermissions;
            readOnly: boolean;
        }) | null;
    }>;
    create: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            teamId?: number | undefined;
        };
        output: {
            workflow: {
                id: number;
                position: number;
                name: string;
                userId: number | null;
                teamId: number | null;
                isActiveOnAll: boolean;
                trigger: import(".prisma/client").$Enums.WorkflowTriggerEvents;
                time: number | null;
                timeUnit: import(".prisma/client").$Enums.TimeUnit | null;
            };
        };
    }>;
    delete: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            id: number;
        };
        output: {
            id: number;
        };
    }>;
    update: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            name: string;
            id: number;
            time: number | null;
            steps: {
                id: number;
                template: "CANCELLED" | "REMINDER" | "CUSTOM" | "RESCHEDULED" | "COMPLETED" | "RATING";
                action: "EMAIL_HOST" | "EMAIL_ATTENDEE" | "SMS_ATTENDEE" | "SMS_NUMBER" | "EMAIL_ADDRESS" | "WHATSAPP_ATTENDEE" | "WHATSAPP_NUMBER" | "CAL_AI_PHONE_CALL";
                stepNumber: number;
                workflowId: number;
                sendTo: string | null;
                reminderBody: string | null;
                emailSubject: string | null;
                numberRequired: boolean | null;
                sender: string | null;
                includeCalendarEvent: boolean;
                senderName: string | null;
            }[];
            trigger: "AFTER_HOSTS_CAL_VIDEO_NO_SHOW" | "AFTER_GUESTS_CAL_VIDEO_NO_SHOW" | "BEFORE_EVENT" | "EVENT_CANCELLED" | "NEW_EVENT" | "AFTER_EVENT" | "RESCHEDULE_EVENT";
            timeUnit: "DAY" | "HOUR" | "MINUTE" | null;
            activeOn: number[];
            isActiveOnAll?: boolean | undefined;
        };
        output: {
            workflow: ({
                team: {
                    name: string;
                    id: number;
                    slug: string | null;
                    isOrganization: boolean;
                    members: {
                        id: number;
                        role: import(".prisma/client").$Enums.MembershipRole;
                        userId: number;
                        createdAt: Date | null;
                        updatedAt: Date | null;
                        disableImpersonation: boolean;
                        teamId: number;
                        accepted: boolean;
                        customRoleId: string | null;
                    }[];
                } | null;
                steps: {
                    id: number;
                    template: import(".prisma/client").$Enums.WorkflowTemplates;
                    action: import(".prisma/client").$Enums.WorkflowActions;
                    stepNumber: number;
                    workflowId: number;
                    sendTo: string | null;
                    reminderBody: string | null;
                    emailSubject: string | null;
                    numberRequired: boolean | null;
                    sender: string | null;
                    numberVerificationPending: boolean;
                    includeCalendarEvent: boolean;
                    verifiedAt: Date | null;
                    agentId: string | null;
                }[];
                activeOn: {
                    eventType: {
                        id: number;
                        length: number;
                        title: string;
                        metadata: import(".prisma/client").Prisma.JsonValue;
                        description: string | null;
                        userId: number | null;
                        timeZone: string | null;
                        slug: string;
                        parentId: number | null;
                        bookingLimits: import(".prisma/client").Prisma.JsonValue;
                        teamId: number | null;
                        hidden: boolean;
                        interfaceLanguage: string | null;
                        position: number;
                        locations: import(".prisma/client").Prisma.JsonValue;
                        offsetStart: number;
                        profileId: number | null;
                        useEventLevelSelectedCalendars: boolean;
                        eventName: string | null;
                        bookingFields: import(".prisma/client").Prisma.JsonValue;
                        periodType: import(".prisma/client").$Enums.PeriodType;
                        periodStartDate: Date | null;
                        periodEndDate: Date | null;
                        periodDays: number | null;
                        periodCountCalendarDays: boolean | null;
                        lockTimeZoneToggleOnBookingPage: boolean;
                        lockedTimeZone: string | null;
                        requiresConfirmation: boolean;
                        requiresConfirmationWillBlockSlot: boolean;
                        requiresConfirmationForFreeEmail: boolean;
                        requiresBookerEmailVerification: boolean;
                        canSendCalVideoTranscriptionEmails: boolean;
                        autoTranslateDescriptionEnabled: boolean;
                        recurringEvent: import(".prisma/client").Prisma.JsonValue;
                        disableGuests: boolean;
                        hideCalendarNotes: boolean;
                        hideCalendarEventDetails: boolean;
                        minimumBookingNotice: number;
                        beforeEventBuffer: number;
                        afterEventBuffer: number;
                        seatsPerTimeSlot: number | null;
                        onlyShowFirstAvailableSlot: boolean;
                        disableCancelling: boolean | null;
                        disableRescheduling: boolean | null;
                        seatsShowAttendees: boolean | null;
                        seatsShowAvailabilityCount: boolean | null;
                        schedulingType: import(".prisma/client").$Enums.SchedulingType | null;
                        scheduleId: number | null;
                        allowReschedulingCancelledBookings: boolean | null;
                        price: number;
                        currency: string;
                        slotInterval: number | null;
                        successRedirectUrl: string | null;
                        forwardParamsSuccessRedirect: boolean | null;
                        durationLimits: import(".prisma/client").Prisma.JsonValue;
                        isInstantEvent: boolean;
                        instantMeetingExpiryTimeOffsetInSeconds: number;
                        instantMeetingScheduleId: number | null;
                        instantMeetingParameters: string[];
                        assignAllTeamMembers: boolean;
                        assignRRMembersUsingSegment: boolean;
                        rrSegmentQueryValue: import(".prisma/client").Prisma.JsonValue;
                        useEventTypeDestinationCalendarEmail: boolean;
                        isRRWeightsEnabled: boolean;
                        maxLeadThreshold: number | null;
                        includeNoShowInRRCalculation: boolean;
                        allowReschedulingPastBookings: boolean;
                        hideOrganizerEmail: boolean;
                        maxActiveBookingsPerBooker: number | null;
                        maxActiveBookingPerBookerOfferReschedule: boolean;
                        customReplyToEmail: string | null;
                        eventTypeColor: import(".prisma/client").Prisma.JsonValue;
                        rescheduleWithSameRoundRobinHost: boolean;
                        secondaryEmailId: number | null;
                        useBookerTimezone: boolean;
                        restrictionScheduleId: number | null;
                        bookingRequiresAuthentication: boolean;
                    };
                }[];
                activeOnTeams: {
                    team: {
                        name: string;
                        id: number;
                        metadata: import(".prisma/client").Prisma.JsonValue;
                        createdAt: Date;
                        bio: string | null;
                        timeZone: string;
                        weekStart: string;
                        hideBranding: boolean;
                        theme: string | null;
                        timeFormat: number | null;
                        brandColor: string | null;
                        darkBrandColor: string | null;
                        smsLockState: import(".prisma/client").$Enums.SMSLockState;
                        smsLockReviewedByAdmin: boolean;
                        slug: string | null;
                        logoUrl: string | null;
                        calVideoLogo: string | null;
                        appLogo: string | null;
                        appIconLogo: string | null;
                        hideTeamProfileLink: boolean;
                        isPrivate: boolean;
                        hideBookATeamMember: boolean;
                        rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
                        rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
                        bannerUrl: string | null;
                        parentId: number | null;
                        isOrganization: boolean;
                        pendingPayment: boolean;
                        isPlatform: boolean;
                        createdByOAuthClientId: string | null;
                        bookingLimits: import(".prisma/client").Prisma.JsonValue;
                        includeManagedEventsInLimits: boolean;
                    };
                }[];
            } & {
                name: string;
                id: number;
                time: number | null;
                userId: number | null;
                teamId: number | null;
                position: number;
                trigger: import(".prisma/client").$Enums.WorkflowTriggerEvents;
                timeUnit: import(".prisma/client").$Enums.TimeUnit | null;
                isActiveOnAll: boolean;
            }) | null;
        };
    }>;
    activateEventType: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            eventTypeId: number;
            workflowId: number;
        };
        output: void;
    }>;
    sendVerificationCode: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            phoneNumber: string;
        };
        output: void;
    }>;
    verifyPhoneNumber: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            code: string;
            phoneNumber: string;
            teamId?: number | undefined;
        };
        output: boolean;
    }>;
    getVerifiedNumbers: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            teamId?: number | undefined;
        };
        output: {
            id: number;
            userId: number | null;
            teamId: number | null;
            phoneNumber: string;
        }[];
    }>;
    getVerifiedEmails: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            teamId?: number | undefined;
        };
        output: string[];
    }>;
    verifyEmailCode: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            code: string;
            email: string;
            teamId?: number | undefined;
        };
        output: true;
    }>;
    getWorkflowActionOptions: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: {
            label: string;
            value: "EMAIL_HOST" | "EMAIL_ATTENDEE" | "SMS_ATTENDEE" | "SMS_NUMBER" | "EMAIL_ADDRESS" | "WHATSAPP_ATTENDEE" | "WHATSAPP_NUMBER";
            needsCredits: boolean;
        }[];
    }>;
    filteredList: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            filters?: {
                teamIds?: number[] | undefined;
                userIds?: number[] | undefined;
                upIds?: string[] | undefined;
            } | undefined;
        } | null | undefined;
        output: {
            filtered: ({
                id: number;
                position: number;
                name: string;
                userId: number | null;
                teamId: number | null;
                isActiveOnAll: boolean;
                trigger: import(".prisma/client").$Enums.WorkflowTriggerEvents;
                time: number | null;
                timeUnit: import(".prisma/client").$Enums.TimeUnit | null;
            } & {
                team: {
                    id: number;
                    name: string;
                    members: import(".prisma/client").Membership[];
                    slug: string | null;
                    logo?: string | null;
                } | null;
                steps: import("@calcom/ee/workflows/lib/types").WorkflowStep[];
                activeOnTeams?: {
                    team: {
                        id: number;
                        name?: string | null;
                    };
                }[];
                activeOn?: {
                    eventType: {
                        id: number;
                        title: string;
                        parentId: number | null;
                        _count: {
                            children: number;
                        };
                    };
                }[];
                readOnly?: boolean;
                permissions?: import("@calcom/lib/server/repository/workflow-permissions").WorkflowPermissions;
                isOrg?: boolean;
            } & {
                permissions: import("@calcom/lib/server/repository/workflow-permissions").WorkflowPermissions;
                readOnly: boolean;
            })[];
            totalCount: number;
        } | undefined;
    }>;
    getAllActiveWorkflows: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            eventType: {
                id: number;
                metadata: {
                    config?: {
                        useHostSchedulesForTeamEvent?: boolean | undefined;
                    } | undefined;
                    smartContractAddress?: string | undefined;
                    blockchainId?: number | undefined;
                    multipleDuration?: number[] | undefined;
                    giphyThankYouPage?: string | undefined;
                    additionalNotesRequired?: boolean | undefined;
                    disableSuccessPage?: boolean | undefined;
                    disableStandardEmails?: {
                        all?: {
                            host?: boolean | undefined;
                            attendee?: boolean | undefined;
                        } | undefined;
                        confirmation?: {
                            host?: boolean | undefined;
                            attendee?: boolean | undefined;
                        } | undefined;
                    } | undefined;
                    managedEventConfig?: {
                        unlockedFields?: {
                            users?: true | undefined;
                            children?: true | undefined;
                            length?: true | undefined;
                            title?: true | undefined;
                            metadata?: true | undefined;
                            description?: true | undefined;
                            userId?: true | undefined;
                            calVideoSettings?: true | undefined;
                            destinationCalendar?: true | undefined;
                            profile?: true | undefined;
                            team?: true | undefined;
                            schedule?: true | undefined;
                            availability?: true | undefined;
                            hashedLink?: true | undefined;
                            secondaryEmail?: true | undefined;
                            customInputs?: true | undefined;
                            timeZone?: true | undefined;
                            bookings?: true | undefined;
                            selectedCalendars?: true | undefined;
                            webhooks?: true | undefined;
                            workflows?: true | undefined;
                            hosts?: true | undefined;
                            slug?: true | undefined;
                            parentId?: true | undefined;
                            bookingLimits?: true | undefined;
                            parent?: true | undefined;
                            teamId?: true | undefined;
                            hidden?: true | undefined;
                            _count?: true | undefined;
                            interfaceLanguage?: true | undefined;
                            position?: true | undefined;
                            locations?: true | undefined;
                            offsetStart?: true | undefined;
                            profileId?: true | undefined;
                            useEventLevelSelectedCalendars?: true | undefined;
                            eventName?: true | undefined;
                            bookingFields?: true | undefined;
                            periodType?: true | undefined;
                            periodStartDate?: true | undefined;
                            periodEndDate?: true | undefined;
                            periodDays?: true | undefined;
                            periodCountCalendarDays?: true | undefined;
                            lockTimeZoneToggleOnBookingPage?: true | undefined;
                            lockedTimeZone?: true | undefined;
                            requiresConfirmation?: true | undefined;
                            requiresConfirmationWillBlockSlot?: true | undefined;
                            requiresConfirmationForFreeEmail?: true | undefined;
                            requiresBookerEmailVerification?: true | undefined;
                            canSendCalVideoTranscriptionEmails?: true | undefined;
                            autoTranslateDescriptionEnabled?: true | undefined;
                            recurringEvent?: true | undefined;
                            disableGuests?: true | undefined;
                            hideCalendarNotes?: true | undefined;
                            hideCalendarEventDetails?: true | undefined;
                            minimumBookingNotice?: true | undefined;
                            beforeEventBuffer?: true | undefined;
                            afterEventBuffer?: true | undefined;
                            seatsPerTimeSlot?: true | undefined;
                            onlyShowFirstAvailableSlot?: true | undefined;
                            disableCancelling?: true | undefined;
                            disableRescheduling?: true | undefined;
                            seatsShowAttendees?: true | undefined;
                            seatsShowAvailabilityCount?: true | undefined;
                            schedulingType?: true | undefined;
                            scheduleId?: true | undefined;
                            allowReschedulingCancelledBookings?: true | undefined;
                            price?: true | undefined;
                            currency?: true | undefined;
                            slotInterval?: true | undefined;
                            successRedirectUrl?: true | undefined;
                            forwardParamsSuccessRedirect?: true | undefined;
                            durationLimits?: true | undefined;
                            isInstantEvent?: true | undefined;
                            instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                            instantMeetingScheduleId?: true | undefined;
                            instantMeetingParameters?: true | undefined;
                            assignAllTeamMembers?: true | undefined;
                            assignRRMembersUsingSegment?: true | undefined;
                            rrSegmentQueryValue?: true | undefined;
                            useEventTypeDestinationCalendarEmail?: true | undefined;
                            isRRWeightsEnabled?: true | undefined;
                            maxLeadThreshold?: true | undefined;
                            includeNoShowInRRCalculation?: true | undefined;
                            allowReschedulingPastBookings?: true | undefined;
                            hideOrganizerEmail?: true | undefined;
                            maxActiveBookingsPerBooker?: true | undefined;
                            maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                            customReplyToEmail?: true | undefined;
                            eventTypeColor?: true | undefined;
                            rescheduleWithSameRoundRobinHost?: true | undefined;
                            secondaryEmailId?: true | undefined;
                            useBookerTimezone?: true | undefined;
                            restrictionScheduleId?: true | undefined;
                            bookingRequiresAuthentication?: true | undefined;
                            owner?: true | undefined;
                            instantMeetingSchedule?: true | undefined;
                            aiPhoneCallConfig?: true | undefined;
                            fieldTranslations?: true | undefined;
                            restrictionSchedule?: true | undefined;
                            hostGroups?: true | undefined;
                        } | undefined;
                    } | undefined;
                    requiresConfirmationThreshold?: {
                        time: number;
                        unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
                    } | undefined;
                    bookerLayouts?: {
                        enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
                        defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
                    } | null | undefined;
                    apps?: unknown;
                } | null;
                teamId?: number | null | undefined;
                parent?: {
                    id: number | null;
                    teamId: number | null;
                } | null | undefined;
                userId?: number | null | undefined;
            };
        };
        output: import("@calcom/ee/workflows/lib/types").Workflow[];
    }>;
    workflowOrder: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            ids: number[];
        };
        output: void;
    }>;
}>;
