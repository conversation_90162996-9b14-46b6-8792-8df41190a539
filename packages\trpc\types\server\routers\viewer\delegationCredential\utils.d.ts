import type { Prisma } from "@calcom/prisma/client";
export declare class InvalidServiceAccountKeyError extends Error {
    constructor(message: string);
}
export declare function ensureDelegationCredentialNotAlreadyConfigured({ domain, currentOrganizationId, delegationCredentialBeingUpdatedId, }: {
    domain: string;
    currentOrganizationId: number;
    delegationCredentialBeingUpdatedId: string | null;
}): Promise<void>;
export declare function parseServiceAccountKey(serviceAccountKey: Prisma.JsonValue): {
    client_id: string;
    private_key: string;
    client_email: string;
} | null;
export declare const handleDelegationCredentialError: (error: unknown) => never;
export declare const ensureNoServiceAccountKey: <T extends {
    id: string;
    serviceAccountKey?: unknown;
} | null>(delegationCredential: T) => (Omit<NonNullable<T>, "serviceAccountKey"> & {
    serviceAccountKey: undefined;
}) | null;
