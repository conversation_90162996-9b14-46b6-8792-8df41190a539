import type { TRPCContext } from "../../../createContext";
import type { TrpcSessionUser } from "../../../types";
import type { TResendVerifyEmailSchema } from "./resendVerifyEmail.schema";
type ResendEmailOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
        req: TRPCContext["req"] | undefined;
    };
    input: TResendVerifyEmailSchema;
};
export declare const resendVerifyEmail: ({ input, ctx }: ResendEmailOptions) => Promise<{
    ok: boolean;
    skipped: boolean;
}>;
export {};
