import { z } from "zod";
export declare const eventTypesRouter: import("@trpc/server/unstable-core-do-not-import").CreateRouterInner<import("@trpc/server/unstable-core-do-not-import").RootConfig<{
    ctx: import("../../../createContext").InnerContext;
    meta: object;
    errorShape: {
        message: string;
        code: number;
        data: {
            code: string;
            httpStatus: number;
            path?: string;
            [key: string]: unknown;
        };
    };
    transformer: {
        stringify: (object: any) => string;
        parse: <T = unknown>(string: string) => T;
        serialize: (object: any) => import("superjson/dist/types").SuperJSONResult;
        deserialize: <T_1 = unknown>(payload: import("superjson/dist/types").SuperJSONResult) => T_1;
        registerClass: (v: import("superjson/dist/types").Class, options?: string | import("superjson/dist/class-registry").RegisterOptions | undefined) => void;
        registerSymbol: (v: Symbol, identifier?: string | undefined) => void;
        registerCustom: <I, O extends import("superjson/dist/types").JSONValue>(transformer: Omit<import("superjson/dist/custom-transformer-registry").CustomTransfomer<I, O>, "name">, name: string) => void;
        allowErrorProps: (...props: string[]) => void;
    };
}>, {
    getByViewer: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            filters?: {
                teamIds?: number[] | undefined;
                upIds?: string[] | undefined;
                schedulingTypes?: ("ROUND_ROBIN" | "COLLECTIVE" | "MANAGED")[] | undefined;
            } | undefined;
            forRoutingForms?: boolean | undefined;
        } | null | undefined;
        output: {
            allUsersAcrossAllEventTypes: Map<number, {
                name: string | null;
                id: number;
                username: string | null;
                avatarUrl: string | null;
                timeZone: string;
            } & {
                nonProfileUsername: string | null;
                profile: import("@calcom/types/UserProfile").UserProfile;
            }>;
            eventTypeGroups: {
                eventTypes: {
                    userIds: number[];
                    safeDescription: string | undefined;
                    metadata: {
                        config?: {
                            useHostSchedulesForTeamEvent?: boolean | undefined;
                        } | undefined;
                        smartContractAddress?: string | undefined;
                        blockchainId?: number | undefined;
                        multipleDuration?: number[] | undefined;
                        giphyThankYouPage?: string | undefined;
                        additionalNotesRequired?: boolean | undefined;
                        disableSuccessPage?: boolean | undefined;
                        disableStandardEmails?: {
                            all?: {
                                host?: boolean | undefined;
                                attendee?: boolean | undefined;
                            } | undefined;
                            confirmation?: {
                                host?: boolean | undefined;
                                attendee?: boolean | undefined;
                            } | undefined;
                        } | undefined;
                        managedEventConfig?: {
                            unlockedFields?: {
                                users?: true | undefined;
                                children?: true | undefined;
                                length?: true | undefined;
                                title?: true | undefined;
                                metadata?: true | undefined;
                                description?: true | undefined;
                                userId?: true | undefined;
                                calVideoSettings?: true | undefined;
                                destinationCalendar?: true | undefined;
                                profile?: true | undefined;
                                team?: true | undefined;
                                schedule?: true | undefined;
                                availability?: true | undefined;
                                hashedLink?: true | undefined;
                                secondaryEmail?: true | undefined;
                                customInputs?: true | undefined;
                                timeZone?: true | undefined;
                                bookings?: true | undefined;
                                selectedCalendars?: true | undefined;
                                webhooks?: true | undefined;
                                workflows?: true | undefined;
                                hosts?: true | undefined;
                                slug?: true | undefined;
                                parentId?: true | undefined;
                                bookingLimits?: true | undefined;
                                parent?: true | undefined;
                                teamId?: true | undefined;
                                hidden?: true | undefined;
                                _count?: true | undefined;
                                interfaceLanguage?: true | undefined;
                                position?: true | undefined;
                                locations?: true | undefined;
                                offsetStart?: true | undefined;
                                profileId?: true | undefined;
                                useEventLevelSelectedCalendars?: true | undefined;
                                eventName?: true | undefined;
                                bookingFields?: true | undefined;
                                periodType?: true | undefined;
                                periodStartDate?: true | undefined;
                                periodEndDate?: true | undefined;
                                periodDays?: true | undefined;
                                periodCountCalendarDays?: true | undefined;
                                lockTimeZoneToggleOnBookingPage?: true | undefined;
                                lockedTimeZone?: true | undefined;
                                requiresConfirmation?: true | undefined;
                                requiresConfirmationWillBlockSlot?: true | undefined;
                                requiresConfirmationForFreeEmail?: true | undefined;
                                requiresBookerEmailVerification?: true | undefined;
                                canSendCalVideoTranscriptionEmails?: true | undefined;
                                autoTranslateDescriptionEnabled?: true | undefined;
                                recurringEvent?: true | undefined;
                                disableGuests?: true | undefined;
                                hideCalendarNotes?: true | undefined;
                                hideCalendarEventDetails?: true | undefined;
                                minimumBookingNotice?: true | undefined;
                                beforeEventBuffer?: true | undefined;
                                afterEventBuffer?: true | undefined;
                                seatsPerTimeSlot?: true | undefined;
                                onlyShowFirstAvailableSlot?: true | undefined;
                                disableCancelling?: true | undefined;
                                disableRescheduling?: true | undefined;
                                seatsShowAttendees?: true | undefined;
                                seatsShowAvailabilityCount?: true | undefined;
                                schedulingType?: true | undefined;
                                scheduleId?: true | undefined;
                                allowReschedulingCancelledBookings?: true | undefined;
                                price?: true | undefined;
                                currency?: true | undefined;
                                slotInterval?: true | undefined;
                                successRedirectUrl?: true | undefined;
                                forwardParamsSuccessRedirect?: true | undefined;
                                durationLimits?: true | undefined;
                                isInstantEvent?: true | undefined;
                                instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                                instantMeetingScheduleId?: true | undefined;
                                instantMeetingParameters?: true | undefined;
                                assignAllTeamMembers?: true | undefined;
                                assignRRMembersUsingSegment?: true | undefined;
                                rrSegmentQueryValue?: true | undefined;
                                useEventTypeDestinationCalendarEmail?: true | undefined;
                                isRRWeightsEnabled?: true | undefined;
                                maxLeadThreshold?: true | undefined;
                                includeNoShowInRRCalculation?: true | undefined;
                                allowReschedulingPastBookings?: true | undefined;
                                hideOrganizerEmail?: true | undefined;
                                maxActiveBookingsPerBooker?: true | undefined;
                                maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                                customReplyToEmail?: true | undefined;
                                eventTypeColor?: true | undefined;
                                rescheduleWithSameRoundRobinHost?: true | undefined;
                                secondaryEmailId?: true | undefined;
                                useBookerTimezone?: true | undefined;
                                restrictionScheduleId?: true | undefined;
                                bookingRequiresAuthentication?: true | undefined;
                                owner?: true | undefined;
                                instantMeetingSchedule?: true | undefined;
                                aiPhoneCallConfig?: true | undefined;
                                fieldTranslations?: true | undefined;
                                restrictionSchedule?: true | undefined;
                                hostGroups?: true | undefined;
                            } | undefined;
                        } | undefined;
                        requiresConfirmationThreshold?: {
                            time: number;
                            unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
                        } | undefined;
                        bookerLayouts?: {
                            enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
                            defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
                        } | null | undefined;
                        apps?: unknown;
                    } | null;
                    children: {
                        users: ({
                            name: string | null;
                            id: number;
                            username: string | null;
                            avatarUrl: string | null;
                            timeZone: string;
                        } & {
                            nonProfileUsername: string | null;
                            profile: import("@calcom/types/UserProfile").UserProfile;
                        })[];
                        id: number;
                        length: number;
                        title: string;
                        metadata: import(".prisma/client").Prisma.JsonValue;
                        description: string | null;
                        userId: number | null;
                        timeZone: string | null;
                        slug: string;
                        parentId: number | null;
                        bookingLimits: import(".prisma/client").Prisma.JsonValue;
                        teamId: number | null;
                        hidden: boolean;
                        interfaceLanguage: string | null;
                        position: number;
                        locations: import(".prisma/client").Prisma.JsonValue;
                        offsetStart: number;
                        profileId: number | null;
                        useEventLevelSelectedCalendars: boolean;
                        eventName: string | null;
                        bookingFields: import(".prisma/client").Prisma.JsonValue;
                        periodType: import(".prisma/client").$Enums.PeriodType;
                        periodStartDate: Date | null;
                        periodEndDate: Date | null;
                        periodDays: number | null;
                        periodCountCalendarDays: boolean | null;
                        lockTimeZoneToggleOnBookingPage: boolean;
                        lockedTimeZone: string | null;
                        requiresConfirmation: boolean;
                        requiresConfirmationWillBlockSlot: boolean;
                        requiresConfirmationForFreeEmail: boolean;
                        requiresBookerEmailVerification: boolean;
                        canSendCalVideoTranscriptionEmails: boolean;
                        autoTranslateDescriptionEnabled: boolean;
                        recurringEvent: import(".prisma/client").Prisma.JsonValue;
                        disableGuests: boolean;
                        hideCalendarNotes: boolean;
                        hideCalendarEventDetails: boolean;
                        minimumBookingNotice: number;
                        beforeEventBuffer: number;
                        afterEventBuffer: number;
                        seatsPerTimeSlot: number | null;
                        onlyShowFirstAvailableSlot: boolean;
                        disableCancelling: boolean | null;
                        disableRescheduling: boolean | null;
                        seatsShowAttendees: boolean | null;
                        seatsShowAvailabilityCount: boolean | null;
                        schedulingType: import(".prisma/client").$Enums.SchedulingType | null;
                        scheduleId: number | null;
                        allowReschedulingCancelledBookings: boolean | null;
                        price: number;
                        currency: string;
                        slotInterval: number | null;
                        successRedirectUrl: string | null;
                        forwardParamsSuccessRedirect: boolean | null;
                        durationLimits: import(".prisma/client").Prisma.JsonValue;
                        isInstantEvent: boolean;
                        instantMeetingExpiryTimeOffsetInSeconds: number;
                        instantMeetingScheduleId: number | null;
                        instantMeetingParameters: string[];
                        assignAllTeamMembers: boolean;
                        assignRRMembersUsingSegment: boolean;
                        rrSegmentQueryValue: import(".prisma/client").Prisma.JsonValue;
                        useEventTypeDestinationCalendarEmail: boolean;
                        isRRWeightsEnabled: boolean;
                        maxLeadThreshold: number | null;
                        includeNoShowInRRCalculation: boolean;
                        allowReschedulingPastBookings: boolean;
                        hideOrganizerEmail: boolean;
                        maxActiveBookingsPerBooker: number | null;
                        maxActiveBookingPerBookerOfferReschedule: boolean;
                        customReplyToEmail: string | null;
                        eventTypeColor: import(".prisma/client").Prisma.JsonValue;
                        rescheduleWithSameRoundRobinHost: boolean;
                        secondaryEmailId: number | null;
                        useBookerTimezone: boolean;
                        restrictionScheduleId: number | null;
                        bookingRequiresAuthentication: boolean;
                    }[];
                    id: number;
                    length: number;
                    title: string;
                    description: string | null;
                    userId: number | null;
                    team: {
                        id: number;
                        members: {
                            user: {
                                timeZone: string;
                            };
                        }[];
                    } | null;
                    hashedLink: {
                        link: string;
                        id: number;
                        expiresAt: Date | null;
                        maxUsageCount: number;
                        usageCount: number;
                    }[];
                    timeZone: string | null;
                    hosts: ({
                        user: {
                            name: string | null;
                            id: number;
                            username: string | null;
                            avatarUrl: string | null;
                            timeZone: string;
                        };
                    } & {
                        userId: number;
                        eventTypeId: number;
                        createdAt: Date;
                        scheduleId: number | null;
                        weight: number | null;
                        isFixed: boolean;
                        priority: number | null;
                        weightAdjustment: number | null;
                        groupId: string | null;
                        memberId: number | null;
                    })[];
                    slug: string;
                    parentId: number | null;
                    bookingLimits: import(".prisma/client").Prisma.JsonValue;
                    teamId: number | null;
                    hidden: boolean;
                    interfaceLanguage: string | null;
                    position: number;
                    locations: import(".prisma/client").Prisma.JsonValue;
                    offsetStart: number;
                    profileId: number | null;
                    useEventLevelSelectedCalendars: boolean;
                    eventName: string | null;
                    bookingFields: import(".prisma/client").Prisma.JsonValue;
                    periodType: import(".prisma/client").$Enums.PeriodType;
                    periodStartDate: Date | null;
                    periodEndDate: Date | null;
                    periodDays: number | null;
                    periodCountCalendarDays: boolean | null;
                    lockTimeZoneToggleOnBookingPage: boolean;
                    lockedTimeZone: string | null;
                    requiresConfirmation: boolean;
                    requiresConfirmationWillBlockSlot: boolean;
                    requiresConfirmationForFreeEmail: boolean;
                    requiresBookerEmailVerification: boolean;
                    canSendCalVideoTranscriptionEmails: boolean;
                    autoTranslateDescriptionEnabled: boolean;
                    recurringEvent: import(".prisma/client").Prisma.JsonValue;
                    disableGuests: boolean;
                    hideCalendarNotes: boolean;
                    hideCalendarEventDetails: boolean;
                    minimumBookingNotice: number;
                    beforeEventBuffer: number;
                    afterEventBuffer: number;
                    seatsPerTimeSlot: number | null;
                    onlyShowFirstAvailableSlot: boolean;
                    disableCancelling: boolean | null;
                    disableRescheduling: boolean | null;
                    seatsShowAttendees: boolean | null;
                    seatsShowAvailabilityCount: boolean | null;
                    schedulingType: import(".prisma/client").$Enums.SchedulingType | null;
                    scheduleId: number | null;
                    allowReschedulingCancelledBookings: boolean | null;
                    price: number;
                    currency: string;
                    slotInterval: number | null;
                    successRedirectUrl: string | null;
                    durationLimits: import(".prisma/client").Prisma.JsonValue;
                    isInstantEvent: boolean;
                    instantMeetingExpiryTimeOffsetInSeconds: number;
                    instantMeetingScheduleId: number | null;
                    instantMeetingParameters: string[];
                    assignAllTeamMembers: boolean;
                    assignRRMembersUsingSegment: boolean;
                    rrSegmentQueryValue: import(".prisma/client").Prisma.JsonValue;
                    useEventTypeDestinationCalendarEmail: boolean;
                    isRRWeightsEnabled: boolean;
                    maxLeadThreshold: number | null;
                    allowReschedulingPastBookings: boolean;
                    hideOrganizerEmail: boolean;
                    customReplyToEmail: string | null;
                    eventTypeColor: import(".prisma/client").Prisma.JsonValue;
                    rescheduleWithSameRoundRobinHost: boolean;
                    secondaryEmailId: number | null;
                    useBookerTimezone: boolean;
                    restrictionScheduleId: number | null;
                    owner: {
                        timeZone: string;
                    } | null;
                    instantMeetingSchedule: {
                        name: string;
                        id: number;
                    } | null;
                    aiPhoneCallConfig: {
                        id: number;
                        eventTypeId: number;
                        enabled: boolean;
                        templateType: string;
                        schedulerName: string | null;
                        generalPrompt: string | null;
                        yourPhoneNumber: string;
                        numberToCall: string;
                        guestName: string | null;
                        guestEmail: string | null;
                        guestCompany: string | null;
                        beginMessage: string | null;
                        llmId: string | null;
                    } | null;
                }[];
                teamId?: number | null;
                parentId?: number | null;
                bookerUrl: string;
                membershipRole?: import("@calcom/prisma/enums").MembershipRole | null;
                profile: {
                    slug: (string | null) | null;
                    name: string | null;
                    image: string;
                    eventTypesLockedByOrg?: boolean;
                };
                metadata: {
                    membershipCount: number;
                    readOnly: boolean;
                };
            }[];
            profiles: {
                teamId: number | null | undefined;
                membershipRole: import("@calcom/prisma/enums").MembershipRole | null | undefined;
                membershipCount: number;
                readOnly: boolean;
                slug: (string | null) | null;
                name: string | null;
                image: string;
                eventTypesLockedByOrg?: boolean;
            }[];
        };
    }>;
    getUserEventGroups: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            filters?: {
                teamIds?: number[] | undefined;
                upIds?: string[] | undefined;
                schedulingTypes?: ("ROUND_ROBIN" | "COLLECTIVE" | "MANAGED")[] | undefined;
            } | undefined;
            forRoutingForms?: boolean | undefined;
        } | null | undefined;
        output: {
            eventTypeGroups: {
                teamId?: number | null;
                parentId?: number | null;
                bookerUrl: string;
                membershipRole?: import("@calcom/prisma/enums").MembershipRole | null;
                profile: {
                    slug: (string | null) | null;
                    name: string | null;
                    image: string;
                    eventTypesLockedByOrg?: boolean;
                };
                metadata: {
                    membershipCount: number;
                    readOnly: boolean;
                };
            }[];
            profiles: {
                teamId: number | null | undefined;
                membershipRole: import("@calcom/prisma/enums").MembershipRole | null | undefined;
                membershipCount: number;
                readOnly: boolean;
                slug: (string | null) | null;
                name: string | null;
                image: string;
                eventTypesLockedByOrg?: boolean;
            }[];
        };
    }>;
    getEventTypesFromGroup: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            group: {
                teamId?: number | null | undefined;
                parentId?: number | null | undefined;
            };
            filters?: {
                teamIds?: number[] | undefined;
                upIds?: string[] | undefined;
                schedulingTypes?: ("ROUND_ROBIN" | "COLLECTIVE" | "MANAGED")[] | undefined;
            } | undefined;
            forRoutingForms?: boolean | undefined;
            cursor?: number | null | undefined;
            limit?: number | undefined;
            searchQuery?: string | undefined;
        };
        output: {
            eventTypes: {
                safeDescription: string | undefined;
                users: ({
                    name: string | null;
                    id: number;
                    username: string | null;
                    avatarUrl: string | null;
                    timeZone: string;
                } & {
                    nonProfileUsername: string | null;
                    profile: import("@calcom/types/UserProfile").UserProfile;
                })[];
                metadata: {
                    config?: {
                        useHostSchedulesForTeamEvent?: boolean | undefined;
                    } | undefined;
                    smartContractAddress?: string | undefined;
                    blockchainId?: number | undefined;
                    multipleDuration?: number[] | undefined;
                    giphyThankYouPage?: string | undefined;
                    additionalNotesRequired?: boolean | undefined;
                    disableSuccessPage?: boolean | undefined;
                    disableStandardEmails?: {
                        all?: {
                            host?: boolean | undefined;
                            attendee?: boolean | undefined;
                        } | undefined;
                        confirmation?: {
                            host?: boolean | undefined;
                            attendee?: boolean | undefined;
                        } | undefined;
                    } | undefined;
                    managedEventConfig?: {
                        unlockedFields?: {
                            users?: true | undefined;
                            children?: true | undefined;
                            length?: true | undefined;
                            title?: true | undefined;
                            metadata?: true | undefined;
                            description?: true | undefined;
                            userId?: true | undefined;
                            calVideoSettings?: true | undefined;
                            destinationCalendar?: true | undefined;
                            profile?: true | undefined;
                            team?: true | undefined;
                            schedule?: true | undefined;
                            availability?: true | undefined;
                            hashedLink?: true | undefined;
                            secondaryEmail?: true | undefined;
                            customInputs?: true | undefined;
                            timeZone?: true | undefined;
                            bookings?: true | undefined;
                            selectedCalendars?: true | undefined;
                            webhooks?: true | undefined;
                            workflows?: true | undefined;
                            hosts?: true | undefined;
                            slug?: true | undefined;
                            parentId?: true | undefined;
                            bookingLimits?: true | undefined;
                            parent?: true | undefined;
                            teamId?: true | undefined;
                            hidden?: true | undefined;
                            _count?: true | undefined;
                            interfaceLanguage?: true | undefined;
                            position?: true | undefined;
                            locations?: true | undefined;
                            offsetStart?: true | undefined;
                            profileId?: true | undefined;
                            useEventLevelSelectedCalendars?: true | undefined;
                            eventName?: true | undefined;
                            bookingFields?: true | undefined;
                            periodType?: true | undefined;
                            periodStartDate?: true | undefined;
                            periodEndDate?: true | undefined;
                            periodDays?: true | undefined;
                            periodCountCalendarDays?: true | undefined;
                            lockTimeZoneToggleOnBookingPage?: true | undefined;
                            lockedTimeZone?: true | undefined;
                            requiresConfirmation?: true | undefined;
                            requiresConfirmationWillBlockSlot?: true | undefined;
                            requiresConfirmationForFreeEmail?: true | undefined;
                            requiresBookerEmailVerification?: true | undefined;
                            canSendCalVideoTranscriptionEmails?: true | undefined;
                            autoTranslateDescriptionEnabled?: true | undefined;
                            recurringEvent?: true | undefined;
                            disableGuests?: true | undefined;
                            hideCalendarNotes?: true | undefined;
                            hideCalendarEventDetails?: true | undefined;
                            minimumBookingNotice?: true | undefined;
                            beforeEventBuffer?: true | undefined;
                            afterEventBuffer?: true | undefined;
                            seatsPerTimeSlot?: true | undefined;
                            onlyShowFirstAvailableSlot?: true | undefined;
                            disableCancelling?: true | undefined;
                            disableRescheduling?: true | undefined;
                            seatsShowAttendees?: true | undefined;
                            seatsShowAvailabilityCount?: true | undefined;
                            schedulingType?: true | undefined;
                            scheduleId?: true | undefined;
                            allowReschedulingCancelledBookings?: true | undefined;
                            price?: true | undefined;
                            currency?: true | undefined;
                            slotInterval?: true | undefined;
                            successRedirectUrl?: true | undefined;
                            forwardParamsSuccessRedirect?: true | undefined;
                            durationLimits?: true | undefined;
                            isInstantEvent?: true | undefined;
                            instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                            instantMeetingScheduleId?: true | undefined;
                            instantMeetingParameters?: true | undefined;
                            assignAllTeamMembers?: true | undefined;
                            assignRRMembersUsingSegment?: true | undefined;
                            rrSegmentQueryValue?: true | undefined;
                            useEventTypeDestinationCalendarEmail?: true | undefined;
                            isRRWeightsEnabled?: true | undefined;
                            maxLeadThreshold?: true | undefined;
                            includeNoShowInRRCalculation?: true | undefined;
                            allowReschedulingPastBookings?: true | undefined;
                            hideOrganizerEmail?: true | undefined;
                            maxActiveBookingsPerBooker?: true | undefined;
                            maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                            customReplyToEmail?: true | undefined;
                            eventTypeColor?: true | undefined;
                            rescheduleWithSameRoundRobinHost?: true | undefined;
                            secondaryEmailId?: true | undefined;
                            useBookerTimezone?: true | undefined;
                            restrictionScheduleId?: true | undefined;
                            bookingRequiresAuthentication?: true | undefined;
                            owner?: true | undefined;
                            instantMeetingSchedule?: true | undefined;
                            aiPhoneCallConfig?: true | undefined;
                            fieldTranslations?: true | undefined;
                            restrictionSchedule?: true | undefined;
                            hostGroups?: true | undefined;
                        } | undefined;
                    } | undefined;
                    requiresConfirmationThreshold?: {
                        time: number;
                        unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
                    } | undefined;
                    bookerLayouts?: {
                        enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
                        defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
                    } | null | undefined;
                    apps?: unknown;
                } | null;
                children: {
                    users: ({
                        name: string | null;
                        id: number;
                        username: string | null;
                        avatarUrl: string | null;
                        timeZone: string;
                    } & {
                        nonProfileUsername: string | null;
                        profile: import("@calcom/types/UserProfile").UserProfile;
                    })[];
                    id: number;
                    length: number;
                    title: string;
                    metadata: import(".prisma/client").Prisma.JsonValue;
                    description: string | null;
                    userId: number | null;
                    timeZone: string | null;
                    slug: string;
                    parentId: number | null;
                    bookingLimits: import(".prisma/client").Prisma.JsonValue;
                    teamId: number | null;
                    hidden: boolean;
                    interfaceLanguage: string | null;
                    position: number;
                    locations: import(".prisma/client").Prisma.JsonValue;
                    offsetStart: number;
                    profileId: number | null;
                    useEventLevelSelectedCalendars: boolean;
                    eventName: string | null;
                    bookingFields: import(".prisma/client").Prisma.JsonValue;
                    periodType: import(".prisma/client").$Enums.PeriodType;
                    periodStartDate: Date | null;
                    periodEndDate: Date | null;
                    periodDays: number | null;
                    periodCountCalendarDays: boolean | null;
                    lockTimeZoneToggleOnBookingPage: boolean;
                    lockedTimeZone: string | null;
                    requiresConfirmation: boolean;
                    requiresConfirmationWillBlockSlot: boolean;
                    requiresConfirmationForFreeEmail: boolean;
                    requiresBookerEmailVerification: boolean;
                    canSendCalVideoTranscriptionEmails: boolean;
                    autoTranslateDescriptionEnabled: boolean;
                    recurringEvent: import(".prisma/client").Prisma.JsonValue;
                    disableGuests: boolean;
                    hideCalendarNotes: boolean;
                    hideCalendarEventDetails: boolean;
                    minimumBookingNotice: number;
                    beforeEventBuffer: number;
                    afterEventBuffer: number;
                    seatsPerTimeSlot: number | null;
                    onlyShowFirstAvailableSlot: boolean;
                    disableCancelling: boolean | null;
                    disableRescheduling: boolean | null;
                    seatsShowAttendees: boolean | null;
                    seatsShowAvailabilityCount: boolean | null;
                    schedulingType: import(".prisma/client").$Enums.SchedulingType | null;
                    scheduleId: number | null;
                    allowReschedulingCancelledBookings: boolean | null;
                    price: number;
                    currency: string;
                    slotInterval: number | null;
                    successRedirectUrl: string | null;
                    forwardParamsSuccessRedirect: boolean | null;
                    durationLimits: import(".prisma/client").Prisma.JsonValue;
                    isInstantEvent: boolean;
                    instantMeetingExpiryTimeOffsetInSeconds: number;
                    instantMeetingScheduleId: number | null;
                    instantMeetingParameters: string[];
                    assignAllTeamMembers: boolean;
                    assignRRMembersUsingSegment: boolean;
                    rrSegmentQueryValue: import(".prisma/client").Prisma.JsonValue;
                    useEventTypeDestinationCalendarEmail: boolean;
                    isRRWeightsEnabled: boolean;
                    maxLeadThreshold: number | null;
                    includeNoShowInRRCalculation: boolean;
                    allowReschedulingPastBookings: boolean;
                    hideOrganizerEmail: boolean;
                    maxActiveBookingsPerBooker: number | null;
                    maxActiveBookingPerBookerOfferReschedule: boolean;
                    customReplyToEmail: string | null;
                    eventTypeColor: import(".prisma/client").Prisma.JsonValue;
                    rescheduleWithSameRoundRobinHost: boolean;
                    secondaryEmailId: number | null;
                    useBookerTimezone: boolean;
                    restrictionScheduleId: number | null;
                    bookingRequiresAuthentication: boolean;
                }[];
                id: number;
                length: number;
                title: string;
                description: string | null;
                userId: number | null;
                team: {
                    id: number;
                    members: {
                        user: {
                            timeZone: string;
                        };
                    }[];
                } | null;
                hashedLink: {
                    link: string;
                    id: number;
                    expiresAt: Date | null;
                    maxUsageCount: number;
                    usageCount: number;
                }[];
                timeZone: string | null;
                hosts: ({
                    user: {
                        name: string | null;
                        id: number;
                        username: string | null;
                        avatarUrl: string | null;
                        timeZone: string;
                    };
                } & {
                    userId: number;
                    eventTypeId: number;
                    createdAt: Date;
                    scheduleId: number | null;
                    weight: number | null;
                    isFixed: boolean;
                    priority: number | null;
                    weightAdjustment: number | null;
                    groupId: string | null;
                    memberId: number | null;
                })[];
                slug: string;
                parentId: number | null;
                bookingLimits: import(".prisma/client").Prisma.JsonValue;
                teamId: number | null;
                hidden: boolean;
                interfaceLanguage: string | null;
                position: number;
                locations: import(".prisma/client").Prisma.JsonValue;
                offsetStart: number;
                profileId: number | null;
                useEventLevelSelectedCalendars: boolean;
                eventName: string | null;
                bookingFields: import(".prisma/client").Prisma.JsonValue;
                periodType: import(".prisma/client").$Enums.PeriodType;
                periodStartDate: Date | null;
                periodEndDate: Date | null;
                periodDays: number | null;
                periodCountCalendarDays: boolean | null;
                lockTimeZoneToggleOnBookingPage: boolean;
                lockedTimeZone: string | null;
                requiresConfirmation: boolean;
                requiresConfirmationWillBlockSlot: boolean;
                requiresConfirmationForFreeEmail: boolean;
                requiresBookerEmailVerification: boolean;
                canSendCalVideoTranscriptionEmails: boolean;
                autoTranslateDescriptionEnabled: boolean;
                recurringEvent: import(".prisma/client").Prisma.JsonValue;
                disableGuests: boolean;
                hideCalendarNotes: boolean;
                hideCalendarEventDetails: boolean;
                minimumBookingNotice: number;
                beforeEventBuffer: number;
                afterEventBuffer: number;
                seatsPerTimeSlot: number | null;
                onlyShowFirstAvailableSlot: boolean;
                disableCancelling: boolean | null;
                disableRescheduling: boolean | null;
                seatsShowAttendees: boolean | null;
                seatsShowAvailabilityCount: boolean | null;
                schedulingType: import(".prisma/client").$Enums.SchedulingType | null;
                scheduleId: number | null;
                allowReschedulingCancelledBookings: boolean | null;
                price: number;
                currency: string;
                slotInterval: number | null;
                successRedirectUrl: string | null;
                durationLimits: import(".prisma/client").Prisma.JsonValue;
                isInstantEvent: boolean;
                instantMeetingExpiryTimeOffsetInSeconds: number;
                instantMeetingScheduleId: number | null;
                instantMeetingParameters: string[];
                assignAllTeamMembers: boolean;
                assignRRMembersUsingSegment: boolean;
                rrSegmentQueryValue: import(".prisma/client").Prisma.JsonValue;
                useEventTypeDestinationCalendarEmail: boolean;
                isRRWeightsEnabled: boolean;
                maxLeadThreshold: number | null;
                allowReschedulingPastBookings: boolean;
                hideOrganizerEmail: boolean;
                customReplyToEmail: string | null;
                eventTypeColor: import(".prisma/client").Prisma.JsonValue;
                rescheduleWithSameRoundRobinHost: boolean;
                secondaryEmailId: number | null;
                useBookerTimezone: boolean;
                restrictionScheduleId: number | null;
                owner: {
                    timeZone: string;
                } | null;
                instantMeetingSchedule: {
                    name: string;
                    id: number;
                } | null;
                aiPhoneCallConfig: {
                    id: number;
                    eventTypeId: number;
                    enabled: boolean;
                    templateType: string;
                    schedulerName: string | null;
                    generalPrompt: string | null;
                    yourPhoneNumber: string;
                    numberToCall: string;
                    guestName: string | null;
                    guestEmail: string | null;
                    guestCompany: string | null;
                    beginMessage: string | null;
                    llmId: string | null;
                } | null;
            }[];
            nextCursor: number | null | undefined;
        };
    }>;
    getTeamAndEventTypeOptions: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            teamId?: number | undefined;
            isOrg?: boolean | undefined;
        } | null | undefined;
        output: {
            eventTypeOptions: {
                value: string;
                label: string;
            }[];
            teamOptions: {
                value: string;
                label: string;
            }[];
        };
    }>;
    list: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: {
            id: number;
            length: number;
            title: string;
            metadata: import(".prisma/client").Prisma.JsonValue;
            description: string | null;
            slug: string;
            hidden: boolean;
            schedulingType: import(".prisma/client").$Enums.SchedulingType | null;
        }[];
    }>;
    listWithTeam: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: {
            id: number;
            team: {
                id: number;
                name: string;
            } | null;
            title: string;
            slug: string;
        }[];
    }>;
    create: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            length: number;
            title: string;
            slug: string;
            metadata?: {
                config?: {
                    useHostSchedulesForTeamEvent?: boolean | undefined;
                } | undefined;
                smartContractAddress?: string | undefined;
                blockchainId?: number | undefined;
                multipleDuration?: number[] | undefined;
                giphyThankYouPage?: string | undefined;
                additionalNotesRequired?: boolean | undefined;
                disableSuccessPage?: boolean | undefined;
                disableStandardEmails?: {
                    all?: {
                        host?: boolean | undefined;
                        attendee?: boolean | undefined;
                    } | undefined;
                    confirmation?: {
                        host?: boolean | undefined;
                        attendee?: boolean | undefined;
                    } | undefined;
                } | undefined;
                managedEventConfig?: {
                    unlockedFields?: {
                        users?: true | undefined;
                        children?: true | undefined;
                        length?: true | undefined;
                        title?: true | undefined;
                        metadata?: true | undefined;
                        description?: true | undefined;
                        userId?: true | undefined;
                        calVideoSettings?: true | undefined;
                        destinationCalendar?: true | undefined;
                        profile?: true | undefined;
                        team?: true | undefined;
                        schedule?: true | undefined;
                        availability?: true | undefined;
                        hashedLink?: true | undefined;
                        secondaryEmail?: true | undefined;
                        customInputs?: true | undefined;
                        timeZone?: true | undefined;
                        bookings?: true | undefined;
                        selectedCalendars?: true | undefined;
                        webhooks?: true | undefined;
                        workflows?: true | undefined;
                        hosts?: true | undefined;
                        slug?: true | undefined;
                        parentId?: true | undefined;
                        bookingLimits?: true | undefined;
                        parent?: true | undefined;
                        teamId?: true | undefined;
                        hidden?: true | undefined;
                        _count?: true | undefined;
                        interfaceLanguage?: true | undefined;
                        position?: true | undefined;
                        locations?: true | undefined;
                        offsetStart?: true | undefined;
                        profileId?: true | undefined;
                        useEventLevelSelectedCalendars?: true | undefined;
                        eventName?: true | undefined;
                        bookingFields?: true | undefined;
                        periodType?: true | undefined;
                        periodStartDate?: true | undefined;
                        periodEndDate?: true | undefined;
                        periodDays?: true | undefined;
                        periodCountCalendarDays?: true | undefined;
                        lockTimeZoneToggleOnBookingPage?: true | undefined;
                        lockedTimeZone?: true | undefined;
                        requiresConfirmation?: true | undefined;
                        requiresConfirmationWillBlockSlot?: true | undefined;
                        requiresConfirmationForFreeEmail?: true | undefined;
                        requiresBookerEmailVerification?: true | undefined;
                        canSendCalVideoTranscriptionEmails?: true | undefined;
                        autoTranslateDescriptionEnabled?: true | undefined;
                        recurringEvent?: true | undefined;
                        disableGuests?: true | undefined;
                        hideCalendarNotes?: true | undefined;
                        hideCalendarEventDetails?: true | undefined;
                        minimumBookingNotice?: true | undefined;
                        beforeEventBuffer?: true | undefined;
                        afterEventBuffer?: true | undefined;
                        seatsPerTimeSlot?: true | undefined;
                        onlyShowFirstAvailableSlot?: true | undefined;
                        disableCancelling?: true | undefined;
                        disableRescheduling?: true | undefined;
                        seatsShowAttendees?: true | undefined;
                        seatsShowAvailabilityCount?: true | undefined;
                        schedulingType?: true | undefined;
                        scheduleId?: true | undefined;
                        allowReschedulingCancelledBookings?: true | undefined;
                        price?: true | undefined;
                        currency?: true | undefined;
                        slotInterval?: true | undefined;
                        successRedirectUrl?: true | undefined;
                        forwardParamsSuccessRedirect?: true | undefined;
                        durationLimits?: true | undefined;
                        isInstantEvent?: true | undefined;
                        instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                        instantMeetingScheduleId?: true | undefined;
                        instantMeetingParameters?: true | undefined;
                        assignAllTeamMembers?: true | undefined;
                        assignRRMembersUsingSegment?: true | undefined;
                        rrSegmentQueryValue?: true | undefined;
                        useEventTypeDestinationCalendarEmail?: true | undefined;
                        isRRWeightsEnabled?: true | undefined;
                        maxLeadThreshold?: true | undefined;
                        includeNoShowInRRCalculation?: true | undefined;
                        allowReschedulingPastBookings?: true | undefined;
                        hideOrganizerEmail?: true | undefined;
                        maxActiveBookingsPerBooker?: true | undefined;
                        maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                        customReplyToEmail?: true | undefined;
                        eventTypeColor?: true | undefined;
                        rescheduleWithSameRoundRobinHost?: true | undefined;
                        secondaryEmailId?: true | undefined;
                        useBookerTimezone?: true | undefined;
                        restrictionScheduleId?: true | undefined;
                        bookingRequiresAuthentication?: true | undefined;
                        owner?: true | undefined;
                        instantMeetingSchedule?: true | undefined;
                        aiPhoneCallConfig?: true | undefined;
                        fieldTranslations?: true | undefined;
                        restrictionSchedule?: true | undefined;
                        hostGroups?: true | undefined;
                    } | undefined;
                } | undefined;
                requiresConfirmationThreshold?: {
                    time: number;
                    unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
                } | undefined;
                bookerLayouts?: {
                    enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
                    defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
                } | null | undefined;
                apps?: unknown;
            } | null | undefined;
            description?: string | null | undefined;
            calVideoSettings?: {
                disableRecordingForGuests?: boolean | null | undefined;
                disableRecordingForOrganizer?: boolean | null | undefined;
                redirectUrlOnExit?: string | null | undefined;
                enableAutomaticRecordingForOrganizer?: boolean | null | undefined;
                enableAutomaticTranscription?: boolean | null | undefined;
                disableTranscriptionForGuests?: boolean | null | undefined;
                disableTranscriptionForOrganizer?: boolean | null | undefined;
            } | null | undefined;
            teamId?: number | null | undefined;
            hidden?: boolean | undefined;
            locations?: {
                type: string;
                address?: string | undefined;
                link?: string | undefined;
                displayLocationPublicly?: boolean | undefined;
                hostPhoneNumber?: string | undefined;
                credentialId?: number | undefined;
                teamName?: string | undefined;
                customLabel?: string | undefined;
            }[] | undefined;
            disableGuests?: boolean | undefined;
            minimumBookingNotice?: number | undefined;
            beforeEventBuffer?: number | undefined;
            afterEventBuffer?: number | undefined;
            schedulingType?: "ROUND_ROBIN" | "COLLECTIVE" | "MANAGED" | null | undefined;
            scheduleId?: number | undefined;
            slotInterval?: number | null | undefined;
        };
        output: {
            eventType: {
                calVideoSettings: {
                    eventTypeId: number;
                    createdAt: Date;
                    updatedAt: Date;
                    disableRecordingForOrganizer: boolean;
                    disableRecordingForGuests: boolean;
                    enableAutomaticTranscription: boolean;
                    enableAutomaticRecordingForOrganizer: boolean;
                    redirectUrlOnExit: string | null;
                    disableTranscriptionForGuests: boolean;
                    disableTranscriptionForOrganizer: boolean;
                } | null;
            } & {
                id: number;
                length: number;
                title: string;
                metadata: import(".prisma/client").Prisma.JsonValue;
                description: string | null;
                userId: number | null;
                timeZone: string | null;
                slug: string;
                parentId: number | null;
                bookingLimits: import(".prisma/client").Prisma.JsonValue;
                teamId: number | null;
                hidden: boolean;
                interfaceLanguage: string | null;
                position: number;
                locations: import(".prisma/client").Prisma.JsonValue;
                offsetStart: number;
                profileId: number | null;
                useEventLevelSelectedCalendars: boolean;
                eventName: string | null;
                bookingFields: import(".prisma/client").Prisma.JsonValue;
                periodType: import(".prisma/client").$Enums.PeriodType;
                periodStartDate: Date | null;
                periodEndDate: Date | null;
                periodDays: number | null;
                periodCountCalendarDays: boolean | null;
                lockTimeZoneToggleOnBookingPage: boolean;
                lockedTimeZone: string | null;
                requiresConfirmation: boolean;
                requiresConfirmationWillBlockSlot: boolean;
                requiresConfirmationForFreeEmail: boolean;
                requiresBookerEmailVerification: boolean;
                canSendCalVideoTranscriptionEmails: boolean;
                autoTranslateDescriptionEnabled: boolean;
                recurringEvent: import(".prisma/client").Prisma.JsonValue;
                disableGuests: boolean;
                hideCalendarNotes: boolean;
                hideCalendarEventDetails: boolean;
                minimumBookingNotice: number;
                beforeEventBuffer: number;
                afterEventBuffer: number;
                seatsPerTimeSlot: number | null;
                onlyShowFirstAvailableSlot: boolean;
                disableCancelling: boolean | null;
                disableRescheduling: boolean | null;
                seatsShowAttendees: boolean | null;
                seatsShowAvailabilityCount: boolean | null;
                schedulingType: import(".prisma/client").$Enums.SchedulingType | null;
                scheduleId: number | null;
                allowReschedulingCancelledBookings: boolean | null;
                price: number;
                currency: string;
                slotInterval: number | null;
                successRedirectUrl: string | null;
                forwardParamsSuccessRedirect: boolean | null;
                durationLimits: import(".prisma/client").Prisma.JsonValue;
                isInstantEvent: boolean;
                instantMeetingExpiryTimeOffsetInSeconds: number;
                instantMeetingScheduleId: number | null;
                instantMeetingParameters: string[];
                assignAllTeamMembers: boolean;
                assignRRMembersUsingSegment: boolean;
                rrSegmentQueryValue: import(".prisma/client").Prisma.JsonValue;
                useEventTypeDestinationCalendarEmail: boolean;
                isRRWeightsEnabled: boolean;
                maxLeadThreshold: number | null;
                includeNoShowInRRCalculation: boolean;
                allowReschedulingPastBookings: boolean;
                hideOrganizerEmail: boolean;
                maxActiveBookingsPerBooker: number | null;
                maxActiveBookingPerBookerOfferReschedule: boolean;
                customReplyToEmail: string | null;
                eventTypeColor: import(".prisma/client").Prisma.JsonValue;
                rescheduleWithSameRoundRobinHost: boolean;
                secondaryEmailId: number | null;
                useBookerTimezone: boolean;
                restrictionScheduleId: number | null;
                bookingRequiresAuthentication: boolean;
            };
        };
    }>;
    get: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            id: number;
            eventTypeId?: number | undefined;
            users?: number[] | undefined;
        };
        output: {
            eventType: {
                schedule: number | null;
                restrictionScheduleId: number | null;
                restrictionScheduleName: string | null;
                useBookerTimezone: boolean;
                instantMeetingSchedule: number | null;
                scheduleName: string | null;
                recurringEvent: import("@calcom/types/Calendar").RecurringEvent | null;
                bookingLimits: Partial<Record<"PER_DAY" | "PER_MONTH" | "PER_WEEK" | "PER_YEAR", number | undefined>> | null;
                durationLimits: Partial<Record<"PER_DAY" | "PER_MONTH" | "PER_WEEK" | "PER_YEAR", number | undefined>> | null;
                eventTypeColor: {
                    lightEventTypeColor: string;
                    darkEventTypeColor: string;
                } | null;
                locations: import("@calcom/app-store/locations").LocationObject[];
                metadata: {
                    config?: {
                        useHostSchedulesForTeamEvent?: boolean | undefined;
                    } | undefined;
                    smartContractAddress?: string | undefined;
                    blockchainId?: number | undefined;
                    multipleDuration?: number[] | undefined;
                    giphyThankYouPage?: string | undefined;
                    additionalNotesRequired?: boolean | undefined;
                    disableSuccessPage?: boolean | undefined;
                    disableStandardEmails?: {
                        all?: {
                            host?: boolean | undefined;
                            attendee?: boolean | undefined;
                        } | undefined;
                        confirmation?: {
                            host?: boolean | undefined;
                            attendee?: boolean | undefined;
                        } | undefined;
                    } | undefined;
                    managedEventConfig?: {
                        unlockedFields?: {
                            users?: true | undefined;
                            children?: true | undefined;
                            length?: true | undefined;
                            title?: true | undefined;
                            metadata?: true | undefined;
                            description?: true | undefined;
                            userId?: true | undefined;
                            calVideoSettings?: true | undefined;
                            destinationCalendar?: true | undefined;
                            profile?: true | undefined;
                            team?: true | undefined;
                            schedule?: true | undefined;
                            availability?: true | undefined;
                            hashedLink?: true | undefined;
                            secondaryEmail?: true | undefined;
                            customInputs?: true | undefined;
                            timeZone?: true | undefined;
                            bookings?: true | undefined;
                            selectedCalendars?: true | undefined;
                            webhooks?: true | undefined;
                            workflows?: true | undefined;
                            hosts?: true | undefined;
                            slug?: true | undefined;
                            parentId?: true | undefined;
                            bookingLimits?: true | undefined;
                            parent?: true | undefined;
                            teamId?: true | undefined;
                            hidden?: true | undefined;
                            _count?: true | undefined;
                            interfaceLanguage?: true | undefined;
                            position?: true | undefined;
                            locations?: true | undefined;
                            offsetStart?: true | undefined;
                            profileId?: true | undefined;
                            useEventLevelSelectedCalendars?: true | undefined;
                            eventName?: true | undefined;
                            bookingFields?: true | undefined;
                            periodType?: true | undefined;
                            periodStartDate?: true | undefined;
                            periodEndDate?: true | undefined;
                            periodDays?: true | undefined;
                            periodCountCalendarDays?: true | undefined;
                            lockTimeZoneToggleOnBookingPage?: true | undefined;
                            lockedTimeZone?: true | undefined;
                            requiresConfirmation?: true | undefined;
                            requiresConfirmationWillBlockSlot?: true | undefined;
                            requiresConfirmationForFreeEmail?: true | undefined;
                            requiresBookerEmailVerification?: true | undefined;
                            canSendCalVideoTranscriptionEmails?: true | undefined;
                            autoTranslateDescriptionEnabled?: true | undefined;
                            recurringEvent?: true | undefined;
                            disableGuests?: true | undefined;
                            hideCalendarNotes?: true | undefined;
                            hideCalendarEventDetails?: true | undefined;
                            minimumBookingNotice?: true | undefined;
                            beforeEventBuffer?: true | undefined;
                            afterEventBuffer?: true | undefined;
                            seatsPerTimeSlot?: true | undefined;
                            onlyShowFirstAvailableSlot?: true | undefined;
                            disableCancelling?: true | undefined;
                            disableRescheduling?: true | undefined;
                            seatsShowAttendees?: true | undefined;
                            seatsShowAvailabilityCount?: true | undefined;
                            schedulingType?: true | undefined;
                            scheduleId?: true | undefined;
                            allowReschedulingCancelledBookings?: true | undefined;
                            price?: true | undefined;
                            currency?: true | undefined;
                            slotInterval?: true | undefined;
                            successRedirectUrl?: true | undefined;
                            forwardParamsSuccessRedirect?: true | undefined;
                            durationLimits?: true | undefined;
                            isInstantEvent?: true | undefined;
                            instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                            instantMeetingScheduleId?: true | undefined;
                            instantMeetingParameters?: true | undefined;
                            assignAllTeamMembers?: true | undefined;
                            assignRRMembersUsingSegment?: true | undefined;
                            rrSegmentQueryValue?: true | undefined;
                            useEventTypeDestinationCalendarEmail?: true | undefined;
                            isRRWeightsEnabled?: true | undefined;
                            maxLeadThreshold?: true | undefined;
                            includeNoShowInRRCalculation?: true | undefined;
                            allowReschedulingPastBookings?: true | undefined;
                            hideOrganizerEmail?: true | undefined;
                            maxActiveBookingsPerBooker?: true | undefined;
                            maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                            customReplyToEmail?: true | undefined;
                            eventTypeColor?: true | undefined;
                            rescheduleWithSameRoundRobinHost?: true | undefined;
                            secondaryEmailId?: true | undefined;
                            useBookerTimezone?: true | undefined;
                            restrictionScheduleId?: true | undefined;
                            bookingRequiresAuthentication?: true | undefined;
                            owner?: true | undefined;
                            instantMeetingSchedule?: true | undefined;
                            aiPhoneCallConfig?: true | undefined;
                            fieldTranslations?: true | undefined;
                            restrictionSchedule?: true | undefined;
                            hostGroups?: true | undefined;
                        } | undefined;
                    } | undefined;
                    requiresConfirmationThreshold?: {
                        time: number;
                        unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
                    } | undefined;
                    bookerLayouts?: {
                        enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
                        defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
                    } | null | undefined;
                    apps?: {
                        alby?: {
                            price: number;
                            currency: string;
                            appCategories?: string[] | undefined;
                            paymentOption?: string | undefined;
                            enabled?: boolean | undefined;
                            credentialId?: number | undefined;
                        } | undefined;
                        basecamp3?: {
                            credentialId?: number | undefined;
                            enabled?: boolean | undefined;
                            appCategories?: string[] | undefined;
                        } | undefined;
                        btcpayserver?: {
                            price: number;
                            currency: string;
                            appCategories?: string[] | undefined;
                            paymentOption?: string | undefined;
                            enabled?: boolean | undefined;
                            credentialId?: number | undefined;
                        } | undefined;
                        closecom?: {
                            enabled?: boolean | undefined;
                            credentialId?: number | undefined;
                            appCategories?: string[] | undefined;
                        } | undefined;
                        dailyvideo?: {} | undefined;
                        dub?: {} | undefined;
                        fathom?: {
                            credentialId?: number | undefined;
                            enabled?: boolean | undefined;
                            appCategories?: string[] | undefined;
                            trackingId?: string | undefined;
                        } | undefined;
                        feishucalendar?: {} | undefined;
                        ga4?: {
                            credentialId?: number | undefined;
                            enabled?: boolean | undefined;
                            appCategories?: string[] | undefined;
                            trackingId?: string | undefined;
                        } | undefined;
                        giphy?: {
                            credentialId?: number | undefined;
                            enabled?: boolean | undefined;
                            appCategories?: string[] | undefined;
                            thankYouPage?: string | undefined;
                        } | undefined;
                        googlecalendar?: {} | undefined;
                        googlevideo?: {} | undefined;
                        gtm?: {
                            trackingId: string;
                            credentialId?: number | undefined;
                            enabled?: boolean | undefined;
                            appCategories?: string[] | undefined;
                        } | undefined;
                        hitpay?: {
                            price: number;
                            currency: string;
                            credentialId?: number | undefined;
                            appCategories?: string[] | undefined;
                            paymentOption?: string | undefined;
                            enabled?: boolean | undefined;
                        } | undefined;
                        hubspot?: {
                            enabled?: boolean | undefined;
                            credentialId?: number | undefined;
                            appCategories?: string[] | undefined;
                        } | undefined;
                        insihts?: {
                            credentialId?: number | undefined;
                            enabled?: boolean | undefined;
                            appCategories?: string[] | undefined;
                            SITE_ID?: string | undefined;
                            SCRIPT_URL?: string | undefined;
                        } | undefined;
                        intercom?: {} | undefined;
                        jelly?: {} | undefined;
                        jitsivideo?: {} | undefined;
                        larkcalendar?: {} | undefined;
                        make?: {} | undefined;
                        matomo?: {
                            credentialId?: number | undefined;
                            enabled?: boolean | undefined;
                            appCategories?: string[] | undefined;
                            MATOMO_URL?: string | undefined;
                            SITE_ID?: string | undefined;
                        } | undefined;
                        metapixel?: {
                            credentialId?: number | undefined;
                            enabled?: boolean | undefined;
                            appCategories?: string[] | undefined;
                            trackingId?: string | undefined;
                        } | undefined;
                        "mock-payment-app"?: {
                            price: number;
                            currency: string;
                            credentialId?: number | undefined;
                            appCategories?: string[] | undefined;
                            paymentOption?: string | undefined;
                            enabled?: boolean | undefined;
                        } | undefined;
                        nextcloudtalk?: {} | undefined;
                        office365calendar?: {
                            client_id: string;
                            client_secret: string;
                        } | undefined;
                        office365video?: {
                            client_id: string;
                            client_secret: string;
                        } | undefined;
                        paypal?: {
                            price: number;
                            currency: string;
                            credentialId?: number | undefined;
                            appCategories?: string[] | undefined;
                            paymentOption?: string | undefined;
                            enabled?: boolean | undefined;
                        } | undefined;
                        "pipedrive-crm"?: {
                            enabled?: boolean | undefined;
                            credentialId?: number | undefined;
                            appCategories?: string[] | undefined;
                        } | undefined;
                        plausible?: {
                            credentialId?: number | undefined;
                            enabled?: boolean | undefined;
                            appCategories?: string[] | undefined;
                            PLAUSIBLE_URL?: string | undefined;
                            trackingId?: string | undefined;
                        } | undefined;
                        posthog?: {
                            credentialId?: number | undefined;
                            enabled?: boolean | undefined;
                            appCategories?: string[] | undefined;
                            TRACKING_ID?: string | undefined;
                            API_HOST?: string | undefined;
                        } | undefined;
                        qr_code?: {
                            enabled?: boolean | undefined;
                            credentialId?: number | undefined;
                            appCategories?: string[] | undefined;
                        } | undefined;
                        "routing-forms"?: any;
                        salesforce?: {
                            credentialId?: number | undefined;
                            enabled?: boolean | undefined;
                            appCategories?: string[] | undefined;
                            roundRobinLeadSkip?: boolean | undefined;
                            roundRobinSkipCheckRecordOn?: import("@calcom/app-store/salesforce/lib/enums").SalesforceRecordEnum | undefined;
                            ifFreeEmailDomainSkipOwnerCheck?: boolean | undefined;
                            roundRobinSkipFallbackToLeadOwner?: boolean | undefined;
                            skipContactCreation?: boolean | undefined;
                            createEventOn?: import("@calcom/app-store/salesforce/lib/enums").SalesforceRecordEnum | undefined;
                            createNewContactUnderAccount?: boolean | undefined;
                            createLeadIfAccountNull?: boolean | undefined;
                            onBookingWriteToEventObject?: boolean | undefined;
                            onBookingWriteToEventObjectMap?: Record<string, any> | undefined;
                            createEventOnLeadCheckForContact?: boolean | undefined;
                            onBookingChangeRecordOwner?: boolean | undefined;
                            onBookingChangeRecordOwnerName?: string | undefined;
                            sendNoShowAttendeeData?: boolean | undefined;
                            sendNoShowAttendeeDataField?: string | undefined;
                            onBookingWriteToRecord?: boolean | undefined;
                            onBookingWriteToRecordFields?: Record<string, {
                                value: string | boolean;
                                fieldType: import("@calcom/app-store/salesforce/lib/enums").SalesforceFieldType;
                                whenToWrite: import("@calcom/app-store/salesforce/lib/enums").WhenToWriteToRecord;
                            }> | undefined;
                            ignoreGuests?: boolean | undefined;
                            onCancelWriteToEventRecord?: boolean | undefined;
                            onCancelWriteToEventRecordFields?: Record<string, {
                                value: string | boolean;
                                fieldType: import("@calcom/app-store/salesforce/lib/enums").SalesforceFieldType;
                                whenToWrite: import("@calcom/app-store/salesforce/lib/enums").WhenToWriteToRecord;
                            }> | undefined;
                        } | undefined;
                        shimmervideo?: {} | undefined;
                        stripe?: {
                            price: number;
                            currency: string;
                            credentialId?: number | undefined;
                            appCategories?: string[] | undefined;
                            paymentOption?: string | undefined;
                            enabled?: boolean | undefined;
                            refundPolicy?: import("@calcom/lib/payment/types").RefundPolicy | undefined;
                            refundDaysCount?: number | undefined;
                            refundCountCalendarDays?: boolean | undefined;
                        } | undefined;
                        tandemvideo?: {} | undefined;
                        "booking-pages-tag"?: {
                            trackingId: string;
                            credentialId?: number | undefined;
                            enabled?: boolean | undefined;
                            appCategories?: string[] | undefined;
                        } | undefined;
                        "event-type-app-card"?: {
                            isSunrise: boolean;
                            credentialId?: number | undefined;
                            enabled?: boolean | undefined;
                            appCategories?: string[] | undefined;
                        } | undefined;
                        twipla?: {
                            credentialId?: number | undefined;
                            enabled?: boolean | undefined;
                            appCategories?: string[] | undefined;
                            SITE_ID?: string | undefined;
                        } | undefined;
                        umami?: {
                            credentialId?: number | undefined;
                            enabled?: boolean | undefined;
                            appCategories?: string[] | undefined;
                            SITE_ID?: string | undefined;
                            SCRIPT_URL?: string | undefined;
                        } | undefined;
                        vital?: {} | undefined;
                        webex?: {} | undefined;
                        wordpress?: {
                            isSunrise: boolean;
                            credentialId?: number | undefined;
                            enabled?: boolean | undefined;
                            appCategories?: string[] | undefined;
                        } | undefined;
                        zapier?: {} | undefined;
                        "zoho-bigin"?: {
                            enabled?: boolean | undefined;
                            credentialId?: number | undefined;
                            appCategories?: string[] | undefined;
                        } | undefined;
                        zohocalendar?: {} | undefined;
                        zohocrm?: {
                            enabled?: boolean | undefined;
                            credentialId?: number | undefined;
                            appCategories?: string[] | undefined;
                        } | undefined;
                        zoomvideo?: {} | undefined;
                    } | undefined;
                };
                customInputs: {
                    id: number;
                    type: "TEXT" | "TEXTLONG" | "NUMBER" | "BOOL" | "RADIO" | "PHONE";
                    label: string;
                    required: boolean;
                    eventTypeId: number;
                    placeholder: string;
                    options?: {
                        type: string;
                        label: string;
                    }[] | null | undefined;
                    hasToBeCreated?: boolean | undefined;
                }[];
                users: {
                    name: string | null;
                    id: number;
                    locale: string | null;
                    email: string;
                    username: string | null;
                    avatarUrl: string | null;
                    timeZone: string;
                    defaultScheduleId: number | null;
                    isPlatformManaged: boolean;
                }[];
                bookerUrl: string;
                children: {
                    owner: {
                        avatar: string;
                        email: string;
                        name: string;
                        username: string;
                        membership: import(".prisma/client").$Enums.MembershipRole;
                        id: number;
                        avatarUrl: string | null;
                        nonProfileUsername: string | null;
                        profile: import("@calcom/types/UserProfile").UserProfile;
                    };
                    created: boolean;
                    slug: string;
                    hidden: boolean;
                }[];
                id: number;
                length: number;
                title: string;
                description: string | null;
                userId: number | null;
                calVideoSettings: {
                    disableRecordingForOrganizer: boolean;
                    disableRecordingForGuests: boolean;
                    enableAutomaticTranscription: boolean;
                    enableAutomaticRecordingForOrganizer: boolean;
                    redirectUrlOnExit: string | null;
                    disableTranscriptionForGuests: boolean;
                    disableTranscriptionForOrganizer: boolean;
                } | null;
                destinationCalendar: {
                    id: number;
                    userId: number | null;
                    eventTypeId: number | null;
                    createdAt: Date | null;
                    updatedAt: Date | null;
                    integration: string;
                    externalId: string;
                    primaryEmail: string | null;
                    credentialId: number | null;
                    delegationCredentialId: string | null;
                    domainWideDelegationCredentialId: string | null;
                } | null;
                team: {
                    name: string;
                    id: number;
                    slug: string | null;
                    rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
                    parentId: number | null;
                    members: {
                        user: {
                            name: string | null;
                            id: number;
                            locale: string | null;
                            email: string;
                            username: string | null;
                            avatarUrl: string | null;
                            timeZone: string;
                            defaultScheduleId: number | null;
                            isPlatformManaged: boolean;
                            eventTypes: {
                                slug: string;
                            }[];
                        };
                        role: import(".prisma/client").$Enums.MembershipRole;
                        accepted: boolean;
                    }[];
                    parent: {
                        organizationSettings: {
                            lockEventTypeCreationForUsers: boolean;
                        } | null;
                        slug: string | null;
                    } | null;
                } | null;
                hashedLink: {
                    link: string;
                    id: number;
                    expiresAt: Date | null;
                    maxUsageCount: number;
                    usageCount: number;
                }[];
                timeZone: string | null;
                webhooks: {
                    id: string;
                    eventTypeId: number | null;
                    secret: string | null;
                    active: boolean;
                    subscriberUrl: string;
                    payloadTemplate: string | null;
                    eventTriggers: import(".prisma/client").$Enums.WebhookTriggerEvents[];
                }[];
                workflows: ({
                    workflow: {
                        name: string;
                        id: number;
                        time: number | null;
                        userId: number | null;
                        team: {
                            name: string;
                            id: number;
                            slug: string | null;
                            members: {
                                id: number;
                                role: import(".prisma/client").$Enums.MembershipRole;
                                userId: number;
                                createdAt: Date | null;
                                updatedAt: Date | null;
                                disableImpersonation: boolean;
                                teamId: number;
                                accepted: boolean;
                                customRoleId: string | null;
                            }[];
                        } | null;
                        teamId: number | null;
                        steps: {
                            id: number;
                            template: import(".prisma/client").$Enums.WorkflowTemplates;
                            action: import(".prisma/client").$Enums.WorkflowActions;
                            stepNumber: number;
                            workflowId: number;
                            sendTo: string | null;
                            reminderBody: string | null;
                            emailSubject: string | null;
                            numberRequired: boolean | null;
                            sender: string | null;
                            numberVerificationPending: boolean;
                            includeCalendarEvent: boolean;
                            verifiedAt: Date | null;
                            agentId: string | null;
                        }[];
                        trigger: import(".prisma/client").$Enums.WorkflowTriggerEvents;
                        timeUnit: import(".prisma/client").$Enums.TimeUnit | null;
                        activeOn: {
                            eventType: {
                                id: number;
                                title: string;
                                parentId: number | null;
                                _count: {
                                    children: number;
                                };
                            };
                        }[];
                    };
                } & {
                    id: number;
                    eventTypeId: number;
                    workflowId: number;
                })[];
                hosts: {
                    user: {
                        timeZone: string;
                    };
                    userId: number;
                    scheduleId: number | null;
                    weight: number | null;
                    isFixed: boolean;
                    priority: number | null;
                }[];
                slug: string;
                parent: {
                    id: number;
                    teamId: number | null;
                } | null;
                teamId: number | null;
                hidden: boolean;
                interfaceLanguage: string | null;
                offsetStart: number;
                useEventLevelSelectedCalendars: boolean;
                eventName: string | null;
                bookingFields: import(".prisma/client").Prisma.JsonValue;
                periodType: import(".prisma/client").$Enums.PeriodType;
                periodStartDate: Date | null;
                periodEndDate: Date | null;
                periodDays: number | null;
                periodCountCalendarDays: boolean | null;
                lockTimeZoneToggleOnBookingPage: boolean;
                lockedTimeZone: string | null;
                requiresConfirmation: boolean;
                requiresConfirmationWillBlockSlot: boolean;
                requiresConfirmationForFreeEmail: boolean;
                requiresBookerEmailVerification: boolean;
                canSendCalVideoTranscriptionEmails: boolean;
                autoTranslateDescriptionEnabled: boolean;
                disableGuests: boolean;
                hideCalendarNotes: boolean;
                hideCalendarEventDetails: boolean;
                minimumBookingNotice: number;
                beforeEventBuffer: number;
                afterEventBuffer: number;
                seatsPerTimeSlot: number | null;
                onlyShowFirstAvailableSlot: boolean;
                disableCancelling: boolean | null;
                disableRescheduling: boolean | null;
                seatsShowAttendees: boolean | null;
                seatsShowAvailabilityCount: boolean | null;
                schedulingType: import(".prisma/client").$Enums.SchedulingType | null;
                allowReschedulingCancelledBookings: boolean | null;
                price: number;
                currency: string;
                slotInterval: number | null;
                successRedirectUrl: string | null;
                forwardParamsSuccessRedirect: boolean | null;
                isInstantEvent: boolean;
                instantMeetingExpiryTimeOffsetInSeconds: number;
                instantMeetingParameters: string[];
                assignAllTeamMembers: boolean;
                assignRRMembersUsingSegment: boolean;
                rrSegmentQueryValue: import(".prisma/client").Prisma.JsonValue;
                useEventTypeDestinationCalendarEmail: boolean;
                isRRWeightsEnabled: boolean;
                maxLeadThreshold: number | null;
                includeNoShowInRRCalculation: boolean;
                allowReschedulingPastBookings: boolean;
                hideOrganizerEmail: boolean;
                maxActiveBookingsPerBooker: number | null;
                maxActiveBookingPerBookerOfferReschedule: boolean;
                customReplyToEmail: string | null;
                rescheduleWithSameRoundRobinHost: boolean;
                secondaryEmailId: number | null;
                owner: {
                    id: number;
                    timeZone: string;
                } | null;
                aiPhoneCallConfig: {
                    id: number;
                    eventTypeId: number;
                    enabled: boolean;
                    templateType: string;
                    schedulerName: string | null;
                    generalPrompt: string | null;
                    yourPhoneNumber: string;
                    numberToCall: string;
                    guestName: string | null;
                    guestEmail: string | null;
                    guestCompany: string | null;
                    beginMessage: string | null;
                    llmId: string | null;
                } | null;
                fieldTranslations: {
                    field: import(".prisma/client").$Enums.EventTypeAutoTranslatedField;
                    targetLocale: string;
                    translatedText: string;
                }[];
                restrictionSchedule: {
                    name: string;
                    id: number;
                } | null;
                hostGroups: {
                    name: string;
                    id: string;
                }[];
            } & {
                users: ({
                    name: string | null;
                    id: number;
                    locale: string | null;
                    email: string;
                    username: string | null;
                    avatarUrl: string | null;
                    timeZone: string;
                    defaultScheduleId: number | null;
                    isPlatformManaged: boolean;
                } & {
                    avatar: string;
                })[];
                periodStartDate: string | null;
                periodEndDate: string | null;
                bookingFields: {
                    name: string;
                    type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
                    label?: string | undefined;
                    options?: {
                        label: string;
                        value: string;
                    }[] | undefined;
                    required?: boolean | undefined;
                    placeholder?: string | undefined;
                    maxLength?: number | undefined;
                    defaultLabel?: string | undefined;
                    defaultPlaceholder?: string | undefined;
                    labelAsSafeHtml?: string | undefined;
                    getOptionsAt?: string | undefined;
                    optionsInputs?: Record<string, {
                        type: "phone" | "address" | "text";
                        required?: boolean | undefined;
                        placeholder?: string | undefined;
                    }> | undefined;
                    minLength?: number | undefined;
                    excludeEmails?: string | undefined;
                    requireEmails?: string | undefined;
                    variant?: string | undefined;
                    variantsConfig?: {
                        variants: Record<string, {
                            fields: {
                                name: string;
                                type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
                                label?: string | undefined;
                                required?: boolean | undefined;
                                placeholder?: string | undefined;
                                maxLength?: number | undefined;
                                labelAsSafeHtml?: string | undefined;
                                minLength?: number | undefined;
                                excludeEmails?: string | undefined;
                                requireEmails?: string | undefined;
                            }[];
                        }>;
                    } | undefined;
                    views?: {
                        id: string;
                        label: string;
                        description?: string | undefined;
                    }[] | undefined;
                    hideWhenJustOneOption?: boolean | undefined;
                    hidden?: boolean | undefined;
                    editable?: "user" | "system" | "system-but-optional" | "system-but-hidden" | "user-readonly" | undefined;
                    sources?: {
                        id: string;
                        type: string;
                        label: string;
                        editUrl?: string | undefined;
                        fieldRequired?: boolean | undefined;
                    }[] | undefined;
                    disableOnPrefill?: boolean | undefined;
                }[] & z.BRAND<"HAS_SYSTEM_FIELDS">;
            };
            locationOptions: {
                label: string;
                options: {
                    label: string;
                    value: string;
                    disabled?: boolean;
                    icon?: string;
                    slug?: string;
                    credentialId?: number;
                    supportsCustomLabel?: boolean;
                }[];
            }[];
            destinationCalendar: {
                id: number;
                userId: number | null;
                eventTypeId: number | null;
                createdAt: Date | null;
                updatedAt: Date | null;
                integration: string;
                externalId: string;
                primaryEmail: string | null;
                credentialId: number | null;
                delegationCredentialId: string | null;
                domainWideDelegationCredentialId: string | null;
            } | null;
            team: {
                name: string;
                id: number;
                slug: string | null;
                rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
                parentId: number | null;
                members: {
                    user: {
                        name: string | null;
                        id: number;
                        locale: string | null;
                        email: string;
                        username: string | null;
                        avatarUrl: string | null;
                        timeZone: string;
                        defaultScheduleId: number | null;
                        isPlatformManaged: boolean;
                        eventTypes: {
                            slug: string;
                        }[];
                    };
                    role: import(".prisma/client").$Enums.MembershipRole;
                    accepted: boolean;
                }[];
                parent: {
                    organizationSettings: {
                        lockEventTypeCreationForUsers: boolean;
                    } | null;
                    slug: string | null;
                } | null;
            } | null;
            teamMembers: {
                profileId: number | null;
                eventTypes: string[];
                membership: import(".prisma/client").$Enums.MembershipRole;
                name: string | null;
                id: number;
                locale: string | null;
                email: string;
                username: string | null;
                avatarUrl: string | null;
                timeZone: string;
                defaultScheduleId: number | null;
                isPlatformManaged: boolean;
                nonProfileUsername: string | null;
                profile: import("@calcom/types/UserProfile").UserProfile;
                avatar: string;
            }[];
            currentUserMembership: {
                user: {
                    name: string | null;
                    id: number;
                    locale: string | null;
                    email: string;
                    username: string | null;
                    avatarUrl: string | null;
                    timeZone: string;
                    defaultScheduleId: number | null;
                    isPlatformManaged: boolean;
                    eventTypes: {
                        slug: string;
                    }[];
                };
                role: import(".prisma/client").$Enums.MembershipRole;
                accepted: boolean;
            } | null;
            isUserOrganizationAdmin: boolean;
        };
    }>;
    update: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            id: number;
            eventTypeId?: number | undefined;
            users?: (number[] & (string | number)[]) | undefined;
            children?: {
                hidden: boolean;
                owner: {
                    name: string;
                    id: number;
                    email: string;
                    eventTypeSlugs: string[];
                };
            }[] | undefined;
            length?: number | undefined;
            title?: string | undefined;
            metadata?: {
                config?: {
                    useHostSchedulesForTeamEvent?: boolean | undefined;
                } | undefined;
                smartContractAddress?: string | undefined;
                blockchainId?: number | undefined;
                multipleDuration?: number[] | undefined;
                giphyThankYouPage?: string | undefined;
                additionalNotesRequired?: boolean | undefined;
                disableSuccessPage?: boolean | undefined;
                disableStandardEmails?: {
                    all?: {
                        host?: boolean | undefined;
                        attendee?: boolean | undefined;
                    } | undefined;
                    confirmation?: {
                        host?: boolean | undefined;
                        attendee?: boolean | undefined;
                    } | undefined;
                } | undefined;
                managedEventConfig?: {
                    unlockedFields?: {
                        users?: true | undefined;
                        children?: true | undefined;
                        length?: true | undefined;
                        title?: true | undefined;
                        metadata?: true | undefined;
                        description?: true | undefined;
                        userId?: true | undefined;
                        calVideoSettings?: true | undefined;
                        destinationCalendar?: true | undefined;
                        profile?: true | undefined;
                        team?: true | undefined;
                        schedule?: true | undefined;
                        availability?: true | undefined;
                        hashedLink?: true | undefined;
                        secondaryEmail?: true | undefined;
                        customInputs?: true | undefined;
                        timeZone?: true | undefined;
                        bookings?: true | undefined;
                        selectedCalendars?: true | undefined;
                        webhooks?: true | undefined;
                        workflows?: true | undefined;
                        hosts?: true | undefined;
                        slug?: true | undefined;
                        parentId?: true | undefined;
                        bookingLimits?: true | undefined;
                        parent?: true | undefined;
                        teamId?: true | undefined;
                        hidden?: true | undefined;
                        _count?: true | undefined;
                        interfaceLanguage?: true | undefined;
                        position?: true | undefined;
                        locations?: true | undefined;
                        offsetStart?: true | undefined;
                        profileId?: true | undefined;
                        useEventLevelSelectedCalendars?: true | undefined;
                        eventName?: true | undefined;
                        bookingFields?: true | undefined;
                        periodType?: true | undefined;
                        periodStartDate?: true | undefined;
                        periodEndDate?: true | undefined;
                        periodDays?: true | undefined;
                        periodCountCalendarDays?: true | undefined;
                        lockTimeZoneToggleOnBookingPage?: true | undefined;
                        lockedTimeZone?: true | undefined;
                        requiresConfirmation?: true | undefined;
                        requiresConfirmationWillBlockSlot?: true | undefined;
                        requiresConfirmationForFreeEmail?: true | undefined;
                        requiresBookerEmailVerification?: true | undefined;
                        canSendCalVideoTranscriptionEmails?: true | undefined;
                        autoTranslateDescriptionEnabled?: true | undefined;
                        recurringEvent?: true | undefined;
                        disableGuests?: true | undefined;
                        hideCalendarNotes?: true | undefined;
                        hideCalendarEventDetails?: true | undefined;
                        minimumBookingNotice?: true | undefined;
                        beforeEventBuffer?: true | undefined;
                        afterEventBuffer?: true | undefined;
                        seatsPerTimeSlot?: true | undefined;
                        onlyShowFirstAvailableSlot?: true | undefined;
                        disableCancelling?: true | undefined;
                        disableRescheduling?: true | undefined;
                        seatsShowAttendees?: true | undefined;
                        seatsShowAvailabilityCount?: true | undefined;
                        schedulingType?: true | undefined;
                        scheduleId?: true | undefined;
                        allowReschedulingCancelledBookings?: true | undefined;
                        price?: true | undefined;
                        currency?: true | undefined;
                        slotInterval?: true | undefined;
                        successRedirectUrl?: true | undefined;
                        forwardParamsSuccessRedirect?: true | undefined;
                        durationLimits?: true | undefined;
                        isInstantEvent?: true | undefined;
                        instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                        instantMeetingScheduleId?: true | undefined;
                        instantMeetingParameters?: true | undefined;
                        assignAllTeamMembers?: true | undefined;
                        assignRRMembersUsingSegment?: true | undefined;
                        rrSegmentQueryValue?: true | undefined;
                        useEventTypeDestinationCalendarEmail?: true | undefined;
                        isRRWeightsEnabled?: true | undefined;
                        maxLeadThreshold?: true | undefined;
                        includeNoShowInRRCalculation?: true | undefined;
                        allowReschedulingPastBookings?: true | undefined;
                        hideOrganizerEmail?: true | undefined;
                        maxActiveBookingsPerBooker?: true | undefined;
                        maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                        customReplyToEmail?: true | undefined;
                        eventTypeColor?: true | undefined;
                        rescheduleWithSameRoundRobinHost?: true | undefined;
                        secondaryEmailId?: true | undefined;
                        useBookerTimezone?: true | undefined;
                        restrictionScheduleId?: true | undefined;
                        bookingRequiresAuthentication?: true | undefined;
                        owner?: true | undefined;
                        instantMeetingSchedule?: true | undefined;
                        aiPhoneCallConfig?: true | undefined;
                        fieldTranslations?: true | undefined;
                        restrictionSchedule?: true | undefined;
                        hostGroups?: true | undefined;
                    } | undefined;
                } | undefined;
                requiresConfirmationThreshold?: {
                    time: number;
                    unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
                } | undefined;
                bookerLayouts?: {
                    enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
                    defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
                } | null | undefined;
                apps?: unknown;
            } | null | undefined;
            description?: string | null | undefined;
            userId?: number | null | undefined;
            calVideoSettings?: {
                disableRecordingForGuests?: boolean | null | undefined;
                disableRecordingForOrganizer?: boolean | null | undefined;
                enableAutomaticTranscription?: boolean | null | undefined;
                enableAutomaticRecordingForOrganizer?: boolean | null | undefined;
                disableTranscriptionForGuests?: boolean | null | undefined;
                disableTranscriptionForOrganizer?: boolean | null | undefined;
                redirectUrlOnExit?: string | null | undefined;
            } | null | undefined;
            destinationCalendar?: {
                integration: string;
                externalId: string;
            } | null | undefined;
            schedule?: number | null | undefined;
            customInputs?: {
                id: number;
                type: "TEXT" | "TEXTLONG" | "NUMBER" | "BOOL" | "RADIO" | "PHONE";
                label: string;
                required: boolean;
                eventTypeId: number;
                placeholder: string;
                options?: {
                    type: string;
                    label: string;
                }[] | null | undefined;
                hasToBeCreated?: boolean | undefined;
            }[] | undefined;
            timeZone?: string | null | undefined;
            hosts?: {
                userId: number;
                profileId?: number | null | undefined;
                isFixed?: boolean | undefined;
                priority?: number | null | undefined;
                weight?: number | null | undefined;
                scheduleId?: number | null | undefined;
                groupId?: string | null | undefined;
            }[] | undefined;
            slug?: string | undefined;
            parentId?: number | null | undefined;
            bookingLimits?: Partial<Record<"PER_DAY" | "PER_MONTH" | "PER_WEEK" | "PER_YEAR", number | undefined>> | null | undefined;
            teamId?: number | null | undefined;
            hidden?: boolean | undefined;
            interfaceLanguage?: string | null | undefined;
            position?: number | undefined;
            locations?: {
                type: string;
                address?: string | undefined;
                link?: string | undefined;
                displayLocationPublicly?: boolean | undefined;
                hostPhoneNumber?: string | undefined;
                credentialId?: number | undefined;
                teamName?: string | undefined;
                customLabel?: string | undefined;
            }[] | undefined;
            offsetStart?: number | undefined;
            profileId?: number | null | undefined;
            useEventLevelSelectedCalendars?: boolean | undefined;
            eventName?: string | null | undefined;
            bookingFields?: {
                name: string;
                type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
                label?: string | undefined;
                options?: {
                    label: string;
                    value: string;
                }[] | undefined;
                required?: boolean | undefined;
                placeholder?: string | undefined;
                maxLength?: number | undefined;
                defaultLabel?: string | undefined;
                defaultPlaceholder?: string | undefined;
                labelAsSafeHtml?: string | undefined;
                getOptionsAt?: string | undefined;
                optionsInputs?: Record<string, {
                    type: "phone" | "address" | "text";
                    required?: boolean | undefined;
                    placeholder?: string | undefined;
                }> | undefined;
                minLength?: number | undefined;
                excludeEmails?: string | undefined;
                requireEmails?: string | undefined;
                variant?: string | undefined;
                variantsConfig?: {
                    variants: Record<string, {
                        fields: {
                            name: string;
                            type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
                            label?: string | undefined;
                            required?: boolean | undefined;
                            placeholder?: string | undefined;
                            maxLength?: number | undefined;
                            labelAsSafeHtml?: string | undefined;
                            minLength?: number | undefined;
                            excludeEmails?: string | undefined;
                            requireEmails?: string | undefined;
                        }[];
                    }>;
                } | undefined;
                views?: {
                    id: string;
                    label: string;
                    description?: string | undefined;
                }[] | undefined;
                hideWhenJustOneOption?: boolean | undefined;
                hidden?: boolean | undefined;
                editable?: "user" | "system" | "system-but-optional" | "system-but-hidden" | "user-readonly" | undefined;
                sources?: {
                    id: string;
                    type: string;
                    label: string;
                    editUrl?: string | undefined;
                    fieldRequired?: boolean | undefined;
                }[] | undefined;
                disableOnPrefill?: boolean | undefined;
            }[] | undefined;
            periodType?: "UNLIMITED" | "ROLLING" | "ROLLING_WINDOW" | "RANGE" | undefined;
            periodStartDate?: Date | null | undefined;
            periodEndDate?: Date | null | undefined;
            periodDays?: number | null | undefined;
            periodCountCalendarDays?: boolean | null | undefined;
            lockTimeZoneToggleOnBookingPage?: boolean | undefined;
            lockedTimeZone?: string | null | undefined;
            requiresConfirmation?: boolean | undefined;
            requiresConfirmationWillBlockSlot?: boolean | undefined;
            requiresConfirmationForFreeEmail?: boolean | undefined;
            requiresBookerEmailVerification?: boolean | undefined;
            canSendCalVideoTranscriptionEmails?: boolean | undefined;
            autoTranslateDescriptionEnabled?: boolean | undefined;
            recurringEvent?: {
                count: number;
                interval: number;
                freq: import("@calcom/prisma/zod-utils").Frequency;
                dtstart?: Date | undefined;
                until?: Date | undefined;
                tzid?: string | undefined;
            } | null | undefined;
            disableGuests?: boolean | undefined;
            hideCalendarNotes?: boolean | undefined;
            hideCalendarEventDetails?: boolean | undefined;
            minimumBookingNotice?: number | undefined;
            beforeEventBuffer?: number | undefined;
            afterEventBuffer?: number | undefined;
            seatsPerTimeSlot?: number | null | undefined;
            onlyShowFirstAvailableSlot?: boolean | undefined;
            disableCancelling?: boolean | null | undefined;
            disableRescheduling?: boolean | null | undefined;
            seatsShowAttendees?: boolean | null | undefined;
            seatsShowAvailabilityCount?: boolean | null | undefined;
            schedulingType?: "ROUND_ROBIN" | "COLLECTIVE" | "MANAGED" | null | undefined;
            scheduleId?: number | null | undefined;
            allowReschedulingCancelledBookings?: boolean | null | undefined;
            price?: number | undefined;
            currency?: string | undefined;
            slotInterval?: number | null | undefined;
            successRedirectUrl?: string | null | undefined;
            forwardParamsSuccessRedirect?: boolean | null | undefined;
            durationLimits?: Partial<Record<"PER_DAY" | "PER_MONTH" | "PER_WEEK" | "PER_YEAR", number | undefined>> | null | undefined;
            isInstantEvent?: boolean | undefined;
            instantMeetingExpiryTimeOffsetInSeconds?: number | undefined;
            instantMeetingScheduleId?: number | null | undefined;
            instantMeetingParameters?: string[] | undefined;
            assignAllTeamMembers?: boolean | undefined;
            assignRRMembersUsingSegment?: boolean | undefined;
            rrSegmentQueryValue?: {
                type: "group";
                id?: string | undefined;
                children1?: Record<string, {
                    type?: string | undefined;
                    properties?: {
                        field?: any;
                        operator?: any;
                        value?: any;
                        valueSrc?: any;
                        valueError?: (string | null)[] | undefined;
                        valueType?: any;
                    } | undefined;
                }> | undefined;
                properties?: any;
            } | {
                type: "switch_group";
                id?: string | undefined;
                children1?: Record<string, {
                    type?: string | undefined;
                    properties?: {
                        field?: any;
                        operator?: any;
                        value?: any;
                        valueSrc?: any;
                        valueError?: (string | null)[] | undefined;
                        valueType?: any;
                    } | undefined;
                }> | undefined;
                properties?: any;
            } | null | undefined;
            useEventTypeDestinationCalendarEmail?: boolean | undefined;
            isRRWeightsEnabled?: boolean | undefined;
            maxLeadThreshold?: number | null | undefined;
            includeNoShowInRRCalculation?: boolean | undefined;
            allowReschedulingPastBookings?: boolean | undefined;
            hideOrganizerEmail?: boolean | undefined;
            maxActiveBookingsPerBooker?: number | null | undefined;
            maxActiveBookingPerBookerOfferReschedule?: boolean | undefined;
            customReplyToEmail?: string | null | undefined;
            eventTypeColor?: {
                lightEventTypeColor: string;
                darkEventTypeColor: string;
            } | null | undefined;
            rescheduleWithSameRoundRobinHost?: boolean | undefined;
            secondaryEmailId?: number | null | undefined;
            useBookerTimezone?: boolean | undefined;
            restrictionScheduleId?: number | null | undefined;
            bookingRequiresAuthentication?: boolean | undefined;
            instantMeetingSchedule?: number | null | undefined;
            hostGroups?: {
                name: string;
                id: string;
            }[] | undefined;
            calAiPhoneScript?: string | undefined;
            multiplePrivateLinks?: (string | {
                link: string;
                expiresAt?: Date | null | undefined;
                maxUsageCount?: number | null | undefined;
                usageCount?: number | null | undefined;
            })[] | undefined;
            aiPhoneCallConfig?: {
                enabled: boolean;
                templateType: "CHECK_IN_APPOINTMENT" | "CUSTOM_TEMPLATE";
                generalPrompt: string;
                yourPhoneNumber: string;
                numberToCall: string;
                beginMessage: string | null;
                guestName?: string | null | undefined;
                guestEmail?: string | null | undefined;
                guestCompany?: string | null | undefined;
            } | undefined;
        };
        output: {
            eventType: {
                children: {
                    userId: number | null;
                }[];
                title: string;
                description: string | null;
                calVideoSettings: {
                    disableRecordingForOrganizer: boolean;
                    disableRecordingForGuests: boolean;
                    enableAutomaticTranscription: boolean;
                    enableAutomaticRecordingForOrganizer: boolean;
                    redirectUrlOnExit: string | null;
                    disableTranscriptionForGuests: boolean;
                    disableTranscriptionForOrganizer: boolean;
                } | null;
                team: {
                    name: string;
                    id: number;
                    slug: string | null;
                    rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
                    parentId: number | null;
                    members: {
                        user: {
                            name: string | null;
                            id: number;
                            email: string;
                            eventTypes: {
                                slug: string;
                            }[];
                        };
                        role: import(".prisma/client").$Enums.MembershipRole;
                        accepted: boolean;
                    }[];
                    parent: {
                        slug: string | null;
                    } | null;
                } | null;
                workflows: {
                    workflowId: number;
                }[];
                hosts: {
                    userId: number;
                    weight: number | null;
                    isFixed: boolean;
                    priority: number | null;
                }[];
                locations: import(".prisma/client").Prisma.JsonValue;
                recurringEvent: import(".prisma/client").Prisma.JsonValue;
                seatsPerTimeSlot: number | null;
                isRRWeightsEnabled: boolean;
                maxActiveBookingsPerBooker: number | null;
                aiPhoneCallConfig: {
                    enabled: boolean;
                    generalPrompt: string | null;
                    beginMessage: string | null;
                    llmId: string | null;
                } | null;
                fieldTranslations: {
                    field: import(".prisma/client").$Enums.EventTypeAutoTranslatedField;
                }[];
                hostGroups: {
                    name: string;
                    id: string;
                }[];
            };
        };
    }>;
    delete: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            id: number;
            eventTypeId?: number | undefined;
            users?: number[] | undefined;
        };
        output: {
            id: number;
        };
    }>;
    duplicate: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            id: number;
            eventTypeId?: number | undefined;
            users?: number[] | undefined;
            length: number;
            title: string;
            description: string;
            slug: string;
            teamId?: number | null | undefined;
        };
        output: {
            eventType: {
                calVideoSettings: {
                    eventTypeId: number;
                    createdAt: Date;
                    updatedAt: Date;
                    disableRecordingForOrganizer: boolean;
                    disableRecordingForGuests: boolean;
                    enableAutomaticTranscription: boolean;
                    enableAutomaticRecordingForOrganizer: boolean;
                    redirectUrlOnExit: string | null;
                    disableTranscriptionForGuests: boolean;
                    disableTranscriptionForOrganizer: boolean;
                } | null;
            } & {
                id: number;
                length: number;
                title: string;
                metadata: import(".prisma/client").Prisma.JsonValue;
                description: string | null;
                userId: number | null;
                timeZone: string | null;
                slug: string;
                parentId: number | null;
                bookingLimits: import(".prisma/client").Prisma.JsonValue;
                teamId: number | null;
                hidden: boolean;
                interfaceLanguage: string | null;
                position: number;
                locations: import(".prisma/client").Prisma.JsonValue;
                offsetStart: number;
                profileId: number | null;
                useEventLevelSelectedCalendars: boolean;
                eventName: string | null;
                bookingFields: import(".prisma/client").Prisma.JsonValue;
                periodType: import(".prisma/client").$Enums.PeriodType;
                periodStartDate: Date | null;
                periodEndDate: Date | null;
                periodDays: number | null;
                periodCountCalendarDays: boolean | null;
                lockTimeZoneToggleOnBookingPage: boolean;
                lockedTimeZone: string | null;
                requiresConfirmation: boolean;
                requiresConfirmationWillBlockSlot: boolean;
                requiresConfirmationForFreeEmail: boolean;
                requiresBookerEmailVerification: boolean;
                canSendCalVideoTranscriptionEmails: boolean;
                autoTranslateDescriptionEnabled: boolean;
                recurringEvent: import(".prisma/client").Prisma.JsonValue;
                disableGuests: boolean;
                hideCalendarNotes: boolean;
                hideCalendarEventDetails: boolean;
                minimumBookingNotice: number;
                beforeEventBuffer: number;
                afterEventBuffer: number;
                seatsPerTimeSlot: number | null;
                onlyShowFirstAvailableSlot: boolean;
                disableCancelling: boolean | null;
                disableRescheduling: boolean | null;
                seatsShowAttendees: boolean | null;
                seatsShowAvailabilityCount: boolean | null;
                schedulingType: import(".prisma/client").$Enums.SchedulingType | null;
                scheduleId: number | null;
                allowReschedulingCancelledBookings: boolean | null;
                price: number;
                currency: string;
                slotInterval: number | null;
                successRedirectUrl: string | null;
                forwardParamsSuccessRedirect: boolean | null;
                durationLimits: import(".prisma/client").Prisma.JsonValue;
                isInstantEvent: boolean;
                instantMeetingExpiryTimeOffsetInSeconds: number;
                instantMeetingScheduleId: number | null;
                instantMeetingParameters: string[];
                assignAllTeamMembers: boolean;
                assignRRMembersUsingSegment: boolean;
                rrSegmentQueryValue: import(".prisma/client").Prisma.JsonValue;
                useEventTypeDestinationCalendarEmail: boolean;
                isRRWeightsEnabled: boolean;
                maxLeadThreshold: number | null;
                includeNoShowInRRCalculation: boolean;
                allowReschedulingPastBookings: boolean;
                hideOrganizerEmail: boolean;
                maxActiveBookingsPerBooker: number | null;
                maxActiveBookingPerBookerOfferReschedule: boolean;
                customReplyToEmail: string | null;
                eventTypeColor: import(".prisma/client").Prisma.JsonValue;
                rescheduleWithSameRoundRobinHost: boolean;
                secondaryEmailId: number | null;
                useBookerTimezone: boolean;
                restrictionScheduleId: number | null;
                bookingRequiresAuthentication: boolean;
            };
        };
    }>;
    bulkEventFetch: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: {
            eventTypes: {
                logo: string | undefined;
                id: number;
                title: string;
                locations: unknown;
            }[];
        };
    }>;
    bulkUpdateToDefaultLocation: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            eventTypeIds: number[];
        };
        output: import("@prisma/client/runtime/library").GetBatchResult;
    }>;
    getHashedLink: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            linkId: string;
        };
        output: {
            id: number;
            link: string;
            expiresAt: Date | null;
            maxUsageCount: number;
            usageCount: number;
        };
    }>;
    getHashedLinks: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            linkIds: string[];
        };
        output: {
            id: number;
            linkId: string;
            expiresAt: Date | null;
            maxUsageCount: number;
            usageCount: number;
        }[];
    }>;
}>;
