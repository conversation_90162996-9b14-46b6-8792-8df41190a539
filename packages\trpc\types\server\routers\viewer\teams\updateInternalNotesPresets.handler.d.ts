import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TUpdateInternalNotesPresetsInputSchema } from "./updateInternalNotesPresets.schema";
type UpdateInternalNotesPresetsOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TUpdateInternalNotesPresetsInputSchema;
};
export declare const updateInternalNotesPresetsHandler: ({ ctx, input, }: UpdateInternalNotesPresetsOptions) => Promise<{
    name: string;
    id: number;
    createdAt: Date;
    cancellationReason: string | null;
    teamId: number;
}[]>;
export default updateInternalNotesPresetsHandler;
