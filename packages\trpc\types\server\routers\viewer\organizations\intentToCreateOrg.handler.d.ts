import type { TrpcSessionUser } from "../../../types";
import type { TIntentToCreateOrgInputSchema } from "./intentToCreateOrg.schema";
type CreateOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TIntentToCreateOrgInputSchema;
};
export declare const intentToCreateOrgHandler: ({ input, ctx }: CreateOptions) => Promise<{
    userId: number;
    orgOwnerEmail: string;
    name: string;
    slug: string;
    seats: number | null | undefined;
    pricePerSeat: number | null | undefined;
    billingPeriod: import("./intentToCreateOrg.schema").BillingPeriod;
    isPlatform: boolean;
    organizationOnboardingId: string;
}>;
export default intentToCreateOrgHandler;
