import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TListInputSchema } from "./list.schema";
type ListOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TListInputSchema;
};
export declare const listHandler: ({ ctx, input }: ListOptions) => Promise<{
    id: string;
    time: number | null;
    userId: number | null;
    eventTypeId: number | null;
    createdAt: Date;
    teamId: number | null;
    secret: string | null;
    appId: string | null;
    active: boolean;
    platform: boolean;
    subscriberUrl: string;
    payloadTemplate: string | null;
    eventTriggers: import(".prisma/client").$Enums.WebhookTriggerEvents[];
    timeUnit: import(".prisma/client").$Enums.TimeUnit | null;
    platformOAuthClientId: string | null;
}[]>;
export {};
