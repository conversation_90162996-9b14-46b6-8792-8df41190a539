import type { PrismaClient } from "@calcom/prisma";
import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TGetHashedLinksInputSchema } from "./getHashedLinks.schema";
type GetHashedLinksOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
        prisma: PrismaClient;
    };
    input: TGetHashedLinksInputSchema;
};
export declare const getHashedLinksHandler: ({ ctx, input }: GetHashedLinksOptions) => Promise<{
    id: number;
    linkId: string;
    expiresAt: Date | null;
    maxUsageCount: number;
    usageCount: number;
}[]>;
export {};
