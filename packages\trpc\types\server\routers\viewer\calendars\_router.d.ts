export declare const calendarsRouter: import("@trpc/server/unstable-core-do-not-import").CreateRouterInner<import("@trpc/server/unstable-core-do-not-import").RootConfig<{
    ctx: import("../../../createContext").InnerContext;
    meta: object;
    errorShape: {
        message: string;
        code: number;
        data: {
            code: string;
            httpStatus: number;
            path?: string;
            [key: string]: unknown;
        };
    };
    transformer: {
        stringify: (object: any) => string;
        parse: <T = unknown>(string: string) => T;
        serialize: (object: any) => import("superjson/dist/types").SuperJSONResult;
        deserialize: <T_1 = unknown>(payload: import("superjson/dist/types").SuperJSONResult) => T_1;
        registerClass: (v: import("superjson/dist/types").Class, options?: string | import("superjson/dist/class-registry").RegisterOptions | undefined) => void;
        registerSymbol: (v: Symbol, identifier?: string | undefined) => void;
        registerCustom: <I, O extends import("superjson/dist/types").JSONValue>(transformer: Omit<import("superjson/dist/custom-transformer-registry").CustomTransfomer<I, O>, "name">, name: string) => void;
        allowErrorProps: (...props: string[]) => void;
    };
}>, {
    connectedCalendars: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            eventTypeId: number | null;
            onboarding?: boolean | undefined;
        } | undefined;
        output: {
            connectedCalendars: ({
                cacheUpdatedAt: Date | null;
                integration: {
                    installed?: boolean;
                    type: `${string}_calendar` | `${string}_messaging` | `${string}_payment` | `${string}_video` | `${string}_other` | `${string}_automation` | `${string}_analytics` | `${string}_crm` | `${string}_other_calendar`;
                    title?: string;
                    name: string;
                    description: string;
                    variant: "calendar" | "payment" | "conferencing" | "video" | "other" | "other_calendar" | "automation" | "crm";
                    slug: string;
                    category?: string;
                    categories: import(".prisma/client").AppCategories[];
                    extendsFeature?: "EventType" | "User";
                    logo: string;
                    publisher: string;
                    url: string;
                    docsUrl?: string;
                    verified?: boolean;
                    trending?: boolean;
                    rating?: number;
                    reviews?: number;
                    isGlobal?: boolean;
                    simplePath?: string;
                    email: string;
                    key?: import(".prisma/client").Prisma.JsonValue;
                    feeType?: "monthly" | "usage-based" | "one-time" | "free";
                    price?: number;
                    commission?: number;
                    licenseRequired?: boolean;
                    teamsPlanRequired?: {
                        upgradeUrl: string;
                    };
                    appData?: import("@calcom/types/App").AppData;
                    paid?: import("@calcom/types/App").PaidAppData;
                    dirName?: string;
                    isTemplate?: boolean;
                    __template?: string;
                    dependencies?: string[];
                    concurrentMeetings?: boolean;
                    createdAt?: string;
                    isOAuth?: boolean;
                    delegationCredential?: {
                        workspacePlatformSlug: string;
                    };
                    locationOption: import("@calcom/app-store/utils").LocationOption | null;
                };
                credentialId: number;
                delegationCredentialId: string | null;
                error?: undefined;
                primary?: undefined;
                calendars?: undefined;
            } | {
                cacheUpdatedAt: Date | null;
                integration: {
                    installed?: boolean;
                    type: `${string}_calendar` | `${string}_messaging` | `${string}_payment` | `${string}_video` | `${string}_other` | `${string}_automation` | `${string}_analytics` | `${string}_crm` | `${string}_other_calendar`;
                    title?: string;
                    name: string;
                    description: string;
                    variant: "calendar" | "payment" | "conferencing" | "video" | "other" | "other_calendar" | "automation" | "crm";
                    slug: string;
                    category?: string;
                    categories: import(".prisma/client").AppCategories[];
                    extendsFeature?: "EventType" | "User";
                    logo: string;
                    publisher: string;
                    url: string;
                    docsUrl?: string;
                    verified?: boolean;
                    trending?: boolean;
                    rating?: number;
                    reviews?: number;
                    isGlobal?: boolean;
                    simplePath?: string;
                    email: string;
                    key?: import(".prisma/client").Prisma.JsonValue;
                    feeType?: "monthly" | "usage-based" | "one-time" | "free";
                    price?: number;
                    commission?: number;
                    licenseRequired?: boolean;
                    teamsPlanRequired?: {
                        upgradeUrl: string;
                    };
                    appData?: import("@calcom/types/App").AppData;
                    paid?: import("@calcom/types/App").PaidAppData;
                    dirName?: string;
                    isTemplate?: boolean;
                    __template?: string;
                    dependencies?: string[];
                    concurrentMeetings?: boolean;
                    createdAt?: string;
                    isOAuth?: boolean;
                    delegationCredential?: {
                        workspacePlatformSlug: string;
                    };
                    locationOption: import("@calcom/app-store/utils").LocationOption | null;
                };
                credentialId: number;
                error: {
                    message: string;
                };
                delegationCredentialId?: undefined;
                primary?: undefined;
                calendars?: undefined;
            } | {
                cacheUpdatedAt: Date | null;
                integration: {
                    installed?: boolean;
                    type: `${string}_calendar` | `${string}_messaging` | `${string}_payment` | `${string}_video` | `${string}_other` | `${string}_automation` | `${string}_analytics` | `${string}_crm` | `${string}_other_calendar`;
                    title?: string;
                    name: string;
                    description: string;
                    variant: "calendar" | "payment" | "conferencing" | "video" | "other" | "other_calendar" | "automation" | "crm";
                    slug: string;
                    category?: string;
                    categories: import(".prisma/client").AppCategories[];
                    extendsFeature?: "EventType" | "User";
                    logo: string;
                    publisher: string;
                    url: string;
                    docsUrl?: string;
                    verified?: boolean;
                    trending?: boolean;
                    rating?: number;
                    reviews?: number;
                    isGlobal?: boolean;
                    simplePath?: string;
                    email: string;
                    key?: import(".prisma/client").Prisma.JsonValue;
                    feeType?: "monthly" | "usage-based" | "one-time" | "free";
                    price?: number;
                    commission?: number;
                    licenseRequired?: boolean;
                    teamsPlanRequired?: {
                        upgradeUrl: string;
                    };
                    appData?: import("@calcom/types/App").AppData;
                    paid?: import("@calcom/types/App").PaidAppData;
                    dirName?: string;
                    isTemplate?: boolean;
                    __template?: string;
                    dependencies?: string[];
                    concurrentMeetings?: boolean;
                    createdAt?: string;
                    isOAuth?: boolean;
                    delegationCredential?: {
                        workspacePlatformSlug: string;
                    };
                    locationOption: import("@calcom/app-store/utils").LocationOption | null;
                };
                credentialId: number;
                delegationCredentialId: string | null;
                primary: {
                    readOnly: boolean;
                    primary: true | null;
                    isSelected: boolean;
                    credentialId: number;
                    delegationCredentialId: string | null;
                    name?: string;
                    email?: string;
                    primaryEmail?: string;
                    integrationTitle?: string;
                    id?: string | undefined;
                    error?: string | null | undefined;
                    userId?: number | undefined;
                    eventTypeId?: number | null | undefined;
                    createdAt?: Date | null | undefined;
                    updatedAt?: Date | null | undefined;
                    integration?: string | undefined;
                    domainWideDelegationCredentialId?: string | null | undefined;
                    googleChannelId?: string | null | undefined;
                    googleChannelKind?: string | null | undefined;
                    googleChannelResourceId?: string | null | undefined;
                    googleChannelResourceUri?: string | null | undefined;
                    googleChannelExpiration?: string | null | undefined;
                    lastErrorAt?: Date | null | undefined;
                    watchAttempts?: number | undefined;
                    unwatchAttempts?: number | undefined;
                    maxAttempts?: number | undefined;
                    externalId: string;
                };
                calendars: {
                    readOnly: boolean;
                    primary: true | null;
                    isSelected: boolean;
                    credentialId: number;
                    delegationCredentialId: string | null;
                    name?: string;
                    email?: string;
                    primaryEmail?: string;
                    integrationTitle?: string;
                    id?: string | undefined;
                    error?: string | null | undefined;
                    userId?: number | undefined;
                    eventTypeId?: number | null | undefined;
                    createdAt?: Date | null | undefined;
                    updatedAt?: Date | null | undefined;
                    integration?: string | undefined;
                    domainWideDelegationCredentialId?: string | null | undefined;
                    googleChannelId?: string | null | undefined;
                    googleChannelKind?: string | null | undefined;
                    googleChannelResourceId?: string | null | undefined;
                    googleChannelResourceUri?: string | null | undefined;
                    googleChannelExpiration?: string | null | undefined;
                    lastErrorAt?: Date | null | undefined;
                    watchAttempts?: number | undefined;
                    unwatchAttempts?: number | undefined;
                    maxAttempts?: number | undefined;
                    externalId: string;
                }[];
                error?: undefined;
            } | {
                cacheUpdatedAt: Date | null;
                integration: {
                    installed?: boolean;
                    type: `${string}_calendar` | `${string}_messaging` | `${string}_payment` | `${string}_video` | `${string}_other` | `${string}_automation` | `${string}_analytics` | `${string}_crm` | `${string}_other_calendar`;
                    title?: string;
                    name: string;
                    description: string;
                    variant: "calendar" | "payment" | "conferencing" | "video" | "other" | "other_calendar" | "automation" | "crm";
                    slug: string;
                    category?: string;
                    categories: import(".prisma/client").AppCategories[];
                    extendsFeature?: "EventType" | "User";
                    logo: string;
                    publisher: string;
                    url: string;
                    docsUrl?: string;
                    verified?: boolean;
                    trending?: boolean;
                    rating?: number;
                    reviews?: number;
                    isGlobal?: boolean;
                    simplePath?: string;
                    email: string;
                    key?: import(".prisma/client").Prisma.JsonValue;
                    feeType?: "monthly" | "usage-based" | "one-time" | "free";
                    price?: number;
                    commission?: number;
                    licenseRequired?: boolean;
                    teamsPlanRequired?: {
                        upgradeUrl: string;
                    };
                    appData?: import("@calcom/types/App").AppData;
                    paid?: import("@calcom/types/App").PaidAppData;
                    dirName?: string;
                    isTemplate?: boolean;
                    __template?: string;
                    dependencies?: string[];
                    concurrentMeetings?: boolean;
                    createdAt?: string;
                    isOAuth?: boolean;
                    delegationCredential?: {
                        workspacePlatformSlug: string;
                    };
                    locationOption: import("@calcom/app-store/utils").LocationOption | null;
                };
                credentialId: number;
                delegationCredentialId: string | null | undefined;
                error: {
                    message: string;
                };
                primary?: undefined;
                calendars?: undefined;
            })[];
            destinationCalendar: {
                primary?: boolean;
                name?: string;
                readOnly?: boolean;
                email?: string;
                primaryEmail: string | null;
                credentialId: number | null;
                integrationTitle?: string;
                id: string | number;
                error?: string | null | undefined;
                userId: number | null;
                eventTypeId: number | null;
                createdAt: Date | null;
                updatedAt: Date | null;
                integration: string;
                delegationCredentialId: string | null;
                domainWideDelegationCredentialId: string | null;
                googleChannelId?: string | null | undefined;
                googleChannelKind?: string | null | undefined;
                googleChannelResourceId?: string | null | undefined;
                googleChannelResourceUri?: string | null | undefined;
                googleChannelExpiration?: string | null | undefined;
                lastErrorAt?: Date | null | undefined;
                watchAttempts?: number | undefined;
                unwatchAttempts?: number | undefined;
                maxAttempts?: number | undefined;
                externalId: string;
            };
        };
    }>;
    setDestinationCalendar: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            integration: string;
            externalId: string;
            eventTypeId?: number | null | undefined;
            bookingId?: number | null | undefined;
        };
        output: void;
    }>;
    deleteCache: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            credentialId: number;
        };
        output: {
            success: boolean;
        };
    }>;
}>;
