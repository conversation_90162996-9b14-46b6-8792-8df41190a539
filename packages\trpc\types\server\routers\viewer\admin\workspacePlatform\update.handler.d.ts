import type { z } from "zod";
import type { workspacePlatformUpdateSchema } from "./schema";
export default function updateHandler({ input, }: {
    input: z.infer<typeof workspacePlatformUpdateSchema>;
}): Promise<Omit<{
    name: string;
    id: number;
    description: string;
    createdAt: Date;
    updatedAt: Date;
    slug: string;
    enabled: boolean;
    defaultServiceAccountKey: import(".prisma/client").Prisma.JsonValue;
}, "defaultServiceAccountKey"> & {
    defaultServiceAccountKey: undefined;
}>;
