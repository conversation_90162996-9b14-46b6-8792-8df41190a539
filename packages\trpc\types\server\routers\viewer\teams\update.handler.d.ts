import type { IntervalLimit } from "@calcom/lib/intervalLimits/intervalLimitSchema";
import type { TrpcSessionUser } from "../../../types";
import type { TUpdateInputSchema } from "./update.schema";
type UpdateOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TUpdateInputSchema;
};
export declare const updateHandler: ({ ctx, input }: UpdateOptions) => Promise<{
    logoUrl: string | null;
    name: string;
    bio: string | null;
    slug: string | null;
    theme: string | null;
    brandColor: string | null;
    darkBrandColor: string | null;
    bookingLimits: IntervalLimit;
    includeManagedEventsInLimits: boolean;
    rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
    rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
} | undefined>;
export default updateHandler;
