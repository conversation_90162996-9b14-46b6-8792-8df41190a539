export declare const publicViewerRouter: import("@trpc/server/unstable-core-do-not-import").CreateRouterInner<import("@trpc/server/unstable-core-do-not-import").RootConfig<{
    ctx: import("../../createContext").InnerContext;
    meta: object;
    errorShape: {
        message: string;
        code: number;
        data: {
            code: string;
            httpStatus: number;
            path?: string;
            [key: string]: unknown;
        };
    };
    transformer: {
        stringify: (object: any) => string;
        parse: <T = unknown>(string: string) => T;
        serialize: (object: any) => import("superjson/dist/types").SuperJSONResult;
        deserialize: <T_1 = unknown>(payload: import("superjson/dist/types").SuperJSONResult) => T_1;
        registerClass: (v: import("superjson/dist/types").Class, options?: string | import("superjson/dist/class-registry").RegisterOptions | undefined) => void;
        registerSymbol: (v: Symbol, identifier?: string | undefined) => void;
        registerCustom: <I, O extends import("superjson/dist/types").JSONValue>(transformer: Omit<import("superjson/dist/custom-transformer-registry").CustomTransfomer<I, O>, "name">, name: string) => void;
        allowErrorProps: (...props: string[]) => void;
    };
}>, {
    session: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: import("next-auth").Session | null;
    }>;
    countryCode: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: {
            countryCode: string;
        };
    }>;
    submitRating: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            rating: number;
            bookingUid: string;
            comment?: string | undefined;
        };
        output: void;
    }>;
    markHostAsNoShow: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            noShowHost: boolean;
            bookingUid: string;
        };
        output: {
            attendees: import("@calcom/features/handleMarkNoShow").NoShowAttendees;
            noShowHost: boolean;
            message: string;
        };
    }>;
    samlTenantProduct: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            email: string;
        };
        output: {
            tenant: string;
            product: string;
        };
    }>;
    stripeCheckoutSession: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            stripeCustomerId?: string | undefined;
            checkoutSessionId?: string | undefined;
        };
        output: {
            valid: boolean;
            hasPaymentFailed?: undefined;
            isPremiumUsername?: undefined;
            customer?: undefined;
        } | {
            valid: boolean;
            hasPaymentFailed: boolean;
            isPremiumUsername: boolean;
            customer: {
                username: string;
                email: string;
                stripeCustomerId: string;
            };
        };
    }>;
    event: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            username: string;
            org: string | null;
            eventSlug: string;
            isTeamEvent?: boolean | undefined;
            fromRedirectOfNonOrgLink?: boolean | undefined;
        };
        output: {
            bookingFields: {
                name: string;
                type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
                label?: string | undefined;
                options?: {
                    label: string;
                    value: string;
                }[] | undefined;
                required?: boolean | undefined;
                placeholder?: string | undefined;
                maxLength?: number | undefined;
                defaultLabel?: string | undefined;
                defaultPlaceholder?: string | undefined;
                labelAsSafeHtml?: string | undefined;
                getOptionsAt?: string | undefined;
                optionsInputs?: Record<string, {
                    type: "phone" | "address" | "text";
                    required?: boolean | undefined;
                    placeholder?: string | undefined;
                }> | undefined;
                minLength?: number | undefined;
                excludeEmails?: string | undefined;
                requireEmails?: string | undefined;
                variant?: string | undefined;
                variantsConfig?: {
                    variants: Record<string, {
                        fields: {
                            name: string;
                            type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
                            label?: string | undefined;
                            required?: boolean | undefined;
                            placeholder?: string | undefined;
                            maxLength?: number | undefined;
                            labelAsSafeHtml?: string | undefined;
                            minLength?: number | undefined;
                            excludeEmails?: string | undefined;
                            requireEmails?: string | undefined;
                        }[];
                    }>;
                } | undefined;
                views?: {
                    id: string;
                    label: string;
                    description?: string | undefined;
                }[] | undefined;
                hideWhenJustOneOption?: boolean | undefined;
                hidden?: boolean | undefined;
                editable?: "user" | "system" | "system-but-optional" | "system-but-hidden" | "user-readonly" | undefined;
                sources?: {
                    id: string;
                    type: string;
                    label: string;
                    editUrl?: string | undefined;
                    fieldRequired?: boolean | undefined;
                }[] | undefined;
                disableOnPrefill?: boolean | undefined;
            }[] & import("zod").BRAND<"HAS_SYSTEM_FIELDS">;
            subsetOfUsers: ({
                metadata: undefined;
                bookerUrl: string;
                profile: import("@calcom/types/UserProfile").UserAsPersonalProfile;
                name: string | null;
                id: number;
                locale: string | null;
                startTime: number;
                endTime: number;
                email: string;
                movedToProfileId: number | null;
                username: string | null;
                emailVerified: Date | null;
                bio: string | null;
                avatarUrl: string | null;
                timeZone: string;
                weekStart: string;
                bufferTime: number;
                hideBranding: boolean;
                theme: string | null;
                createdDate: Date;
                trialEndsAt: Date | null;
                lastActiveAt: Date | null;
                completedOnboarding: boolean;
                timeFormat: number | null;
                twoFactorSecret: string | null;
                twoFactorEnabled: boolean;
                backupCodes: string | null;
                identityProvider: import(".prisma/client").$Enums.IdentityProvider;
                identityProviderId: string | null;
                invitedTo: number | null;
                brandColor: string | null;
                darkBrandColor: string | null;
                allowDynamicBooking: boolean | null;
                allowSEOIndexing: boolean | null;
                receiveMonthlyDigestEmail: boolean | null;
                verified: boolean | null;
                disableImpersonation: boolean;
                locked: boolean;
                isPlatformManaged: boolean;
                teams: {
                    id: number;
                    role: import(".prisma/client").$Enums.MembershipRole;
                    userId: number;
                    createdAt: Date | null;
                    updatedAt: Date | null;
                    disableImpersonation: boolean;
                    teamId: number;
                    accepted: boolean;
                    customRoleId: string | null;
                }[];
            } | {
                metadata: undefined;
                bookerUrl: string;
                profile: {
                    organization: Omit<{
                        name: string;
                        id: number;
                        metadata: import(".prisma/client").Prisma.JsonValue;
                        organizationSettings: {
                            allowSEOIndexing: boolean;
                            orgProfileRedirectsToVerifiedDomain: boolean;
                        } | null;
                        hideBranding: boolean;
                        slug: string | null;
                        logoUrl: string | null;
                        bannerUrl: string | null;
                        isPlatform: boolean;
                    } & Omit<Pick<{
                        id: number;
                        name: string;
                        slug: string | null;
                        logoUrl: string | null;
                        calVideoLogo: string | null;
                        appLogo: string | null;
                        appIconLogo: string | null;
                        bio: string | null;
                        hideBranding: boolean;
                        hideTeamProfileLink: boolean;
                        isPrivate: boolean;
                        hideBookATeamMember: boolean;
                        createdAt: Date;
                        metadata: import(".prisma/client").Prisma.JsonValue | null;
                        theme: string | null;
                        rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
                        rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
                        brandColor: string | null;
                        darkBrandColor: string | null;
                        bannerUrl: string | null;
                        parentId: number | null;
                        timeFormat: number | null;
                        timeZone: string;
                        weekStart: string;
                        isOrganization: boolean;
                        pendingPayment: boolean;
                        isPlatform: boolean;
                        createdByOAuthClientId: string | null;
                        smsLockState: import(".prisma/client").$Enums.SMSLockState;
                        smsLockReviewedByAdmin: boolean;
                        bookingLimits: import(".prisma/client").Prisma.JsonValue | null;
                        includeManagedEventsInLimits: boolean;
                    }, "name" | "id" | "metadata" | "hideBranding" | "slug" | "logoUrl" | "bannerUrl" | "isPlatform">, "metadata"> & {
                        requestedSlug: string | null;
                        metadata: {
                            requestedSlug: string | null;
                            defaultConferencingApp?: {
                                appSlug?: string | undefined;
                                appLink?: string | undefined;
                            } | undefined;
                            paymentId?: string | undefined;
                            subscriptionId?: string | null | undefined;
                            subscriptionItemId?: string | null | undefined;
                            orgSeats?: number | null | undefined;
                            orgPricePerSeat?: number | null | undefined;
                            migratedToOrgFrom?: {
                                teamSlug?: string | null | undefined;
                                lastMigrationTime?: string | undefined;
                                reverted?: boolean | undefined;
                                lastRevertTime?: string | undefined;
                            } | undefined;
                            billingPeriod?: import("@calcom/prisma/zod-utils").BillingPeriod | undefined;
                        };
                    }, "metadata"> & {
                        requestedSlug: string | null;
                        metadata: {
                            requestedSlug: string | null;
                            defaultConferencingApp?: {
                                appSlug?: string | undefined;
                                appLink?: string | undefined;
                            } | undefined;
                            paymentId?: string | undefined;
                            subscriptionId?: string | null | undefined;
                            subscriptionItemId?: string | null | undefined;
                            orgSeats?: number | null | undefined;
                            orgPricePerSeat?: number | null | undefined;
                            migratedToOrgFrom?: {
                                teamSlug?: string | null | undefined;
                                lastMigrationTime?: string | undefined;
                                reverted?: boolean | undefined;
                                lastRevertTime?: string | undefined;
                            } | undefined;
                            billingPeriod?: import("@calcom/prisma/zod-utils").BillingPeriod | undefined;
                        };
                    };
                    id: number;
                    userId: number;
                    uid: string;
                    createdAt: Date & string;
                    updatedAt: Date & string;
                    username: string;
                    organizationId: number;
                    upId: string;
                };
                name: string | null;
                id: number;
                locale: string | null;
                startTime: number;
                endTime: number;
                email: string;
                movedToProfileId: number | null;
                username: string | null;
                emailVerified: Date | null;
                bio: string | null;
                avatarUrl: string | null;
                timeZone: string;
                weekStart: string;
                bufferTime: number;
                hideBranding: boolean;
                theme: string | null;
                createdDate: Date;
                trialEndsAt: Date | null;
                lastActiveAt: Date | null;
                completedOnboarding: boolean;
                timeFormat: number | null;
                twoFactorSecret: string | null;
                twoFactorEnabled: boolean;
                backupCodes: string | null;
                identityProvider: import(".prisma/client").$Enums.IdentityProvider;
                identityProviderId: string | null;
                invitedTo: number | null;
                brandColor: string | null;
                darkBrandColor: string | null;
                allowDynamicBooking: boolean | null;
                allowSEOIndexing: boolean | null;
                receiveMonthlyDigestEmail: boolean | null;
                verified: boolean | null;
                disableImpersonation: boolean;
                locked: boolean;
                isPlatformManaged: boolean;
                teams: {
                    id: number;
                    role: import(".prisma/client").$Enums.MembershipRole;
                    userId: number;
                    createdAt: Date | null;
                    updatedAt: Date | null;
                    disableImpersonation: boolean;
                    teamId: number;
                    accepted: boolean;
                    customRoleId: string | null;
                }[];
            })[];
            users: ({
                metadata: undefined;
                bookerUrl: string;
                profile: import("@calcom/types/UserProfile").UserAsPersonalProfile;
                name: string | null;
                id: number;
                locale: string | null;
                startTime: number;
                endTime: number;
                email: string;
                movedToProfileId: number | null;
                username: string | null;
                emailVerified: Date | null;
                bio: string | null;
                avatarUrl: string | null;
                timeZone: string;
                weekStart: string;
                bufferTime: number;
                hideBranding: boolean;
                theme: string | null;
                createdDate: Date;
                trialEndsAt: Date | null;
                lastActiveAt: Date | null;
                completedOnboarding: boolean;
                timeFormat: number | null;
                twoFactorSecret: string | null;
                twoFactorEnabled: boolean;
                backupCodes: string | null;
                identityProvider: import(".prisma/client").$Enums.IdentityProvider;
                identityProviderId: string | null;
                invitedTo: number | null;
                brandColor: string | null;
                darkBrandColor: string | null;
                allowDynamicBooking: boolean | null;
                allowSEOIndexing: boolean | null;
                receiveMonthlyDigestEmail: boolean | null;
                verified: boolean | null;
                disableImpersonation: boolean;
                locked: boolean;
                isPlatformManaged: boolean;
                teams: {
                    id: number;
                    role: import(".prisma/client").$Enums.MembershipRole;
                    userId: number;
                    createdAt: Date | null;
                    updatedAt: Date | null;
                    disableImpersonation: boolean;
                    teamId: number;
                    accepted: boolean;
                    customRoleId: string | null;
                }[];
            } | {
                metadata: undefined;
                bookerUrl: string;
                profile: {
                    organization: Omit<{
                        name: string;
                        id: number;
                        metadata: import(".prisma/client").Prisma.JsonValue;
                        organizationSettings: {
                            allowSEOIndexing: boolean;
                            orgProfileRedirectsToVerifiedDomain: boolean;
                        } | null;
                        hideBranding: boolean;
                        slug: string | null;
                        logoUrl: string | null;
                        bannerUrl: string | null;
                        isPlatform: boolean;
                    } & Omit<Pick<{
                        id: number;
                        name: string;
                        slug: string | null;
                        logoUrl: string | null;
                        calVideoLogo: string | null;
                        appLogo: string | null;
                        appIconLogo: string | null;
                        bio: string | null;
                        hideBranding: boolean;
                        hideTeamProfileLink: boolean;
                        isPrivate: boolean;
                        hideBookATeamMember: boolean;
                        createdAt: Date;
                        metadata: import(".prisma/client").Prisma.JsonValue | null;
                        theme: string | null;
                        rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
                        rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
                        brandColor: string | null;
                        darkBrandColor: string | null;
                        bannerUrl: string | null;
                        parentId: number | null;
                        timeFormat: number | null;
                        timeZone: string;
                        weekStart: string;
                        isOrganization: boolean;
                        pendingPayment: boolean;
                        isPlatform: boolean;
                        createdByOAuthClientId: string | null;
                        smsLockState: import(".prisma/client").$Enums.SMSLockState;
                        smsLockReviewedByAdmin: boolean;
                        bookingLimits: import(".prisma/client").Prisma.JsonValue | null;
                        includeManagedEventsInLimits: boolean;
                    }, "name" | "id" | "metadata" | "hideBranding" | "slug" | "logoUrl" | "bannerUrl" | "isPlatform">, "metadata"> & {
                        requestedSlug: string | null;
                        metadata: {
                            requestedSlug: string | null;
                            defaultConferencingApp?: {
                                appSlug?: string | undefined;
                                appLink?: string | undefined;
                            } | undefined;
                            paymentId?: string | undefined;
                            subscriptionId?: string | null | undefined;
                            subscriptionItemId?: string | null | undefined;
                            orgSeats?: number | null | undefined;
                            orgPricePerSeat?: number | null | undefined;
                            migratedToOrgFrom?: {
                                teamSlug?: string | null | undefined;
                                lastMigrationTime?: string | undefined;
                                reverted?: boolean | undefined;
                                lastRevertTime?: string | undefined;
                            } | undefined;
                            billingPeriod?: import("@calcom/prisma/zod-utils").BillingPeriod | undefined;
                        };
                    }, "metadata"> & {
                        requestedSlug: string | null;
                        metadata: {
                            requestedSlug: string | null;
                            defaultConferencingApp?: {
                                appSlug?: string | undefined;
                                appLink?: string | undefined;
                            } | undefined;
                            paymentId?: string | undefined;
                            subscriptionId?: string | null | undefined;
                            subscriptionItemId?: string | null | undefined;
                            orgSeats?: number | null | undefined;
                            orgPricePerSeat?: number | null | undefined;
                            migratedToOrgFrom?: {
                                teamSlug?: string | null | undefined;
                                lastMigrationTime?: string | undefined;
                                reverted?: boolean | undefined;
                                lastRevertTime?: string | undefined;
                            } | undefined;
                            billingPeriod?: import("@calcom/prisma/zod-utils").BillingPeriod | undefined;
                        };
                    };
                    id: number;
                    userId: number;
                    uid: string;
                    createdAt: Date & string;
                    updatedAt: Date & string;
                    username: string;
                    organizationId: number;
                    upId: string;
                };
                name: string | null;
                id: number;
                locale: string | null;
                startTime: number;
                endTime: number;
                email: string;
                movedToProfileId: number | null;
                username: string | null;
                emailVerified: Date | null;
                bio: string | null;
                avatarUrl: string | null;
                timeZone: string;
                weekStart: string;
                bufferTime: number;
                hideBranding: boolean;
                theme: string | null;
                createdDate: Date;
                trialEndsAt: Date | null;
                lastActiveAt: Date | null;
                completedOnboarding: boolean;
                timeFormat: number | null;
                twoFactorSecret: string | null;
                twoFactorEnabled: boolean;
                backupCodes: string | null;
                identityProvider: import(".prisma/client").$Enums.IdentityProvider;
                identityProviderId: string | null;
                invitedTo: number | null;
                brandColor: string | null;
                darkBrandColor: string | null;
                allowDynamicBooking: boolean | null;
                allowSEOIndexing: boolean | null;
                receiveMonthlyDigestEmail: boolean | null;
                verified: boolean | null;
                disableImpersonation: boolean;
                locked: boolean;
                isPlatformManaged: boolean;
                teams: {
                    id: number;
                    role: import(".prisma/client").$Enums.MembershipRole;
                    userId: number;
                    createdAt: Date | null;
                    updatedAt: Date | null;
                    disableImpersonation: boolean;
                    teamId: number;
                    accepted: boolean;
                    customRoleId: string | null;
                }[];
            })[] | undefined;
            locations: (Pick<Partial<import("@calcom/app-store/locations").LocationObject>, "link" | "address" | "customLabel"> & Omit<import("@calcom/app-store/locations").LocationObject, "link" | "address" | "customLabel">)[];
            profile: {
                image?: string | undefined;
                name?: string | undefined;
                username?: string | null | undefined;
                weekStart: string;
                brandColor: string | null;
                darkBrandColor: string | null;
                theme: null;
                bookerLayouts: {
                    enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
                    defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
                } | null;
            };
            entity: {
                considerUnpublished: boolean;
                fromRedirectOfNonOrgLink: boolean;
                orgSlug: string | null;
                name: string | null;
                teamSlug: null;
                logoUrl: null;
                hideProfileLink: boolean;
            };
            isInstantEvent: boolean;
            instantMeetingParameters: never[];
            showInstantEventConnectNowModal: boolean;
            autoTranslateDescriptionEnabled: boolean;
            fieldTranslations: never[];
            metadata: {
                config?: {
                    useHostSchedulesForTeamEvent?: boolean | undefined;
                } | undefined;
                smartContractAddress?: string | undefined;
                blockchainId?: number | undefined;
                multipleDuration?: number[] | undefined;
                giphyThankYouPage?: string | undefined;
                additionalNotesRequired?: boolean | undefined;
                disableSuccessPage?: boolean | undefined;
                disableStandardEmails?: {
                    all?: {
                        host?: boolean | undefined;
                        attendee?: boolean | undefined;
                    } | undefined;
                    confirmation?: {
                        host?: boolean | undefined;
                        attendee?: boolean | undefined;
                    } | undefined;
                } | undefined;
                managedEventConfig?: {
                    unlockedFields?: {
                        users?: true | undefined;
                        children?: true | undefined;
                        length?: true | undefined;
                        title?: true | undefined;
                        metadata?: true | undefined;
                        description?: true | undefined;
                        userId?: true | undefined;
                        calVideoSettings?: true | undefined;
                        destinationCalendar?: true | undefined;
                        profile?: true | undefined;
                        team?: true | undefined;
                        schedule?: true | undefined;
                        availability?: true | undefined;
                        hashedLink?: true | undefined;
                        secondaryEmail?: true | undefined;
                        customInputs?: true | undefined;
                        timeZone?: true | undefined;
                        bookings?: true | undefined;
                        selectedCalendars?: true | undefined;
                        webhooks?: true | undefined;
                        workflows?: true | undefined;
                        hosts?: true | undefined;
                        slug?: true | undefined;
                        parentId?: true | undefined;
                        bookingLimits?: true | undefined;
                        parent?: true | undefined;
                        teamId?: true | undefined;
                        hidden?: true | undefined;
                        _count?: true | undefined;
                        interfaceLanguage?: true | undefined;
                        position?: true | undefined;
                        locations?: true | undefined;
                        offsetStart?: true | undefined;
                        profileId?: true | undefined;
                        useEventLevelSelectedCalendars?: true | undefined;
                        eventName?: true | undefined;
                        bookingFields?: true | undefined;
                        periodType?: true | undefined;
                        periodStartDate?: true | undefined;
                        periodEndDate?: true | undefined;
                        periodDays?: true | undefined;
                        periodCountCalendarDays?: true | undefined;
                        lockTimeZoneToggleOnBookingPage?: true | undefined;
                        lockedTimeZone?: true | undefined;
                        requiresConfirmation?: true | undefined;
                        requiresConfirmationWillBlockSlot?: true | undefined;
                        requiresConfirmationForFreeEmail?: true | undefined;
                        requiresBookerEmailVerification?: true | undefined;
                        canSendCalVideoTranscriptionEmails?: true | undefined;
                        autoTranslateDescriptionEnabled?: true | undefined;
                        recurringEvent?: true | undefined;
                        disableGuests?: true | undefined;
                        hideCalendarNotes?: true | undefined;
                        hideCalendarEventDetails?: true | undefined;
                        minimumBookingNotice?: true | undefined;
                        beforeEventBuffer?: true | undefined;
                        afterEventBuffer?: true | undefined;
                        seatsPerTimeSlot?: true | undefined;
                        onlyShowFirstAvailableSlot?: true | undefined;
                        disableCancelling?: true | undefined;
                        disableRescheduling?: true | undefined;
                        seatsShowAttendees?: true | undefined;
                        seatsShowAvailabilityCount?: true | undefined;
                        schedulingType?: true | undefined;
                        scheduleId?: true | undefined;
                        allowReschedulingCancelledBookings?: true | undefined;
                        price?: true | undefined;
                        currency?: true | undefined;
                        slotInterval?: true | undefined;
                        successRedirectUrl?: true | undefined;
                        forwardParamsSuccessRedirect?: true | undefined;
                        durationLimits?: true | undefined;
                        isInstantEvent?: true | undefined;
                        instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                        instantMeetingScheduleId?: true | undefined;
                        instantMeetingParameters?: true | undefined;
                        assignAllTeamMembers?: true | undefined;
                        assignRRMembersUsingSegment?: true | undefined;
                        rrSegmentQueryValue?: true | undefined;
                        useEventTypeDestinationCalendarEmail?: true | undefined;
                        isRRWeightsEnabled?: true | undefined;
                        maxLeadThreshold?: true | undefined;
                        includeNoShowInRRCalculation?: true | undefined;
                        allowReschedulingPastBookings?: true | undefined;
                        hideOrganizerEmail?: true | undefined;
                        maxActiveBookingsPerBooker?: true | undefined;
                        maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                        customReplyToEmail?: true | undefined;
                        eventTypeColor?: true | undefined;
                        rescheduleWithSameRoundRobinHost?: true | undefined;
                        secondaryEmailId?: true | undefined;
                        useBookerTimezone?: true | undefined;
                        restrictionScheduleId?: true | undefined;
                        bookingRequiresAuthentication?: true | undefined;
                        owner?: true | undefined;
                        instantMeetingSchedule?: true | undefined;
                        aiPhoneCallConfig?: true | undefined;
                        fieldTranslations?: true | undefined;
                        restrictionSchedule?: true | undefined;
                        hostGroups?: true | undefined;
                    } | undefined;
                } | undefined;
                requiresConfirmationThreshold?: {
                    time: number;
                    unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
                } | undefined;
                bookerLayouts?: {
                    enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
                    defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
                } | null | undefined;
                apps?: {
                    alby?: {
                        price: number;
                        currency: string;
                        appCategories?: string[] | undefined;
                        paymentOption?: string | undefined;
                        enabled?: boolean | undefined;
                        credentialId?: number | undefined;
                    } | undefined;
                    basecamp3?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    btcpayserver?: {
                        price: number;
                        currency: string;
                        appCategories?: string[] | undefined;
                        paymentOption?: string | undefined;
                        enabled?: boolean | undefined;
                        credentialId?: number | undefined;
                    } | undefined;
                    closecom?: {
                        enabled?: boolean | undefined;
                        credentialId?: number | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    dailyvideo?: {} | undefined;
                    dub?: {} | undefined;
                    fathom?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        trackingId?: string | undefined;
                    } | undefined;
                    feishucalendar?: {} | undefined;
                    ga4?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        trackingId?: string | undefined;
                    } | undefined;
                    giphy?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        thankYouPage?: string | undefined;
                    } | undefined;
                    googlecalendar?: {} | undefined;
                    googlevideo?: {} | undefined;
                    gtm?: {
                        trackingId: string;
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    hitpay?: {
                        price: number;
                        currency: string;
                        credentialId?: number | undefined;
                        appCategories?: string[] | undefined;
                        paymentOption?: string | undefined;
                        enabled?: boolean | undefined;
                    } | undefined;
                    hubspot?: {
                        enabled?: boolean | undefined;
                        credentialId?: number | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    insihts?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        SITE_ID?: string | undefined;
                        SCRIPT_URL?: string | undefined;
                    } | undefined;
                    intercom?: {} | undefined;
                    jelly?: {} | undefined;
                    jitsivideo?: {} | undefined;
                    larkcalendar?: {} | undefined;
                    make?: {} | undefined;
                    matomo?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        MATOMO_URL?: string | undefined;
                        SITE_ID?: string | undefined;
                    } | undefined;
                    metapixel?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        trackingId?: string | undefined;
                    } | undefined;
                    "mock-payment-app"?: {
                        price: number;
                        currency: string;
                        credentialId?: number | undefined;
                        appCategories?: string[] | undefined;
                        paymentOption?: string | undefined;
                        enabled?: boolean | undefined;
                    } | undefined;
                    nextcloudtalk?: {} | undefined;
                    office365calendar?: {
                        client_id: string;
                        client_secret: string;
                    } | undefined;
                    office365video?: {
                        client_id: string;
                        client_secret: string;
                    } | undefined;
                    paypal?: {
                        price: number;
                        currency: string;
                        credentialId?: number | undefined;
                        appCategories?: string[] | undefined;
                        paymentOption?: string | undefined;
                        enabled?: boolean | undefined;
                    } | undefined;
                    "pipedrive-crm"?: {
                        enabled?: boolean | undefined;
                        credentialId?: number | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    plausible?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        PLAUSIBLE_URL?: string | undefined;
                        trackingId?: string | undefined;
                    } | undefined;
                    posthog?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        TRACKING_ID?: string | undefined;
                        API_HOST?: string | undefined;
                    } | undefined;
                    qr_code?: {
                        enabled?: boolean | undefined;
                        credentialId?: number | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    "routing-forms"?: any;
                    salesforce?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        roundRobinLeadSkip?: boolean | undefined;
                        roundRobinSkipCheckRecordOn?: import("@calcom/app-store/salesforce/lib/enums").SalesforceRecordEnum | undefined;
                        ifFreeEmailDomainSkipOwnerCheck?: boolean | undefined;
                        roundRobinSkipFallbackToLeadOwner?: boolean | undefined;
                        skipContactCreation?: boolean | undefined;
                        createEventOn?: import("@calcom/app-store/salesforce/lib/enums").SalesforceRecordEnum | undefined;
                        createNewContactUnderAccount?: boolean | undefined;
                        createLeadIfAccountNull?: boolean | undefined;
                        onBookingWriteToEventObject?: boolean | undefined;
                        onBookingWriteToEventObjectMap?: Record<string, any> | undefined;
                        createEventOnLeadCheckForContact?: boolean | undefined;
                        onBookingChangeRecordOwner?: boolean | undefined;
                        onBookingChangeRecordOwnerName?: string | undefined;
                        sendNoShowAttendeeData?: boolean | undefined;
                        sendNoShowAttendeeDataField?: string | undefined;
                        onBookingWriteToRecord?: boolean | undefined;
                        onBookingWriteToRecordFields?: Record<string, {
                            value: string | boolean;
                            fieldType: import("@calcom/app-store/salesforce/lib/enums").SalesforceFieldType;
                            whenToWrite: import("@calcom/app-store/salesforce/lib/enums").WhenToWriteToRecord;
                        }> | undefined;
                        ignoreGuests?: boolean | undefined;
                        onCancelWriteToEventRecord?: boolean | undefined;
                        onCancelWriteToEventRecordFields?: Record<string, {
                            value: string | boolean;
                            fieldType: import("@calcom/app-store/salesforce/lib/enums").SalesforceFieldType;
                            whenToWrite: import("@calcom/app-store/salesforce/lib/enums").WhenToWriteToRecord;
                        }> | undefined;
                    } | undefined;
                    shimmervideo?: {} | undefined;
                    stripe?: {
                        price: number;
                        currency: string;
                        credentialId?: number | undefined;
                        appCategories?: string[] | undefined;
                        paymentOption?: string | undefined;
                        enabled?: boolean | undefined;
                        refundPolicy?: import("@calcom/lib/payment/types").RefundPolicy | undefined;
                        refundDaysCount?: number | undefined;
                        refundCountCalendarDays?: boolean | undefined;
                    } | undefined;
                    tandemvideo?: {} | undefined;
                    "booking-pages-tag"?: {
                        trackingId: string;
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    "event-type-app-card"?: {
                        isSunrise: boolean;
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    twipla?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        SITE_ID?: string | undefined;
                    } | undefined;
                    umami?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        SITE_ID?: string | undefined;
                        SCRIPT_URL?: string | undefined;
                    } | undefined;
                    vital?: {} | undefined;
                    webex?: {} | undefined;
                    wordpress?: {
                        isSunrise: boolean;
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    zapier?: {} | undefined;
                    "zoho-bigin"?: {
                        enabled?: boolean | undefined;
                        credentialId?: number | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    zohocalendar?: {} | undefined;
                    zohocrm?: {
                        enabled?: boolean | undefined;
                        credentialId?: number | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    zoomvideo?: {} | undefined;
                } | undefined;
            } | null;
            isDynamic: boolean;
            periodCountCalendarDays: boolean;
            periodStartDate: null;
            periodEndDate: null;
            beforeEventBuffer: number;
            afterEventBuffer: number;
            periodType: "UNLIMITED";
            periodDays: null;
            slotInterval: null;
            offsetStart: number;
            customInputs: {
                id: number;
                type: "TEXT" | "TEXTLONG" | "NUMBER" | "BOOL" | "RADIO" | "PHONE";
                label: string;
                required: boolean;
                eventTypeId: number;
                placeholder: string;
                options?: {
                    type: string;
                    label: string;
                }[] | null | undefined;
                hasToBeCreated?: boolean | undefined;
            }[];
            disableGuests: boolean;
            minimumBookingNotice: number;
            schedule: null;
            timeZone: null;
            successRedirectUrl: string;
            forwardParamsSuccessRedirect: boolean;
            teamId: null;
            scheduleId: null;
            availability: never[];
            price: number;
            currency: string;
            schedulingType: "COLLECTIVE";
            seatsPerTimeSlot: null;
            seatsShowAttendees: null;
            seatsShowAvailabilityCount: null;
            disableCancelling: boolean;
            disableRescheduling: boolean;
            onlyShowFirstAvailableSlot: boolean;
            allowReschedulingPastBookings: boolean;
            hideOrganizerEmail: boolean;
            id: number;
            hideCalendarNotes: boolean;
            hideCalendarEventDetails: boolean;
            recurringEvent: null;
            destinationCalendar: null;
            team: null;
            lockTimeZoneToggleOnBookingPage: boolean;
            lockedTimeZone: null;
            requiresConfirmation: boolean;
            requiresConfirmationForFreeEmail: boolean;
            requiresBookerEmailVerification: boolean;
            bookingLimits: null;
            maxActiveBookingsPerBooker: null;
            maxActiveBookingPerBookerOfferReschedule: boolean;
            durationLimits: null;
            hidden: boolean;
            userId: number;
            parentId: null;
            parent: null;
            owner: null;
            workflows: never[];
            hosts: never[];
            subsetOfHosts: never[];
            assignAllTeamMembers: boolean;
            assignRRMembersUsingSegment: boolean;
            rrSegmentQueryValue: null;
            isRRWeightsEnabled: boolean;
            rescheduleWithSameRoundRobinHost: boolean;
            useEventTypeDestinationCalendarEmail: boolean;
            secondaryEmailId: null;
            secondaryEmail: null;
            maxLeadThreshold: null;
            includeNoShowInRRCalculation: boolean;
            useEventLevelSelectedCalendars: boolean;
            rrResetInterval: null;
            rrTimestampBasis: null;
            interfaceLanguage: null;
            customReplyToEmail: null;
            restrictionScheduleId: null;
            useBookerTimezone: boolean;
            profileId: null;
            requiresConfirmationWillBlockSlot: boolean;
            canSendCalVideoTranscriptionEmails: boolean;
            instantMeetingExpiryTimeOffsetInSeconds: number;
            instantMeetingScheduleId: null;
            eventTypeColor: null;
            hostGroups: never[];
            bookingRequiresAuthentication: boolean;
            length: number;
            slug: string;
            title: string;
            eventName: string;
            description: string;
            descriptionAsSafeHTML: string;
            position: number;
        } | {
            bookerLayouts: {
                enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
                defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
            } | null;
            description: string;
            metadata: {
                config?: {
                    useHostSchedulesForTeamEvent?: boolean | undefined;
                } | undefined;
                smartContractAddress?: string | undefined;
                blockchainId?: number | undefined;
                multipleDuration?: number[] | undefined;
                giphyThankYouPage?: string | undefined;
                additionalNotesRequired?: boolean | undefined;
                disableSuccessPage?: boolean | undefined;
                disableStandardEmails?: {
                    all?: {
                        host?: boolean | undefined;
                        attendee?: boolean | undefined;
                    } | undefined;
                    confirmation?: {
                        host?: boolean | undefined;
                        attendee?: boolean | undefined;
                    } | undefined;
                } | undefined;
                managedEventConfig?: {
                    unlockedFields?: {
                        users?: true | undefined;
                        children?: true | undefined;
                        length?: true | undefined;
                        title?: true | undefined;
                        metadata?: true | undefined;
                        description?: true | undefined;
                        userId?: true | undefined;
                        calVideoSettings?: true | undefined;
                        destinationCalendar?: true | undefined;
                        profile?: true | undefined;
                        team?: true | undefined;
                        schedule?: true | undefined;
                        availability?: true | undefined;
                        hashedLink?: true | undefined;
                        secondaryEmail?: true | undefined;
                        customInputs?: true | undefined;
                        timeZone?: true | undefined;
                        bookings?: true | undefined;
                        selectedCalendars?: true | undefined;
                        webhooks?: true | undefined;
                        workflows?: true | undefined;
                        hosts?: true | undefined;
                        slug?: true | undefined;
                        parentId?: true | undefined;
                        bookingLimits?: true | undefined;
                        parent?: true | undefined;
                        teamId?: true | undefined;
                        hidden?: true | undefined;
                        _count?: true | undefined;
                        interfaceLanguage?: true | undefined;
                        position?: true | undefined;
                        locations?: true | undefined;
                        offsetStart?: true | undefined;
                        profileId?: true | undefined;
                        useEventLevelSelectedCalendars?: true | undefined;
                        eventName?: true | undefined;
                        bookingFields?: true | undefined;
                        periodType?: true | undefined;
                        periodStartDate?: true | undefined;
                        periodEndDate?: true | undefined;
                        periodDays?: true | undefined;
                        periodCountCalendarDays?: true | undefined;
                        lockTimeZoneToggleOnBookingPage?: true | undefined;
                        lockedTimeZone?: true | undefined;
                        requiresConfirmation?: true | undefined;
                        requiresConfirmationWillBlockSlot?: true | undefined;
                        requiresConfirmationForFreeEmail?: true | undefined;
                        requiresBookerEmailVerification?: true | undefined;
                        canSendCalVideoTranscriptionEmails?: true | undefined;
                        autoTranslateDescriptionEnabled?: true | undefined;
                        recurringEvent?: true | undefined;
                        disableGuests?: true | undefined;
                        hideCalendarNotes?: true | undefined;
                        hideCalendarEventDetails?: true | undefined;
                        minimumBookingNotice?: true | undefined;
                        beforeEventBuffer?: true | undefined;
                        afterEventBuffer?: true | undefined;
                        seatsPerTimeSlot?: true | undefined;
                        onlyShowFirstAvailableSlot?: true | undefined;
                        disableCancelling?: true | undefined;
                        disableRescheduling?: true | undefined;
                        seatsShowAttendees?: true | undefined;
                        seatsShowAvailabilityCount?: true | undefined;
                        schedulingType?: true | undefined;
                        scheduleId?: true | undefined;
                        allowReschedulingCancelledBookings?: true | undefined;
                        price?: true | undefined;
                        currency?: true | undefined;
                        slotInterval?: true | undefined;
                        successRedirectUrl?: true | undefined;
                        forwardParamsSuccessRedirect?: true | undefined;
                        durationLimits?: true | undefined;
                        isInstantEvent?: true | undefined;
                        instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                        instantMeetingScheduleId?: true | undefined;
                        instantMeetingParameters?: true | undefined;
                        assignAllTeamMembers?: true | undefined;
                        assignRRMembersUsingSegment?: true | undefined;
                        rrSegmentQueryValue?: true | undefined;
                        useEventTypeDestinationCalendarEmail?: true | undefined;
                        isRRWeightsEnabled?: true | undefined;
                        maxLeadThreshold?: true | undefined;
                        includeNoShowInRRCalculation?: true | undefined;
                        allowReschedulingPastBookings?: true | undefined;
                        hideOrganizerEmail?: true | undefined;
                        maxActiveBookingsPerBooker?: true | undefined;
                        maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                        customReplyToEmail?: true | undefined;
                        eventTypeColor?: true | undefined;
                        rescheduleWithSameRoundRobinHost?: true | undefined;
                        secondaryEmailId?: true | undefined;
                        useBookerTimezone?: true | undefined;
                        restrictionScheduleId?: true | undefined;
                        bookingRequiresAuthentication?: true | undefined;
                        owner?: true | undefined;
                        instantMeetingSchedule?: true | undefined;
                        aiPhoneCallConfig?: true | undefined;
                        fieldTranslations?: true | undefined;
                        restrictionSchedule?: true | undefined;
                        hostGroups?: true | undefined;
                    } | undefined;
                } | undefined;
                requiresConfirmationThreshold?: {
                    time: number;
                    unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
                } | undefined;
                bookerLayouts?: {
                    enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
                    defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
                } | null | undefined;
                apps?: {
                    alby?: {
                        price: number;
                        currency: string;
                        appCategories?: string[] | undefined;
                        paymentOption?: string | undefined;
                        enabled?: boolean | undefined;
                        credentialId?: number | undefined;
                    } | undefined;
                    basecamp3?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    btcpayserver?: {
                        price: number;
                        currency: string;
                        appCategories?: string[] | undefined;
                        paymentOption?: string | undefined;
                        enabled?: boolean | undefined;
                        credentialId?: number | undefined;
                    } | undefined;
                    closecom?: {
                        enabled?: boolean | undefined;
                        credentialId?: number | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    dailyvideo?: {} | undefined;
                    dub?: {} | undefined;
                    fathom?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        trackingId?: string | undefined;
                    } | undefined;
                    feishucalendar?: {} | undefined;
                    ga4?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        trackingId?: string | undefined;
                    } | undefined;
                    giphy?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        thankYouPage?: string | undefined;
                    } | undefined;
                    googlecalendar?: {} | undefined;
                    googlevideo?: {} | undefined;
                    gtm?: {
                        trackingId: string;
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    hitpay?: {
                        price: number;
                        currency: string;
                        credentialId?: number | undefined;
                        appCategories?: string[] | undefined;
                        paymentOption?: string | undefined;
                        enabled?: boolean | undefined;
                    } | undefined;
                    hubspot?: {
                        enabled?: boolean | undefined;
                        credentialId?: number | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    insihts?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        SITE_ID?: string | undefined;
                        SCRIPT_URL?: string | undefined;
                    } | undefined;
                    intercom?: {} | undefined;
                    jelly?: {} | undefined;
                    jitsivideo?: {} | undefined;
                    larkcalendar?: {} | undefined;
                    make?: {} | undefined;
                    matomo?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        MATOMO_URL?: string | undefined;
                        SITE_ID?: string | undefined;
                    } | undefined;
                    metapixel?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        trackingId?: string | undefined;
                    } | undefined;
                    "mock-payment-app"?: {
                        price: number;
                        currency: string;
                        credentialId?: number | undefined;
                        appCategories?: string[] | undefined;
                        paymentOption?: string | undefined;
                        enabled?: boolean | undefined;
                    } | undefined;
                    nextcloudtalk?: {} | undefined;
                    office365calendar?: {
                        client_id: string;
                        client_secret: string;
                    } | undefined;
                    office365video?: {
                        client_id: string;
                        client_secret: string;
                    } | undefined;
                    paypal?: {
                        price: number;
                        currency: string;
                        credentialId?: number | undefined;
                        appCategories?: string[] | undefined;
                        paymentOption?: string | undefined;
                        enabled?: boolean | undefined;
                    } | undefined;
                    "pipedrive-crm"?: {
                        enabled?: boolean | undefined;
                        credentialId?: number | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    plausible?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        PLAUSIBLE_URL?: string | undefined;
                        trackingId?: string | undefined;
                    } | undefined;
                    posthog?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        TRACKING_ID?: string | undefined;
                        API_HOST?: string | undefined;
                    } | undefined;
                    qr_code?: {
                        enabled?: boolean | undefined;
                        credentialId?: number | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    "routing-forms"?: any;
                    salesforce?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        roundRobinLeadSkip?: boolean | undefined;
                        roundRobinSkipCheckRecordOn?: import("@calcom/app-store/salesforce/lib/enums").SalesforceRecordEnum | undefined;
                        ifFreeEmailDomainSkipOwnerCheck?: boolean | undefined;
                        roundRobinSkipFallbackToLeadOwner?: boolean | undefined;
                        skipContactCreation?: boolean | undefined;
                        createEventOn?: import("@calcom/app-store/salesforce/lib/enums").SalesforceRecordEnum | undefined;
                        createNewContactUnderAccount?: boolean | undefined;
                        createLeadIfAccountNull?: boolean | undefined;
                        onBookingWriteToEventObject?: boolean | undefined;
                        onBookingWriteToEventObjectMap?: Record<string, any> | undefined;
                        createEventOnLeadCheckForContact?: boolean | undefined;
                        onBookingChangeRecordOwner?: boolean | undefined;
                        onBookingChangeRecordOwnerName?: string | undefined;
                        sendNoShowAttendeeData?: boolean | undefined;
                        sendNoShowAttendeeDataField?: string | undefined;
                        onBookingWriteToRecord?: boolean | undefined;
                        onBookingWriteToRecordFields?: Record<string, {
                            value: string | boolean;
                            fieldType: import("@calcom/app-store/salesforce/lib/enums").SalesforceFieldType;
                            whenToWrite: import("@calcom/app-store/salesforce/lib/enums").WhenToWriteToRecord;
                        }> | undefined;
                        ignoreGuests?: boolean | undefined;
                        onCancelWriteToEventRecord?: boolean | undefined;
                        onCancelWriteToEventRecordFields?: Record<string, {
                            value: string | boolean;
                            fieldType: import("@calcom/app-store/salesforce/lib/enums").SalesforceFieldType;
                            whenToWrite: import("@calcom/app-store/salesforce/lib/enums").WhenToWriteToRecord;
                        }> | undefined;
                    } | undefined;
                    shimmervideo?: {} | undefined;
                    stripe?: {
                        price: number;
                        currency: string;
                        credentialId?: number | undefined;
                        appCategories?: string[] | undefined;
                        paymentOption?: string | undefined;
                        enabled?: boolean | undefined;
                        refundPolicy?: import("@calcom/lib/payment/types").RefundPolicy | undefined;
                        refundDaysCount?: number | undefined;
                        refundCountCalendarDays?: boolean | undefined;
                    } | undefined;
                    tandemvideo?: {} | undefined;
                    "booking-pages-tag"?: {
                        trackingId: string;
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    "event-type-app-card"?: {
                        isSunrise: boolean;
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    twipla?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        SITE_ID?: string | undefined;
                    } | undefined;
                    umami?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        SITE_ID?: string | undefined;
                        SCRIPT_URL?: string | undefined;
                    } | undefined;
                    vital?: {} | undefined;
                    webex?: {} | undefined;
                    wordpress?: {
                        isSunrise: boolean;
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    zapier?: {} | undefined;
                    "zoho-bigin"?: {
                        enabled?: boolean | undefined;
                        credentialId?: number | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    zohocalendar?: {} | undefined;
                    zohocrm?: {
                        enabled?: boolean | undefined;
                        credentialId?: number | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    zoomvideo?: {} | undefined;
                } | undefined;
            } | null;
            customInputs: {
                id: number;
                type: "TEXT" | "TEXTLONG" | "NUMBER" | "BOOL" | "RADIO" | "PHONE";
                label: string;
                required: boolean;
                eventTypeId: number;
                placeholder: string;
                options?: {
                    type: string;
                    label: string;
                }[] | null | undefined;
                hasToBeCreated?: boolean | undefined;
            }[];
            locations: (Pick<Partial<import("@calcom/app-store/locations").LocationObject>, "link" | "address" | "customLabel"> & Omit<import("@calcom/app-store/locations").LocationObject, "link" | "address" | "customLabel">)[];
            bookingFields: {
                name: string;
                type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
                label?: string | undefined;
                options?: {
                    label: string;
                    value: string;
                }[] | undefined;
                required?: boolean | undefined;
                placeholder?: string | undefined;
                maxLength?: number | undefined;
                defaultLabel?: string | undefined;
                defaultPlaceholder?: string | undefined;
                labelAsSafeHtml?: string | undefined;
                getOptionsAt?: string | undefined;
                optionsInputs?: Record<string, {
                    type: "phone" | "address" | "text";
                    required?: boolean | undefined;
                    placeholder?: string | undefined;
                }> | undefined;
                minLength?: number | undefined;
                excludeEmails?: string | undefined;
                requireEmails?: string | undefined;
                variant?: string | undefined;
                variantsConfig?: {
                    variants: Record<string, {
                        fields: {
                            name: string;
                            type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
                            label?: string | undefined;
                            required?: boolean | undefined;
                            placeholder?: string | undefined;
                            maxLength?: number | undefined;
                            labelAsSafeHtml?: string | undefined;
                            minLength?: number | undefined;
                            excludeEmails?: string | undefined;
                            requireEmails?: string | undefined;
                        }[];
                    }>;
                } | undefined;
                views?: {
                    id: string;
                    label: string;
                    description?: string | undefined;
                }[] | undefined;
                hideWhenJustOneOption?: boolean | undefined;
                hidden?: boolean | undefined;
                editable?: "user" | "system" | "system-but-optional" | "system-but-hidden" | "user-readonly" | undefined;
                sources?: {
                    id: string;
                    type: string;
                    label: string;
                    editUrl?: string | undefined;
                    fieldRequired?: boolean | undefined;
                }[] | undefined;
                disableOnPrefill?: boolean | undefined;
            }[] & import("zod").BRAND<"HAS_SYSTEM_FIELDS">;
            recurringEvent: import("@calcom/types/Calendar").RecurringEvent | null;
            profile: {
                username: string | null | undefined;
                name: string | null;
                weekStart: string;
                image: string;
                brandColor: string | null;
                darkBrandColor: string | null;
                theme: string | null;
                bookerLayouts: {
                    enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
                    defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
                } | null;
            };
            subsetOfUsers: {
                username: string | null;
                name: string | null;
                weekStart: string;
                organizationId: any;
                avatarUrl: string | null;
                profile: import("@calcom/types/UserProfile").UserProfile;
                bookerUrl: string;
            }[];
            users: {
                username: string | null;
                name: string | null;
                weekStart: string;
                organizationId: any;
                avatarUrl: string | null;
                profile: import("@calcom/types/UserProfile").UserProfile;
                bookerUrl: string;
            }[] | undefined;
            entity: {
                logoUrl?: string | undefined;
                name: any;
                fromRedirectOfNonOrgLink: boolean;
                considerUnpublished: boolean;
                orgSlug: string | null;
                teamSlug: string | null;
                hideProfileLink: boolean;
            };
            isDynamic: boolean;
            isInstantEvent: boolean;
            showInstantEventConnectNowModal: boolean;
            instantMeetingParameters: string[];
            aiPhoneCallConfig: {
                id: number;
                eventTypeId: number;
                enabled: boolean;
                templateType: string;
                schedulerName: string | null;
                generalPrompt: string | null;
                yourPhoneNumber: string;
                numberToCall: string;
                guestName: string | null;
                guestEmail: string | null;
                guestCompany: string | null;
                beginMessage: string | null;
                llmId: string | null;
            } | null;
            assignAllTeamMembers: boolean;
            disableCancelling: boolean | null;
            disableRescheduling: boolean | null;
            allowReschedulingCancelledBookings: boolean | null;
            interfaceLanguage: string | null;
            owner: ({
                name: string | null;
                id: number;
                metadata: import(".prisma/client").Prisma.JsonValue;
                username: string | null;
                avatarUrl: string | null;
                weekStart: string;
                theme: string | null;
                defaultScheduleId: number | null;
                brandColor: string | null;
                darkBrandColor: string | null;
                organization: {
                    name: string;
                    id: number;
                    slug: string | null;
                    bannerUrl: string | null;
                } | null;
            } & {
                nonProfileUsername: string | null;
                profile: import("@calcom/types/UserProfile").UserProfile;
            }) | null;
            subsetOfHosts: {
                user: {
                    name: string | null;
                    id: number;
                    metadata: import(".prisma/client").Prisma.JsonValue;
                    username: string | null;
                    avatarUrl: string | null;
                    weekStart: string;
                    theme: string | null;
                    defaultScheduleId: number | null;
                    brandColor: string | null;
                    darkBrandColor: string | null;
                    organization: {
                        name: string;
                        id: number;
                        slug: string | null;
                        bannerUrl: string | null;
                    } | null;
                } & {
                    nonProfileUsername: string | null;
                    profile: import("@calcom/types/UserProfile").UserProfile;
                };
            }[];
            hosts: {
                user: {
                    name: string | null;
                    id: number;
                    metadata: import(".prisma/client").Prisma.JsonValue;
                    username: string | null;
                    avatarUrl: string | null;
                    weekStart: string;
                    theme: string | null;
                    defaultScheduleId: number | null;
                    brandColor: string | null;
                    darkBrandColor: string | null;
                    organization: {
                        name: string;
                        id: number;
                        slug: string | null;
                        bannerUrl: string | null;
                    } | null;
                } & {
                    nonProfileUsername: string | null;
                    profile: import("@calcom/types/UserProfile").UserProfile;
                };
            }[] | undefined;
            id: number;
            length: number;
            title: string;
            team: {
                name: string;
                metadata: import(".prisma/client").Prisma.JsonValue;
                theme: string | null;
                brandColor: string | null;
                darkBrandColor: string | null;
                slug: string | null;
                logoUrl: string | null;
                hideTeamProfileLink: boolean;
                isPrivate: boolean;
                parentId: number | null;
                parent: {
                    name: string;
                    slug: string | null;
                    logoUrl: string | null;
                    bannerUrl: string | null;
                } | null;
            } | null;
            schedule: {
                id: number;
                timeZone: string | null;
            } | null;
            workflows: ({
                workflow: {
                    steps: {
                        id: number;
                        template: import(".prisma/client").$Enums.WorkflowTemplates;
                        action: import(".prisma/client").$Enums.WorkflowActions;
                        stepNumber: number;
                        workflowId: number;
                        sendTo: string | null;
                        reminderBody: string | null;
                        emailSubject: string | null;
                        numberRequired: boolean | null;
                        sender: string | null;
                        numberVerificationPending: boolean;
                        includeCalendarEvent: boolean;
                        verifiedAt: Date | null;
                        agentId: string | null;
                    }[];
                } & {
                    name: string;
                    id: number;
                    time: number | null;
                    userId: number | null;
                    teamId: number | null;
                    position: number;
                    trigger: import(".prisma/client").$Enums.WorkflowTriggerEvents;
                    timeUnit: import(".prisma/client").$Enums.TimeUnit | null;
                    isActiveOnAll: boolean;
                };
            } & {
                id: number;
                eventTypeId: number;
                workflowId: number;
            })[];
            slug: string;
            parent: {
                team: {
                    theme: string | null;
                    brandColor: string | null;
                    darkBrandColor: string | null;
                } | null;
            } | null;
            teamId: number | null;
            hidden: boolean;
            eventName: string | null;
            periodType: import(".prisma/client").$Enums.PeriodType;
            periodStartDate: Date | null;
            periodEndDate: Date | null;
            periodDays: number | null;
            periodCountCalendarDays: boolean | null;
            lockTimeZoneToggleOnBookingPage: boolean;
            lockedTimeZone: string | null;
            requiresConfirmation: boolean;
            requiresBookerEmailVerification: boolean;
            autoTranslateDescriptionEnabled: boolean;
            disableGuests: boolean;
            seatsPerTimeSlot: number | null;
            seatsShowAvailabilityCount: boolean | null;
            schedulingType: import(".prisma/client").$Enums.SchedulingType | null;
            price: number;
            currency: string;
            successRedirectUrl: string | null;
            forwardParamsSuccessRedirect: boolean | null;
            rescheduleWithSameRoundRobinHost: boolean;
            instantMeetingSchedule: {
                id: number;
                timeZone: string | null;
            } | null;
            fieldTranslations: {
                field: import(".prisma/client").$Enums.EventTypeAutoTranslatedField;
                targetLocale: string;
                translatedText: string;
            }[];
        } | null;
    }>;
    ssoConnections: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: {
            connectionExists: null;
        } | {
            connectionExists: boolean;
        };
    }>;
    checkIfUserEmailVerificationRequired: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            email: string;
            userSessionEmail?: string | undefined;
        };
        output: boolean;
    }>;
}>;
