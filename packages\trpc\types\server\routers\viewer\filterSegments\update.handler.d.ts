import type { TUpdateFilterSegmentInputSchema } from "@calcom/lib/server/repository/filterSegment.type";
import type { TrpcSessionUser } from "@calcom/trpc/server/types";
export declare const updateFilterSegmentHandler: ({ ctx, input, }: {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TUpdateFilterSegmentInputSchema;
}) => Promise<import("@calcom/features/data-table/lib/types").FilterSegmentOutput>;
