import { z } from "zod";
export declare const ZCreateInputSchema: z.ZodObject<{
    organizationId: z.Z<PERSON><[z.<PERSON><PERSON><PERSON>, z.ZodNull]>;
    name: z.ZodString;
    provider: z.ZodString;
}, "strip", z.<PERSON>ny, {
    name: string;
    organizationId: number | null;
    provider: string;
}, {
    name: string;
    organizationId: number | null;
    provider: string;
}>;
export type ZCreateInputSchema = z.infer<typeof ZCreateInputSchema>;
