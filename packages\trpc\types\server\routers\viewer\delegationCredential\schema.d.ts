import { z } from "zod";
export declare const DelegationCredentialCreateSchema: z.ZodObject<{
    workspacePlatformSlug: z.ZodString;
    domain: z.ZodString;
    serviceAccountKey: z.ZodObject<{
        private_key: z.ZodString;
        client_email: z.<PERSON>odOptional<z.ZodString>;
        client_id: z.ZodString;
        tenant_id: z.ZodOptional<z.ZodString>;
    }, "passthrough", z.ZodType<PERSON>ny, z.objectOutputType<{
        private_key: z.ZodString;
        client_email: z.ZodOptional<z.ZodString>;
        client_id: z.ZodString;
        tenant_id: z.ZodOptional<z.ZodString>;
    }, z.<PERSON>odType<PERSON>ny, "passthrough">, z.objectInputType<{
        private_key: z.ZodString;
        client_email: z.ZodOptional<z.ZodString>;
        client_id: z.ZodString;
        tenant_id: z.ZodOptional<z.ZodString>;
    }, z.<PERSON><PERSON><PERSON>ny, "passthrough">>;
}, "strip", z.<PERSON>od<PERSON>ype<PERSON>ny, {
    domain: string;
    serviceAccountKey: {
        client_id: string;
        private_key: string;
        client_email?: string | undefined;
        tenant_id?: string | undefined;
    } & {
        [k: string]: unknown;
    };
    workspacePlatformSlug: string;
}, {
    domain: string;
    serviceAccountKey: {
        client_id: string;
        private_key: string;
        client_email?: string | undefined;
        tenant_id?: string | undefined;
    } & {
        [k: string]: unknown;
    };
    workspacePlatformSlug: string;
}>;
export declare const DelegationCredentialUpdateSchema: z.ZodObject<{
    id: z.ZodString;
    workspacePlatformSlug: z.ZodString;
    domain: z.ZodString;
}, "strip", z.ZodTypeAny, {
    id: string;
    domain: string;
    workspacePlatformSlug: string;
}, {
    id: string;
    domain: string;
    workspacePlatformSlug: string;
}>;
export declare const DelegationCredentialToggleEnabledSchema: z.ZodObject<{
    id: z.ZodString;
    enabled: z.ZodBoolean;
}, "strip", z.ZodTypeAny, {
    id: string;
    enabled: boolean;
}, {
    id: string;
    enabled: boolean;
}>;
export declare const DelegationCredentialDeleteSchema: z.ZodObject<{
    id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    id: string;
}, {
    id: string;
}>;
export declare const DelegationCredentialGetAffectedMembersForDisableSchema: z.ZodObject<{
    id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    id: string;
}, {
    id: string;
}>;
export type TDelegationCredentialGetAffectedMembersForDisableSchema = z.infer<typeof DelegationCredentialGetAffectedMembersForDisableSchema>;
