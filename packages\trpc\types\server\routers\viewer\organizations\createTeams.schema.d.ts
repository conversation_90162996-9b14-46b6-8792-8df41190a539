import { z } from "zod";
export declare const ZCreateTeamsSchema: z.ZodObject<{
    teamNames: z.<PERSON><z.ZodString, "many">;
    orgId: z.ZodNumber;
    moveTeams: z.<PERSON>y<z.ZodObject<{
        id: z.ZodNumber;
        newSlug: z.ZodNullable<z.ZodString>;
        shouldMove: z.ZodBoolean;
    }, "strip", z.ZodTypeAny, {
        id: number;
        newSlug: string | null;
        shouldMove: boolean;
    }, {
        id: number;
        newSlug: string | null;
        shouldMove: boolean;
    }>, "many">;
    creationSource: z.Zod<PERSON>num<{
        readonly API_V1: "API_V1";
        readonly API_V2: "API_V2";
        readonly WEBAPP: "WEBAPP";
    }>;
}, "strip", z.ZodTypeAny, {
    creationSource: "API_V1" | "API_V2" | "WEBAPP";
    orgId: number;
    teamNames: string[];
    moveTeams: {
        id: number;
        newSlug: string | null;
        shouldMove: boolean;
    }[];
}, {
    creationSource: "API_V1" | "API_V2" | "WEBAPP";
    orgId: number;
    teamNames: string[];
    moveTeams: {
        id: number;
        newSlug: string | null;
        shouldMove: boolean;
    }[];
}>;
export type TCreateTeamsSchema = z.infer<typeof ZCreateTeamsSchema>;
