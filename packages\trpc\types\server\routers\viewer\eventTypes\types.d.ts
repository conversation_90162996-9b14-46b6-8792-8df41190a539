import { z } from "zod";
/** Optional fields */
declare const BaseEventTypeUpdateInput: z.ZodObject<{
    users: z.ZodOptional<z.<PERSON>od<PERSON>y<z.ZodUnion<[z.ZodEffects<z.ZodString, number, string>, z.<PERSON>]>, "many">>;
    children: z.<PERSON><z.<PERSON><z.ZodObject<{
        owner: z.ZodObject<{
            id: z.ZodNumber;
            name: z.ZodString;
            email: z.ZodString;
            eventTypeSlugs: z.<PERSON><PERSON><z.ZodString, "many">;
        }, "strip", z.ZodTypeAny, {
            name: string;
            id: number;
            email: string;
            eventTypeSlugs: string[];
        }, {
            name: string;
            id: number;
            email: string;
            eventTypeSlugs: string[];
        }>;
        hidden: z.ZodBoolean;
    }, "strip", z.ZodTypeAny, {
        hidden: boolean;
        owner: {
            name: string;
            id: number;
            email: string;
            eventTypeSlugs: string[];
        };
    }, {
        hidden: boolean;
        owner: {
            name: string;
            id: number;
            email: string;
            eventTypeSlugs: string[];
        };
    }>, "many">>;
    length: z.<PERSON>od<PERSON>ptional<z.<PERSON><PERSON>Number>;
    title: z.ZodOptional<z.ZodString>;
    metadata: z.ZodOptional<z.ZodNullable<z.ZodObject<{
        config: z.ZodOptional<z.ZodObject<{
            useHostSchedulesForTeamEvent: z.ZodOptional<z.ZodBoolean>;
        }, "strip", z.ZodTypeAny, {
            useHostSchedulesForTeamEvent?: boolean | undefined;
        }, {
            useHostSchedulesForTeamEvent?: boolean | undefined;
        }>>;
        smartContractAddress: z.ZodOptional<z.ZodString>;
        blockchainId: z.ZodOptional<z.ZodNumber>;
        multipleDuration: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        giphyThankYouPage: z.ZodOptional<z.ZodString>;
        additionalNotesRequired: z.ZodOptional<z.ZodBoolean>;
        disableSuccessPage: z.ZodOptional<z.ZodBoolean>;
        disableStandardEmails: z.ZodOptional<z.ZodObject<{
            all: z.ZodOptional<z.ZodObject<{
                host: z.ZodOptional<z.ZodBoolean>;
                attendee: z.ZodOptional<z.ZodBoolean>;
            }, "strip", z.ZodTypeAny, {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            }, {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            }>>;
            confirmation: z.ZodOptional<z.ZodObject<{
                host: z.ZodOptional<z.ZodBoolean>;
                attendee: z.ZodOptional<z.ZodBoolean>;
            }, "strip", z.ZodTypeAny, {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            }, {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            }>>;
        }, "strip", z.ZodTypeAny, {
            all?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
            confirmation?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
        }, {
            all?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
            confirmation?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
        }>>;
        managedEventConfig: z.ZodOptional<z.ZodObject<{
            unlockedFields: z.ZodOptional<z.ZodType<{
                users?: true | undefined;
                children?: true | undefined;
                length?: true | undefined;
                title?: true | undefined;
                metadata?: true | undefined;
                description?: true | undefined;
                userId?: true | undefined;
                calVideoSettings?: true | undefined;
                destinationCalendar?: true | undefined;
                profile?: true | undefined;
                team?: true | undefined;
                schedule?: true | undefined;
                availability?: true | undefined;
                hashedLink?: true | undefined;
                secondaryEmail?: true | undefined;
                customInputs?: true | undefined;
                timeZone?: true | undefined;
                bookings?: true | undefined;
                selectedCalendars?: true | undefined;
                webhooks?: true | undefined;
                workflows?: true | undefined;
                hosts?: true | undefined;
                slug?: true | undefined;
                parentId?: true | undefined;
                bookingLimits?: true | undefined;
                parent?: true | undefined;
                teamId?: true | undefined;
                hidden?: true | undefined;
                _count?: true | undefined;
                interfaceLanguage?: true | undefined;
                position?: true | undefined;
                locations?: true | undefined;
                offsetStart?: true | undefined;
                profileId?: true | undefined;
                useEventLevelSelectedCalendars?: true | undefined;
                eventName?: true | undefined;
                bookingFields?: true | undefined;
                periodType?: true | undefined;
                periodStartDate?: true | undefined;
                periodEndDate?: true | undefined;
                periodDays?: true | undefined;
                periodCountCalendarDays?: true | undefined;
                lockTimeZoneToggleOnBookingPage?: true | undefined;
                lockedTimeZone?: true | undefined;
                requiresConfirmation?: true | undefined;
                requiresConfirmationWillBlockSlot?: true | undefined;
                requiresConfirmationForFreeEmail?: true | undefined;
                requiresBookerEmailVerification?: true | undefined;
                canSendCalVideoTranscriptionEmails?: true | undefined;
                autoTranslateDescriptionEnabled?: true | undefined;
                recurringEvent?: true | undefined;
                disableGuests?: true | undefined;
                hideCalendarNotes?: true | undefined;
                hideCalendarEventDetails?: true | undefined;
                minimumBookingNotice?: true | undefined;
                beforeEventBuffer?: true | undefined;
                afterEventBuffer?: true | undefined;
                seatsPerTimeSlot?: true | undefined;
                onlyShowFirstAvailableSlot?: true | undefined;
                disableCancelling?: true | undefined;
                disableRescheduling?: true | undefined;
                seatsShowAttendees?: true | undefined;
                seatsShowAvailabilityCount?: true | undefined;
                schedulingType?: true | undefined;
                scheduleId?: true | undefined;
                allowReschedulingCancelledBookings?: true | undefined;
                price?: true | undefined;
                currency?: true | undefined;
                slotInterval?: true | undefined;
                successRedirectUrl?: true | undefined;
                forwardParamsSuccessRedirect?: true | undefined;
                durationLimits?: true | undefined;
                isInstantEvent?: true | undefined;
                instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                instantMeetingScheduleId?: true | undefined;
                instantMeetingParameters?: true | undefined;
                assignAllTeamMembers?: true | undefined;
                assignRRMembersUsingSegment?: true | undefined;
                rrSegmentQueryValue?: true | undefined;
                useEventTypeDestinationCalendarEmail?: true | undefined;
                isRRWeightsEnabled?: true | undefined;
                maxLeadThreshold?: true | undefined;
                includeNoShowInRRCalculation?: true | undefined;
                allowReschedulingPastBookings?: true | undefined;
                hideOrganizerEmail?: true | undefined;
                maxActiveBookingsPerBooker?: true | undefined;
                maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                customReplyToEmail?: true | undefined;
                eventTypeColor?: true | undefined;
                rescheduleWithSameRoundRobinHost?: true | undefined;
                secondaryEmailId?: true | undefined;
                useBookerTimezone?: true | undefined;
                restrictionScheduleId?: true | undefined;
                bookingRequiresAuthentication?: true | undefined;
                owner?: true | undefined;
                instantMeetingSchedule?: true | undefined;
                aiPhoneCallConfig?: true | undefined;
                fieldTranslations?: true | undefined;
                restrictionSchedule?: true | undefined;
                hostGroups?: true | undefined;
            }, z.ZodTypeDef, {
                users?: true | undefined;
                children?: true | undefined;
                length?: true | undefined;
                title?: true | undefined;
                metadata?: true | undefined;
                description?: true | undefined;
                userId?: true | undefined;
                calVideoSettings?: true | undefined;
                destinationCalendar?: true | undefined;
                profile?: true | undefined;
                team?: true | undefined;
                schedule?: true | undefined;
                availability?: true | undefined;
                hashedLink?: true | undefined;
                secondaryEmail?: true | undefined;
                customInputs?: true | undefined;
                timeZone?: true | undefined;
                bookings?: true | undefined;
                selectedCalendars?: true | undefined;
                webhooks?: true | undefined;
                workflows?: true | undefined;
                hosts?: true | undefined;
                slug?: true | undefined;
                parentId?: true | undefined;
                bookingLimits?: true | undefined;
                parent?: true | undefined;
                teamId?: true | undefined;
                hidden?: true | undefined;
                _count?: true | undefined;
                interfaceLanguage?: true | undefined;
                position?: true | undefined;
                locations?: true | undefined;
                offsetStart?: true | undefined;
                profileId?: true | undefined;
                useEventLevelSelectedCalendars?: true | undefined;
                eventName?: true | undefined;
                bookingFields?: true | undefined;
                periodType?: true | undefined;
                periodStartDate?: true | undefined;
                periodEndDate?: true | undefined;
                periodDays?: true | undefined;
                periodCountCalendarDays?: true | undefined;
                lockTimeZoneToggleOnBookingPage?: true | undefined;
                lockedTimeZone?: true | undefined;
                requiresConfirmation?: true | undefined;
                requiresConfirmationWillBlockSlot?: true | undefined;
                requiresConfirmationForFreeEmail?: true | undefined;
                requiresBookerEmailVerification?: true | undefined;
                canSendCalVideoTranscriptionEmails?: true | undefined;
                autoTranslateDescriptionEnabled?: true | undefined;
                recurringEvent?: true | undefined;
                disableGuests?: true | undefined;
                hideCalendarNotes?: true | undefined;
                hideCalendarEventDetails?: true | undefined;
                minimumBookingNotice?: true | undefined;
                beforeEventBuffer?: true | undefined;
                afterEventBuffer?: true | undefined;
                seatsPerTimeSlot?: true | undefined;
                onlyShowFirstAvailableSlot?: true | undefined;
                disableCancelling?: true | undefined;
                disableRescheduling?: true | undefined;
                seatsShowAttendees?: true | undefined;
                seatsShowAvailabilityCount?: true | undefined;
                schedulingType?: true | undefined;
                scheduleId?: true | undefined;
                allowReschedulingCancelledBookings?: true | undefined;
                price?: true | undefined;
                currency?: true | undefined;
                slotInterval?: true | undefined;
                successRedirectUrl?: true | undefined;
                forwardParamsSuccessRedirect?: true | undefined;
                durationLimits?: true | undefined;
                isInstantEvent?: true | undefined;
                instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                instantMeetingScheduleId?: true | undefined;
                instantMeetingParameters?: true | undefined;
                assignAllTeamMembers?: true | undefined;
                assignRRMembersUsingSegment?: true | undefined;
                rrSegmentQueryValue?: true | undefined;
                useEventTypeDestinationCalendarEmail?: true | undefined;
                isRRWeightsEnabled?: true | undefined;
                maxLeadThreshold?: true | undefined;
                includeNoShowInRRCalculation?: true | undefined;
                allowReschedulingPastBookings?: true | undefined;
                hideOrganizerEmail?: true | undefined;
                maxActiveBookingsPerBooker?: true | undefined;
                maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                customReplyToEmail?: true | undefined;
                eventTypeColor?: true | undefined;
                rescheduleWithSameRoundRobinHost?: true | undefined;
                secondaryEmailId?: true | undefined;
                useBookerTimezone?: true | undefined;
                restrictionScheduleId?: true | undefined;
                bookingRequiresAuthentication?: true | undefined;
                owner?: true | undefined;
                instantMeetingSchedule?: true | undefined;
                aiPhoneCallConfig?: true | undefined;
                fieldTranslations?: true | undefined;
                restrictionSchedule?: true | undefined;
                hostGroups?: true | undefined;
            }>>;
        }, "strip", z.ZodTypeAny, {
            unlockedFields?: {
                users?: true | undefined;
                children?: true | undefined;
                length?: true | undefined;
                title?: true | undefined;
                metadata?: true | undefined;
                description?: true | undefined;
                userId?: true | undefined;
                calVideoSettings?: true | undefined;
                destinationCalendar?: true | undefined;
                profile?: true | undefined;
                team?: true | undefined;
                schedule?: true | undefined;
                availability?: true | undefined;
                hashedLink?: true | undefined;
                secondaryEmail?: true | undefined;
                customInputs?: true | undefined;
                timeZone?: true | undefined;
                bookings?: true | undefined;
                selectedCalendars?: true | undefined;
                webhooks?: true | undefined;
                workflows?: true | undefined;
                hosts?: true | undefined;
                slug?: true | undefined;
                parentId?: true | undefined;
                bookingLimits?: true | undefined;
                parent?: true | undefined;
                teamId?: true | undefined;
                hidden?: true | undefined;
                _count?: true | undefined;
                interfaceLanguage?: true | undefined;
                position?: true | undefined;
                locations?: true | undefined;
                offsetStart?: true | undefined;
                profileId?: true | undefined;
                useEventLevelSelectedCalendars?: true | undefined;
                eventName?: true | undefined;
                bookingFields?: true | undefined;
                periodType?: true | undefined;
                periodStartDate?: true | undefined;
                periodEndDate?: true | undefined;
                periodDays?: true | undefined;
                periodCountCalendarDays?: true | undefined;
                lockTimeZoneToggleOnBookingPage?: true | undefined;
                lockedTimeZone?: true | undefined;
                requiresConfirmation?: true | undefined;
                requiresConfirmationWillBlockSlot?: true | undefined;
                requiresConfirmationForFreeEmail?: true | undefined;
                requiresBookerEmailVerification?: true | undefined;
                canSendCalVideoTranscriptionEmails?: true | undefined;
                autoTranslateDescriptionEnabled?: true | undefined;
                recurringEvent?: true | undefined;
                disableGuests?: true | undefined;
                hideCalendarNotes?: true | undefined;
                hideCalendarEventDetails?: true | undefined;
                minimumBookingNotice?: true | undefined;
                beforeEventBuffer?: true | undefined;
                afterEventBuffer?: true | undefined;
                seatsPerTimeSlot?: true | undefined;
                onlyShowFirstAvailableSlot?: true | undefined;
                disableCancelling?: true | undefined;
                disableRescheduling?: true | undefined;
                seatsShowAttendees?: true | undefined;
                seatsShowAvailabilityCount?: true | undefined;
                schedulingType?: true | undefined;
                scheduleId?: true | undefined;
                allowReschedulingCancelledBookings?: true | undefined;
                price?: true | undefined;
                currency?: true | undefined;
                slotInterval?: true | undefined;
                successRedirectUrl?: true | undefined;
                forwardParamsSuccessRedirect?: true | undefined;
                durationLimits?: true | undefined;
                isInstantEvent?: true | undefined;
                instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                instantMeetingScheduleId?: true | undefined;
                instantMeetingParameters?: true | undefined;
                assignAllTeamMembers?: true | undefined;
                assignRRMembersUsingSegment?: true | undefined;
                rrSegmentQueryValue?: true | undefined;
                useEventTypeDestinationCalendarEmail?: true | undefined;
                isRRWeightsEnabled?: true | undefined;
                maxLeadThreshold?: true | undefined;
                includeNoShowInRRCalculation?: true | undefined;
                allowReschedulingPastBookings?: true | undefined;
                hideOrganizerEmail?: true | undefined;
                maxActiveBookingsPerBooker?: true | undefined;
                maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                customReplyToEmail?: true | undefined;
                eventTypeColor?: true | undefined;
                rescheduleWithSameRoundRobinHost?: true | undefined;
                secondaryEmailId?: true | undefined;
                useBookerTimezone?: true | undefined;
                restrictionScheduleId?: true | undefined;
                bookingRequiresAuthentication?: true | undefined;
                owner?: true | undefined;
                instantMeetingSchedule?: true | undefined;
                aiPhoneCallConfig?: true | undefined;
                fieldTranslations?: true | undefined;
                restrictionSchedule?: true | undefined;
                hostGroups?: true | undefined;
            } | undefined;
        }, {
            unlockedFields?: {
                users?: true | undefined;
                children?: true | undefined;
                length?: true | undefined;
                title?: true | undefined;
                metadata?: true | undefined;
                description?: true | undefined;
                userId?: true | undefined;
                calVideoSettings?: true | undefined;
                destinationCalendar?: true | undefined;
                profile?: true | undefined;
                team?: true | undefined;
                schedule?: true | undefined;
                availability?: true | undefined;
                hashedLink?: true | undefined;
                secondaryEmail?: true | undefined;
                customInputs?: true | undefined;
                timeZone?: true | undefined;
                bookings?: true | undefined;
                selectedCalendars?: true | undefined;
                webhooks?: true | undefined;
                workflows?: true | undefined;
                hosts?: true | undefined;
                slug?: true | undefined;
                parentId?: true | undefined;
                bookingLimits?: true | undefined;
                parent?: true | undefined;
                teamId?: true | undefined;
                hidden?: true | undefined;
                _count?: true | undefined;
                interfaceLanguage?: true | undefined;
                position?: true | undefined;
                locations?: true | undefined;
                offsetStart?: true | undefined;
                profileId?: true | undefined;
                useEventLevelSelectedCalendars?: true | undefined;
                eventName?: true | undefined;
                bookingFields?: true | undefined;
                periodType?: true | undefined;
                periodStartDate?: true | undefined;
                periodEndDate?: true | undefined;
                periodDays?: true | undefined;
                periodCountCalendarDays?: true | undefined;
                lockTimeZoneToggleOnBookingPage?: true | undefined;
                lockedTimeZone?: true | undefined;
                requiresConfirmation?: true | undefined;
                requiresConfirmationWillBlockSlot?: true | undefined;
                requiresConfirmationForFreeEmail?: true | undefined;
                requiresBookerEmailVerification?: true | undefined;
                canSendCalVideoTranscriptionEmails?: true | undefined;
                autoTranslateDescriptionEnabled?: true | undefined;
                recurringEvent?: true | undefined;
                disableGuests?: true | undefined;
                hideCalendarNotes?: true | undefined;
                hideCalendarEventDetails?: true | undefined;
                minimumBookingNotice?: true | undefined;
                beforeEventBuffer?: true | undefined;
                afterEventBuffer?: true | undefined;
                seatsPerTimeSlot?: true | undefined;
                onlyShowFirstAvailableSlot?: true | undefined;
                disableCancelling?: true | undefined;
                disableRescheduling?: true | undefined;
                seatsShowAttendees?: true | undefined;
                seatsShowAvailabilityCount?: true | undefined;
                schedulingType?: true | undefined;
                scheduleId?: true | undefined;
                allowReschedulingCancelledBookings?: true | undefined;
                price?: true | undefined;
                currency?: true | undefined;
                slotInterval?: true | undefined;
                successRedirectUrl?: true | undefined;
                forwardParamsSuccessRedirect?: true | undefined;
                durationLimits?: true | undefined;
                isInstantEvent?: true | undefined;
                instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                instantMeetingScheduleId?: true | undefined;
                instantMeetingParameters?: true | undefined;
                assignAllTeamMembers?: true | undefined;
                assignRRMembersUsingSegment?: true | undefined;
                rrSegmentQueryValue?: true | undefined;
                useEventTypeDestinationCalendarEmail?: true | undefined;
                isRRWeightsEnabled?: true | undefined;
                maxLeadThreshold?: true | undefined;
                includeNoShowInRRCalculation?: true | undefined;
                allowReschedulingPastBookings?: true | undefined;
                hideOrganizerEmail?: true | undefined;
                maxActiveBookingsPerBooker?: true | undefined;
                maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                customReplyToEmail?: true | undefined;
                eventTypeColor?: true | undefined;
                rescheduleWithSameRoundRobinHost?: true | undefined;
                secondaryEmailId?: true | undefined;
                useBookerTimezone?: true | undefined;
                restrictionScheduleId?: true | undefined;
                bookingRequiresAuthentication?: true | undefined;
                owner?: true | undefined;
                instantMeetingSchedule?: true | undefined;
                aiPhoneCallConfig?: true | undefined;
                fieldTranslations?: true | undefined;
                restrictionSchedule?: true | undefined;
                hostGroups?: true | undefined;
            } | undefined;
        }>>;
        requiresConfirmationThreshold: z.ZodOptional<z.ZodObject<{
            time: z.ZodNumber;
            unit: z.ZodType<import("dayjs").UnitTypeLongPlural, z.ZodTypeDef, import("dayjs").UnitTypeLongPlural>;
        }, "strip", z.ZodTypeAny, {
            time: number;
            unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
        }, {
            time: number;
            unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
        }>>;
        bookerLayouts: z.ZodOptional<z.ZodNullable<z.ZodObject<{
            enabledLayouts: z.ZodArray<z.ZodUnion<[z.ZodLiteral<import("@calcom/prisma/zod-utils").BookerLayouts>, z.ZodLiteral<import("@calcom/prisma/zod-utils").BookerLayouts>, z.ZodLiteral<import("@calcom/prisma/zod-utils").BookerLayouts>]>, "many">;
            defaultLayout: z.ZodUnion<[z.ZodLiteral<import("@calcom/prisma/zod-utils").BookerLayouts>, z.ZodLiteral<import("@calcom/prisma/zod-utils").BookerLayouts>, z.ZodLiteral<import("@calcom/prisma/zod-utils").BookerLayouts>]>;
        }, "strip", z.ZodTypeAny, {
            enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
            defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
        }, {
            enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
            defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
        }>>>;
        apps: z.ZodOptional<z.ZodUnknown>;
    }, "strip", z.ZodTypeAny, {
        config?: {
            useHostSchedulesForTeamEvent?: boolean | undefined;
        } | undefined;
        smartContractAddress?: string | undefined;
        blockchainId?: number | undefined;
        multipleDuration?: number[] | undefined;
        giphyThankYouPage?: string | undefined;
        additionalNotesRequired?: boolean | undefined;
        disableSuccessPage?: boolean | undefined;
        disableStandardEmails?: {
            all?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
            confirmation?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
        } | undefined;
        managedEventConfig?: {
            unlockedFields?: {
                users?: true | undefined;
                children?: true | undefined;
                length?: true | undefined;
                title?: true | undefined;
                metadata?: true | undefined;
                description?: true | undefined;
                userId?: true | undefined;
                calVideoSettings?: true | undefined;
                destinationCalendar?: true | undefined;
                profile?: true | undefined;
                team?: true | undefined;
                schedule?: true | undefined;
                availability?: true | undefined;
                hashedLink?: true | undefined;
                secondaryEmail?: true | undefined;
                customInputs?: true | undefined;
                timeZone?: true | undefined;
                bookings?: true | undefined;
                selectedCalendars?: true | undefined;
                webhooks?: true | undefined;
                workflows?: true | undefined;
                hosts?: true | undefined;
                slug?: true | undefined;
                parentId?: true | undefined;
                bookingLimits?: true | undefined;
                parent?: true | undefined;
                teamId?: true | undefined;
                hidden?: true | undefined;
                _count?: true | undefined;
                interfaceLanguage?: true | undefined;
                position?: true | undefined;
                locations?: true | undefined;
                offsetStart?: true | undefined;
                profileId?: true | undefined;
                useEventLevelSelectedCalendars?: true | undefined;
                eventName?: true | undefined;
                bookingFields?: true | undefined;
                periodType?: true | undefined;
                periodStartDate?: true | undefined;
                periodEndDate?: true | undefined;
                periodDays?: true | undefined;
                periodCountCalendarDays?: true | undefined;
                lockTimeZoneToggleOnBookingPage?: true | undefined;
                lockedTimeZone?: true | undefined;
                requiresConfirmation?: true | undefined;
                requiresConfirmationWillBlockSlot?: true | undefined;
                requiresConfirmationForFreeEmail?: true | undefined;
                requiresBookerEmailVerification?: true | undefined;
                canSendCalVideoTranscriptionEmails?: true | undefined;
                autoTranslateDescriptionEnabled?: true | undefined;
                recurringEvent?: true | undefined;
                disableGuests?: true | undefined;
                hideCalendarNotes?: true | undefined;
                hideCalendarEventDetails?: true | undefined;
                minimumBookingNotice?: true | undefined;
                beforeEventBuffer?: true | undefined;
                afterEventBuffer?: true | undefined;
                seatsPerTimeSlot?: true | undefined;
                onlyShowFirstAvailableSlot?: true | undefined;
                disableCancelling?: true | undefined;
                disableRescheduling?: true | undefined;
                seatsShowAttendees?: true | undefined;
                seatsShowAvailabilityCount?: true | undefined;
                schedulingType?: true | undefined;
                scheduleId?: true | undefined;
                allowReschedulingCancelledBookings?: true | undefined;
                price?: true | undefined;
                currency?: true | undefined;
                slotInterval?: true | undefined;
                successRedirectUrl?: true | undefined;
                forwardParamsSuccessRedirect?: true | undefined;
                durationLimits?: true | undefined;
                isInstantEvent?: true | undefined;
                instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                instantMeetingScheduleId?: true | undefined;
                instantMeetingParameters?: true | undefined;
                assignAllTeamMembers?: true | undefined;
                assignRRMembersUsingSegment?: true | undefined;
                rrSegmentQueryValue?: true | undefined;
                useEventTypeDestinationCalendarEmail?: true | undefined;
                isRRWeightsEnabled?: true | undefined;
                maxLeadThreshold?: true | undefined;
                includeNoShowInRRCalculation?: true | undefined;
                allowReschedulingPastBookings?: true | undefined;
                hideOrganizerEmail?: true | undefined;
                maxActiveBookingsPerBooker?: true | undefined;
                maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                customReplyToEmail?: true | undefined;
                eventTypeColor?: true | undefined;
                rescheduleWithSameRoundRobinHost?: true | undefined;
                secondaryEmailId?: true | undefined;
                useBookerTimezone?: true | undefined;
                restrictionScheduleId?: true | undefined;
                bookingRequiresAuthentication?: true | undefined;
                owner?: true | undefined;
                instantMeetingSchedule?: true | undefined;
                aiPhoneCallConfig?: true | undefined;
                fieldTranslations?: true | undefined;
                restrictionSchedule?: true | undefined;
                hostGroups?: true | undefined;
            } | undefined;
        } | undefined;
        requiresConfirmationThreshold?: {
            time: number;
            unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
        } | undefined;
        bookerLayouts?: {
            enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
            defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
        } | null | undefined;
        apps?: unknown;
    }, {
        config?: {
            useHostSchedulesForTeamEvent?: boolean | undefined;
        } | undefined;
        smartContractAddress?: string | undefined;
        blockchainId?: number | undefined;
        multipleDuration?: number[] | undefined;
        giphyThankYouPage?: string | undefined;
        additionalNotesRequired?: boolean | undefined;
        disableSuccessPage?: boolean | undefined;
        disableStandardEmails?: {
            all?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
            confirmation?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
        } | undefined;
        managedEventConfig?: {
            unlockedFields?: {
                users?: true | undefined;
                children?: true | undefined;
                length?: true | undefined;
                title?: true | undefined;
                metadata?: true | undefined;
                description?: true | undefined;
                userId?: true | undefined;
                calVideoSettings?: true | undefined;
                destinationCalendar?: true | undefined;
                profile?: true | undefined;
                team?: true | undefined;
                schedule?: true | undefined;
                availability?: true | undefined;
                hashedLink?: true | undefined;
                secondaryEmail?: true | undefined;
                customInputs?: true | undefined;
                timeZone?: true | undefined;
                bookings?: true | undefined;
                selectedCalendars?: true | undefined;
                webhooks?: true | undefined;
                workflows?: true | undefined;
                hosts?: true | undefined;
                slug?: true | undefined;
                parentId?: true | undefined;
                bookingLimits?: true | undefined;
                parent?: true | undefined;
                teamId?: true | undefined;
                hidden?: true | undefined;
                _count?: true | undefined;
                interfaceLanguage?: true | undefined;
                position?: true | undefined;
                locations?: true | undefined;
                offsetStart?: true | undefined;
                profileId?: true | undefined;
                useEventLevelSelectedCalendars?: true | undefined;
                eventName?: true | undefined;
                bookingFields?: true | undefined;
                periodType?: true | undefined;
                periodStartDate?: true | undefined;
                periodEndDate?: true | undefined;
                periodDays?: true | undefined;
                periodCountCalendarDays?: true | undefined;
                lockTimeZoneToggleOnBookingPage?: true | undefined;
                lockedTimeZone?: true | undefined;
                requiresConfirmation?: true | undefined;
                requiresConfirmationWillBlockSlot?: true | undefined;
                requiresConfirmationForFreeEmail?: true | undefined;
                requiresBookerEmailVerification?: true | undefined;
                canSendCalVideoTranscriptionEmails?: true | undefined;
                autoTranslateDescriptionEnabled?: true | undefined;
                recurringEvent?: true | undefined;
                disableGuests?: true | undefined;
                hideCalendarNotes?: true | undefined;
                hideCalendarEventDetails?: true | undefined;
                minimumBookingNotice?: true | undefined;
                beforeEventBuffer?: true | undefined;
                afterEventBuffer?: true | undefined;
                seatsPerTimeSlot?: true | undefined;
                onlyShowFirstAvailableSlot?: true | undefined;
                disableCancelling?: true | undefined;
                disableRescheduling?: true | undefined;
                seatsShowAttendees?: true | undefined;
                seatsShowAvailabilityCount?: true | undefined;
                schedulingType?: true | undefined;
                scheduleId?: true | undefined;
                allowReschedulingCancelledBookings?: true | undefined;
                price?: true | undefined;
                currency?: true | undefined;
                slotInterval?: true | undefined;
                successRedirectUrl?: true | undefined;
                forwardParamsSuccessRedirect?: true | undefined;
                durationLimits?: true | undefined;
                isInstantEvent?: true | undefined;
                instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                instantMeetingScheduleId?: true | undefined;
                instantMeetingParameters?: true | undefined;
                assignAllTeamMembers?: true | undefined;
                assignRRMembersUsingSegment?: true | undefined;
                rrSegmentQueryValue?: true | undefined;
                useEventTypeDestinationCalendarEmail?: true | undefined;
                isRRWeightsEnabled?: true | undefined;
                maxLeadThreshold?: true | undefined;
                includeNoShowInRRCalculation?: true | undefined;
                allowReschedulingPastBookings?: true | undefined;
                hideOrganizerEmail?: true | undefined;
                maxActiveBookingsPerBooker?: true | undefined;
                maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                customReplyToEmail?: true | undefined;
                eventTypeColor?: true | undefined;
                rescheduleWithSameRoundRobinHost?: true | undefined;
                secondaryEmailId?: true | undefined;
                useBookerTimezone?: true | undefined;
                restrictionScheduleId?: true | undefined;
                bookingRequiresAuthentication?: true | undefined;
                owner?: true | undefined;
                instantMeetingSchedule?: true | undefined;
                aiPhoneCallConfig?: true | undefined;
                fieldTranslations?: true | undefined;
                restrictionSchedule?: true | undefined;
                hostGroups?: true | undefined;
            } | undefined;
        } | undefined;
        requiresConfirmationThreshold?: {
            time: number;
            unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
        } | undefined;
        bookerLayouts?: {
            enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
            defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
        } | null | undefined;
        apps?: unknown;
    }>>>;
    description: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodString>>>;
    userId: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodNumber>>>;
    calVideoSettings: z.ZodOptional<z.ZodNullable<z.ZodOptional<z.ZodObject<{
        disableRecordingForGuests: z.ZodOptional<z.ZodNullable<z.ZodBoolean>>;
        disableRecordingForOrganizer: z.ZodOptional<z.ZodNullable<z.ZodBoolean>>;
        enableAutomaticTranscription: z.ZodOptional<z.ZodNullable<z.ZodBoolean>>;
        enableAutomaticRecordingForOrganizer: z.ZodOptional<z.ZodNullable<z.ZodBoolean>>;
        disableTranscriptionForGuests: z.ZodOptional<z.ZodNullable<z.ZodBoolean>>;
        disableTranscriptionForOrganizer: z.ZodOptional<z.ZodNullable<z.ZodBoolean>>;
        redirectUrlOnExit: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    }, "strip", z.ZodTypeAny, {
        disableRecordingForGuests?: boolean | null | undefined;
        disableRecordingForOrganizer?: boolean | null | undefined;
        enableAutomaticTranscription?: boolean | null | undefined;
        enableAutomaticRecordingForOrganizer?: boolean | null | undefined;
        disableTranscriptionForGuests?: boolean | null | undefined;
        disableTranscriptionForOrganizer?: boolean | null | undefined;
        redirectUrlOnExit?: string | null | undefined;
    }, {
        disableRecordingForGuests?: boolean | null | undefined;
        disableRecordingForOrganizer?: boolean | null | undefined;
        enableAutomaticTranscription?: boolean | null | undefined;
        enableAutomaticRecordingForOrganizer?: boolean | null | undefined;
        disableTranscriptionForGuests?: boolean | null | undefined;
        disableTranscriptionForOrganizer?: boolean | null | undefined;
        redirectUrlOnExit?: string | null | undefined;
    }>>>>;
    destinationCalendar: z.ZodOptional<z.ZodNullable<z.ZodObject<Pick<{
        id: z.ZodNumber;
        integration: z.ZodString;
        externalId: z.ZodString;
        primaryEmail: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        userId: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
        eventTypeId: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
        credentialId: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
        createdAt: z.ZodOptional<z.ZodNullable<z.ZodDate>>;
        updatedAt: z.ZodOptional<z.ZodNullable<z.ZodDate>>;
        delegationCredentialId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        domainWideDelegationCredentialId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    }, "integration" | "externalId">, "strip", z.ZodTypeAny, {
        integration: string;
        externalId: string;
    }, {
        integration: string;
        externalId: string;
    }>>>;
    schedule: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
    customInputs: z.ZodOptional<z.ZodArray<z.ZodObject<{
        id: z.ZodNumber;
        eventTypeId: z.ZodNumber;
        label: z.ZodString;
        type: z.ZodNativeEnum<{
            readonly TEXT: "TEXT";
            readonly TEXTLONG: "TEXTLONG";
            readonly NUMBER: "NUMBER";
            readonly BOOL: "BOOL";
            readonly RADIO: "RADIO";
            readonly PHONE: "PHONE";
        }>;
        options: z.ZodNullable<z.ZodOptional<z.ZodArray<z.ZodObject<{
            label: z.ZodString;
            type: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            type: string;
            label: string;
        }, {
            type: string;
            label: string;
        }>, "many">>>;
        required: z.ZodBoolean;
        placeholder: z.ZodString;
        hasToBeCreated: z.ZodOptional<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        id: number;
        type: "TEXT" | "TEXTLONG" | "NUMBER" | "BOOL" | "RADIO" | "PHONE";
        label: string;
        required: boolean;
        eventTypeId: number;
        placeholder: string;
        options?: {
            type: string;
            label: string;
        }[] | null | undefined;
        hasToBeCreated?: boolean | undefined;
    }, {
        id: number;
        type: "TEXT" | "TEXTLONG" | "NUMBER" | "BOOL" | "RADIO" | "PHONE";
        label: string;
        required: boolean;
        eventTypeId: number;
        placeholder: string;
        options?: {
            type: string;
            label: string;
        }[] | null | undefined;
        hasToBeCreated?: boolean | undefined;
    }>, "many">>;
    timeZone: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodString>>>;
    hosts: z.ZodOptional<z.ZodArray<z.ZodObject<{
        userId: z.ZodNumber;
        profileId: z.ZodOptional<z.ZodUnion<[z.ZodNumber, z.ZodNull]>>;
        isFixed: z.ZodOptional<z.ZodBoolean>;
        priority: z.ZodNullable<z.ZodOptional<z.ZodNumber>>;
        weight: z.ZodNullable<z.ZodOptional<z.ZodNumber>>;
        scheduleId: z.ZodNullable<z.ZodOptional<z.ZodNumber>>;
        groupId: z.ZodNullable<z.ZodOptional<z.ZodString>>;
    }, "strip", z.ZodTypeAny, {
        userId: number;
        profileId?: number | null | undefined;
        isFixed?: boolean | undefined;
        priority?: number | null | undefined;
        weight?: number | null | undefined;
        scheduleId?: number | null | undefined;
        groupId?: string | null | undefined;
    }, {
        userId: number;
        profileId?: number | null | undefined;
        isFixed?: boolean | undefined;
        priority?: number | null | undefined;
        weight?: number | null | undefined;
        scheduleId?: number | null | undefined;
        groupId?: string | null | undefined;
    }>, "many">>;
    slug: z.ZodOptional<z.ZodEffects<z.ZodEffects<z.ZodString, string, string>, string, string>>;
    parentId: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodNumber>>>;
    bookingLimits: z.ZodOptional<z.ZodType<Partial<Record<"PER_DAY" | "PER_MONTH" | "PER_WEEK" | "PER_YEAR", number | undefined>> | null, z.ZodTypeDef, Partial<Record<"PER_DAY" | "PER_MONTH" | "PER_WEEK" | "PER_YEAR", number | undefined>> | null>>;
    teamId: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodNumber>>>;
    hidden: z.ZodOptional<z.ZodBoolean>;
    interfaceLanguage: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodString>>>;
    position: z.ZodOptional<z.ZodNumber>;
    locations: z.ZodOptional<z.ZodArray<z.ZodObject<{
        type: z.ZodString;
        address: z.ZodOptional<z.ZodString>;
        link: z.ZodOptional<z.ZodString>;
        displayLocationPublicly: z.ZodOptional<z.ZodBoolean>;
        hostPhoneNumber: z.ZodOptional<z.ZodString>;
        credentialId: z.ZodOptional<z.ZodNumber>;
        teamName: z.ZodOptional<z.ZodString>;
        customLabel: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        type: string;
        address?: string | undefined;
        link?: string | undefined;
        displayLocationPublicly?: boolean | undefined;
        hostPhoneNumber?: string | undefined;
        credentialId?: number | undefined;
        teamName?: string | undefined;
        customLabel?: string | undefined;
    }, {
        type: string;
        address?: string | undefined;
        link?: string | undefined;
        displayLocationPublicly?: boolean | undefined;
        hostPhoneNumber?: string | undefined;
        credentialId?: number | undefined;
        teamName?: string | undefined;
        customLabel?: string | undefined;
    }>, "many">>;
    offsetStart: z.ZodOptional<z.ZodNumber>;
    profileId: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodNumber>>>;
    useEventLevelSelectedCalendars: z.ZodOptional<z.ZodOptional<z.ZodBoolean>>;
    eventName: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodString>>>;
    bookingFields: z.ZodOptional<z.ZodArray<z.ZodObject<{
        name: z.ZodEffects<z.ZodString, string, string>;
        type: z.ZodEnum<["name", "text", "textarea", "number", "email", "phone", "address", "multiemail", "select", "multiselect", "checkbox", "radio", "radioInput", "boolean", "url"]>;
        label: z.ZodOptional<z.ZodString>;
        options: z.ZodOptional<z.ZodArray<z.ZodObject<{
            label: z.ZodString;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            label: string;
            value: string;
        }, {
            label: string;
            value: string;
        }>, "many">>;
        required: z.ZodOptional<z.ZodDefault<z.ZodBoolean>>;
        placeholder: z.ZodOptional<z.ZodString>;
        maxLength: z.ZodOptional<z.ZodNumber>;
        defaultLabel: z.ZodOptional<z.ZodString>;
        defaultPlaceholder: z.ZodOptional<z.ZodString>;
        labelAsSafeHtml: z.ZodOptional<z.ZodString>;
        getOptionsAt: z.ZodOptional<z.ZodString>;
        optionsInputs: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodObject<{
            type: z.ZodEnum<["address", "phone", "text"]>;
            required: z.ZodOptional<z.ZodBoolean>;
            placeholder: z.ZodOptional<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            type: "phone" | "address" | "text";
            required?: boolean | undefined;
            placeholder?: string | undefined;
        }, {
            type: "phone" | "address" | "text";
            required?: boolean | undefined;
            placeholder?: string | undefined;
        }>>>;
        minLength: z.ZodOptional<z.ZodNumber>;
        excludeEmails: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
        requireEmails: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
        variant: z.ZodOptional<z.ZodString>;
        variantsConfig: z.ZodOptional<z.ZodObject<{
            variants: z.ZodRecord<z.ZodString, z.ZodObject<{
                fields: z.ZodArray<z.ZodObject<Omit<{
                    name: z.ZodEffects<z.ZodString, string, string>;
                    type: z.ZodEnum<["name", "text", "textarea", "number", "email", "phone", "address", "multiemail", "select", "multiselect", "checkbox", "radio", "radioInput", "boolean", "url"]>;
                    label: z.ZodOptional<z.ZodString>;
                    labelAsSafeHtml: z.ZodOptional<z.ZodString>;
                    defaultLabel: z.ZodOptional<z.ZodString>;
                    placeholder: z.ZodOptional<z.ZodString>;
                    defaultPlaceholder: z.ZodOptional<z.ZodString>;
                    required: z.ZodOptional<z.ZodDefault<z.ZodBoolean>>;
                    options: z.ZodOptional<z.ZodArray<z.ZodObject<{
                        label: z.ZodString;
                        value: z.ZodString;
                    }, "strip", z.ZodTypeAny, {
                        label: string;
                        value: string;
                    }, {
                        label: string;
                        value: string;
                    }>, "many">>;
                    getOptionsAt: z.ZodOptional<z.ZodString>;
                    optionsInputs: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodObject<{
                        type: z.ZodEnum<["address", "phone", "text"]>;
                        required: z.ZodOptional<z.ZodBoolean>;
                        placeholder: z.ZodOptional<z.ZodString>;
                    }, "strip", z.ZodTypeAny, {
                        type: "phone" | "address" | "text";
                        required?: boolean | undefined;
                        placeholder?: string | undefined;
                    }, {
                        type: "phone" | "address" | "text";
                        required?: boolean | undefined;
                        placeholder?: string | undefined;
                    }>>>;
                    minLength: z.ZodOptional<z.ZodNumber>;
                    maxLength: z.ZodOptional<z.ZodNumber>;
                    excludeEmails: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
                    requireEmails: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
                }, "options" | "defaultLabel" | "defaultPlaceholder" | "getOptionsAt" | "optionsInputs">, "strip", z.ZodTypeAny, {
                    name: string;
                    type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
                    label?: string | undefined;
                    required?: boolean | undefined;
                    placeholder?: string | undefined;
                    maxLength?: number | undefined;
                    labelAsSafeHtml?: string | undefined;
                    minLength?: number | undefined;
                    excludeEmails?: string | undefined;
                    requireEmails?: string | undefined;
                }, {
                    name: string;
                    type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
                    label?: string | undefined;
                    required?: boolean | undefined;
                    placeholder?: string | undefined;
                    maxLength?: number | undefined;
                    labelAsSafeHtml?: string | undefined;
                    minLength?: number | undefined;
                    excludeEmails?: string | undefined;
                    requireEmails?: string | undefined;
                }>, "many">;
            }, "strip", z.ZodTypeAny, {
                fields: {
                    name: string;
                    type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
                    label?: string | undefined;
                    required?: boolean | undefined;
                    placeholder?: string | undefined;
                    maxLength?: number | undefined;
                    labelAsSafeHtml?: string | undefined;
                    minLength?: number | undefined;
                    excludeEmails?: string | undefined;
                    requireEmails?: string | undefined;
                }[];
            }, {
                fields: {
                    name: string;
                    type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
                    label?: string | undefined;
                    required?: boolean | undefined;
                    placeholder?: string | undefined;
                    maxLength?: number | undefined;
                    labelAsSafeHtml?: string | undefined;
                    minLength?: number | undefined;
                    excludeEmails?: string | undefined;
                    requireEmails?: string | undefined;
                }[];
            }>>;
        }, "strip", z.ZodTypeAny, {
            variants: Record<string, {
                fields: {
                    name: string;
                    type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
                    label?: string | undefined;
                    required?: boolean | undefined;
                    placeholder?: string | undefined;
                    maxLength?: number | undefined;
                    labelAsSafeHtml?: string | undefined;
                    minLength?: number | undefined;
                    excludeEmails?: string | undefined;
                    requireEmails?: string | undefined;
                }[];
            }>;
        }, {
            variants: Record<string, {
                fields: {
                    name: string;
                    type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
                    label?: string | undefined;
                    required?: boolean | undefined;
                    placeholder?: string | undefined;
                    maxLength?: number | undefined;
                    labelAsSafeHtml?: string | undefined;
                    minLength?: number | undefined;
                    excludeEmails?: string | undefined;
                    requireEmails?: string | undefined;
                }[];
            }>;
        }>>;
        views: z.ZodOptional<z.ZodArray<z.ZodObject<{
            label: z.ZodString;
            id: z.ZodString;
            description: z.ZodOptional<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            id: string;
            label: string;
            description?: string | undefined;
        }, {
            id: string;
            label: string;
            description?: string | undefined;
        }>, "many">>;
        hideWhenJustOneOption: z.ZodOptional<z.ZodDefault<z.ZodBoolean>>;
        hidden: z.ZodOptional<z.ZodBoolean>;
        editable: z.ZodOptional<z.ZodDefault<z.ZodEnum<["system", "system-but-optional", "system-but-hidden", "user", "user-readonly"]>>>;
        sources: z.ZodOptional<z.ZodArray<z.ZodObject<{
            id: z.ZodString;
            type: z.ZodUnion<[z.ZodLiteral<"user">, z.ZodLiteral<"system">, z.ZodString]>;
            label: z.ZodString;
            editUrl: z.ZodOptional<z.ZodString>;
            fieldRequired: z.ZodOptional<z.ZodBoolean>;
        }, "strip", z.ZodTypeAny, {
            id: string;
            type: string;
            label: string;
            editUrl?: string | undefined;
            fieldRequired?: boolean | undefined;
        }, {
            id: string;
            type: string;
            label: string;
            editUrl?: string | undefined;
            fieldRequired?: boolean | undefined;
        }>, "many">>;
        disableOnPrefill: z.ZodOptional<z.ZodDefault<z.ZodBoolean>>;
    }, "strip", z.ZodTypeAny, {
        name: string;
        type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
        label?: string | undefined;
        options?: {
            label: string;
            value: string;
        }[] | undefined;
        required?: boolean | undefined;
        placeholder?: string | undefined;
        maxLength?: number | undefined;
        defaultLabel?: string | undefined;
        defaultPlaceholder?: string | undefined;
        labelAsSafeHtml?: string | undefined;
        getOptionsAt?: string | undefined;
        optionsInputs?: Record<string, {
            type: "phone" | "address" | "text";
            required?: boolean | undefined;
            placeholder?: string | undefined;
        }> | undefined;
        minLength?: number | undefined;
        excludeEmails?: string | undefined;
        requireEmails?: string | undefined;
        variant?: string | undefined;
        variantsConfig?: {
            variants: Record<string, {
                fields: {
                    name: string;
                    type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
                    label?: string | undefined;
                    required?: boolean | undefined;
                    placeholder?: string | undefined;
                    maxLength?: number | undefined;
                    labelAsSafeHtml?: string | undefined;
                    minLength?: number | undefined;
                    excludeEmails?: string | undefined;
                    requireEmails?: string | undefined;
                }[];
            }>;
        } | undefined;
        views?: {
            id: string;
            label: string;
            description?: string | undefined;
        }[] | undefined;
        hideWhenJustOneOption?: boolean | undefined;
        hidden?: boolean | undefined;
        editable?: "user" | "system" | "system-but-optional" | "system-but-hidden" | "user-readonly" | undefined;
        sources?: {
            id: string;
            type: string;
            label: string;
            editUrl?: string | undefined;
            fieldRequired?: boolean | undefined;
        }[] | undefined;
        disableOnPrefill?: boolean | undefined;
    }, {
        name: string;
        type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
        label?: string | undefined;
        options?: {
            label: string;
            value: string;
        }[] | undefined;
        required?: boolean | undefined;
        placeholder?: string | undefined;
        maxLength?: number | undefined;
        defaultLabel?: string | undefined;
        defaultPlaceholder?: string | undefined;
        labelAsSafeHtml?: string | undefined;
        getOptionsAt?: string | undefined;
        optionsInputs?: Record<string, {
            type: "phone" | "address" | "text";
            required?: boolean | undefined;
            placeholder?: string | undefined;
        }> | undefined;
        minLength?: number | undefined;
        excludeEmails?: string | undefined;
        requireEmails?: string | undefined;
        variant?: string | undefined;
        variantsConfig?: {
            variants: Record<string, {
                fields: {
                    name: string;
                    type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
                    label?: string | undefined;
                    required?: boolean | undefined;
                    placeholder?: string | undefined;
                    maxLength?: number | undefined;
                    labelAsSafeHtml?: string | undefined;
                    minLength?: number | undefined;
                    excludeEmails?: string | undefined;
                    requireEmails?: string | undefined;
                }[];
            }>;
        } | undefined;
        views?: {
            id: string;
            label: string;
            description?: string | undefined;
        }[] | undefined;
        hideWhenJustOneOption?: boolean | undefined;
        hidden?: boolean | undefined;
        editable?: "user" | "system" | "system-but-optional" | "system-but-hidden" | "user-readonly" | undefined;
        sources?: {
            id: string;
            type: string;
            label: string;
            editUrl?: string | undefined;
            fieldRequired?: boolean | undefined;
        }[] | undefined;
        disableOnPrefill?: boolean | undefined;
    }>, "many">>;
    periodType: z.ZodOptional<z.ZodNativeEnum<{
        UNLIMITED: "UNLIMITED";
        ROLLING: "ROLLING";
        ROLLING_WINDOW: "ROLLING_WINDOW";
        RANGE: "RANGE";
    }>>;
    periodStartDate: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodDate>>>;
    periodEndDate: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodDate>>>;
    periodDays: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodNumber>>>;
    periodCountCalendarDays: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodBoolean>>>;
    lockTimeZoneToggleOnBookingPage: z.ZodOptional<z.ZodBoolean>;
    lockedTimeZone: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodString>>>;
    requiresConfirmation: z.ZodOptional<z.ZodBoolean>;
    requiresConfirmationWillBlockSlot: z.ZodOptional<z.ZodBoolean>;
    requiresConfirmationForFreeEmail: z.ZodOptional<z.ZodBoolean>;
    requiresBookerEmailVerification: z.ZodOptional<z.ZodBoolean>;
    canSendCalVideoTranscriptionEmails: z.ZodOptional<z.ZodBoolean>;
    autoTranslateDescriptionEnabled: z.ZodOptional<z.ZodBoolean>;
    recurringEvent: z.ZodOptional<z.ZodNullable<z.ZodObject<{
        dtstart: z.ZodOptional<z.ZodDate>;
        interval: z.ZodNumber;
        count: z.ZodNumber;
        freq: z.ZodNativeEnum<typeof import("@calcom/prisma/zod-utils").Frequency>;
        until: z.ZodOptional<z.ZodDate>;
        tzid: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        count: number;
        interval: number;
        freq: import("@calcom/prisma/zod-utils").Frequency;
        dtstart?: Date | undefined;
        until?: Date | undefined;
        tzid?: string | undefined;
    }, {
        count: number;
        interval: number;
        freq: import("@calcom/prisma/zod-utils").Frequency;
        dtstart?: Date | undefined;
        until?: Date | undefined;
        tzid?: string | undefined;
    }>>>;
    disableGuests: z.ZodOptional<z.ZodBoolean>;
    hideCalendarNotes: z.ZodOptional<z.ZodBoolean>;
    hideCalendarEventDetails: z.ZodOptional<z.ZodBoolean>;
    minimumBookingNotice: z.ZodOptional<z.ZodNumber>;
    beforeEventBuffer: z.ZodOptional<z.ZodNumber>;
    afterEventBuffer: z.ZodOptional<z.ZodNumber>;
    seatsPerTimeSlot: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodNumber>>>;
    onlyShowFirstAvailableSlot: z.ZodOptional<z.ZodBoolean>;
    disableCancelling: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodBoolean>>>;
    disableRescheduling: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodBoolean>>>;
    seatsShowAttendees: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodBoolean>>>;
    seatsShowAvailabilityCount: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodBoolean>>>;
    schedulingType: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodNativeEnum<{
        ROUND_ROBIN: "ROUND_ROBIN";
        COLLECTIVE: "COLLECTIVE";
        MANAGED: "MANAGED";
    }>>>>;
    scheduleId: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodNumber>>>;
    allowReschedulingCancelledBookings: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodBoolean>>>;
    price: z.ZodOptional<z.ZodNumber>;
    currency: z.ZodOptional<z.ZodString>;
    slotInterval: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodNumber>>>;
    successRedirectUrl: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodOptional<z.ZodUnion<[z.ZodLiteral<"">, z.ZodString]>>>>>;
    forwardParamsSuccessRedirect: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodBoolean>>>;
    durationLimits: z.ZodOptional<z.ZodType<Partial<Record<"PER_DAY" | "PER_MONTH" | "PER_WEEK" | "PER_YEAR", number | undefined>> | null, z.ZodTypeDef, Partial<Record<"PER_DAY" | "PER_MONTH" | "PER_WEEK" | "PER_YEAR", number | undefined>> | null>>;
    isInstantEvent: z.ZodOptional<z.ZodBoolean>;
    instantMeetingExpiryTimeOffsetInSeconds: z.ZodOptional<z.ZodNumber>;
    instantMeetingScheduleId: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodNumber>>>;
    instantMeetingParameters: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    assignAllTeamMembers: z.ZodOptional<z.ZodBoolean>;
    assignRRMembersUsingSegment: z.ZodOptional<z.ZodOptional<z.ZodBoolean>>;
    rrSegmentQueryValue: z.ZodOptional<z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodUnion<[z.ZodObject<{
        id: z.ZodOptional<z.ZodString>;
        type: z.ZodLiteral<"group">;
        children1: z.ZodOptional<z.ZodEffects<z.ZodRecord<z.ZodString, z.ZodObject<{
            type: z.ZodOptional<z.ZodString>;
            properties: z.ZodOptional<z.ZodObject<{
                field: z.ZodOptional<z.ZodAny>;
                operator: z.ZodOptional<z.ZodAny>;
                value: z.ZodOptional<z.ZodAny>;
                valueSrc: z.ZodOptional<z.ZodAny>;
                valueError: z.ZodOptional<z.ZodArray<z.ZodUnion<[z.ZodString, z.ZodNull]>, "many">>;
                valueType: z.ZodOptional<z.ZodAny>;
            }, "strip", z.ZodTypeAny, {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            }, {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            }>>;
        }, "strip", z.ZodTypeAny, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }>>, Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }>, Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }>>>;
        properties: z.ZodAny;
    }, "strip", z.ZodTypeAny, {
        type: "group";
        id?: string | undefined;
        children1?: Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }> | undefined;
        properties?: any;
    }, {
        type: "group";
        id?: string | undefined;
        children1?: Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }> | undefined;
        properties?: any;
    }>, z.ZodObject<{
        id: z.ZodOptional<z.ZodString>;
        type: z.ZodLiteral<"switch_group">;
        children1: z.ZodOptional<z.ZodEffects<z.ZodRecord<z.ZodString, z.ZodObject<{
            type: z.ZodOptional<z.ZodString>;
            properties: z.ZodOptional<z.ZodObject<{
                field: z.ZodOptional<z.ZodAny>;
                operator: z.ZodOptional<z.ZodAny>;
                value: z.ZodOptional<z.ZodAny>;
                valueSrc: z.ZodOptional<z.ZodAny>;
                valueError: z.ZodOptional<z.ZodArray<z.ZodUnion<[z.ZodString, z.ZodNull]>, "many">>;
                valueType: z.ZodOptional<z.ZodAny>;
            }, "strip", z.ZodTypeAny, {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            }, {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            }>>;
        }, "strip", z.ZodTypeAny, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }>>, Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }>, Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }>>>;
        properties: z.ZodAny;
    }, "strip", z.ZodTypeAny, {
        type: "switch_group";
        id?: string | undefined;
        children1?: Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }> | undefined;
        properties?: any;
    }, {
        type: "switch_group";
        id?: string | undefined;
        children1?: Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }> | undefined;
        properties?: any;
    }>]>>>>>;
    useEventTypeDestinationCalendarEmail: z.ZodOptional<z.ZodBoolean>;
    isRRWeightsEnabled: z.ZodOptional<z.ZodBoolean>;
    maxLeadThreshold: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodNumber>>>;
    includeNoShowInRRCalculation: z.ZodOptional<z.ZodBoolean>;
    allowReschedulingPastBookings: z.ZodOptional<z.ZodBoolean>;
    hideOrganizerEmail: z.ZodOptional<z.ZodBoolean>;
    maxActiveBookingsPerBooker: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodNumber>>>;
    maxActiveBookingPerBookerOfferReschedule: z.ZodOptional<z.ZodBoolean>;
    customReplyToEmail: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodString>>>;
    eventTypeColor: z.ZodOptional<z.ZodNullable<z.ZodObject<{
        lightEventTypeColor: z.ZodString;
        darkEventTypeColor: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        lightEventTypeColor: string;
        darkEventTypeColor: string;
    }, {
        lightEventTypeColor: string;
        darkEventTypeColor: string;
    }>>>;
    rescheduleWithSameRoundRobinHost: z.ZodOptional<z.ZodBoolean>;
    secondaryEmailId: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodNumber>>>;
    useBookerTimezone: z.ZodOptional<z.ZodBoolean>;
    restrictionScheduleId: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodNumber>>>;
    bookingRequiresAuthentication: z.ZodOptional<z.ZodBoolean>;
    instantMeetingSchedule: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
    aiPhoneCallConfig: z.ZodOptional<z.ZodOptional<z.ZodObject<{
        generalPrompt: z.ZodString;
        enabled: z.ZodBoolean;
        beginMessage: z.ZodNullable<z.ZodString>;
        yourPhoneNumber: z.ZodString;
        numberToCall: z.ZodString;
        guestName: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        guestEmail: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        guestCompany: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        templateType: z.ZodEnum<["CHECK_IN_APPOINTMENT", "CUSTOM_TEMPLATE"]>;
    }, "strip", z.ZodTypeAny, {
        enabled: boolean;
        templateType: "CHECK_IN_APPOINTMENT" | "CUSTOM_TEMPLATE";
        generalPrompt: string;
        yourPhoneNumber: string;
        numberToCall: string;
        beginMessage: string | null;
        guestName?: string | null | undefined;
        guestEmail?: string | null | undefined;
        guestCompany?: string | null | undefined;
    }, {
        enabled: boolean;
        templateType: "CHECK_IN_APPOINTMENT" | "CUSTOM_TEMPLATE";
        generalPrompt: string;
        yourPhoneNumber: string;
        numberToCall: string;
        beginMessage: string | null;
        guestName?: string | null | undefined;
        guestEmail?: string | null | undefined;
        guestCompany?: string | null | undefined;
    }>>>;
    hostGroups: z.ZodOptional<z.ZodOptional<z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        name: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        name: string;
        id: string;
    }, {
        name: string;
        id: string;
    }>, "many">>>;
    calAiPhoneScript: z.ZodOptional<z.ZodString>;
    multiplePrivateLinks: z.ZodOptional<z.ZodArray<z.ZodUnion<[z.ZodString, z.ZodObject<{
        link: z.ZodString;
        expiresAt: z.ZodOptional<z.ZodNullable<z.ZodDate>>;
        maxUsageCount: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
        usageCount: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
    }, "strict", z.ZodTypeAny, {
        link: string;
        expiresAt?: Date | null | undefined;
        maxUsageCount?: number | null | undefined;
        usageCount?: number | null | undefined;
    }, {
        link: string;
        expiresAt?: Date | null | undefined;
        maxUsageCount?: number | null | undefined;
        usageCount?: number | null | undefined;
    }>]>, "many">>;
    id: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    id: number;
    users?: number[] | undefined;
    children?: {
        hidden: boolean;
        owner: {
            name: string;
            id: number;
            email: string;
            eventTypeSlugs: string[];
        };
    }[] | undefined;
    length?: number | undefined;
    title?: string | undefined;
    metadata?: {
        config?: {
            useHostSchedulesForTeamEvent?: boolean | undefined;
        } | undefined;
        smartContractAddress?: string | undefined;
        blockchainId?: number | undefined;
        multipleDuration?: number[] | undefined;
        giphyThankYouPage?: string | undefined;
        additionalNotesRequired?: boolean | undefined;
        disableSuccessPage?: boolean | undefined;
        disableStandardEmails?: {
            all?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
            confirmation?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
        } | undefined;
        managedEventConfig?: {
            unlockedFields?: {
                users?: true | undefined;
                children?: true | undefined;
                length?: true | undefined;
                title?: true | undefined;
                metadata?: true | undefined;
                description?: true | undefined;
                userId?: true | undefined;
                calVideoSettings?: true | undefined;
                destinationCalendar?: true | undefined;
                profile?: true | undefined;
                team?: true | undefined;
                schedule?: true | undefined;
                availability?: true | undefined;
                hashedLink?: true | undefined;
                secondaryEmail?: true | undefined;
                customInputs?: true | undefined;
                timeZone?: true | undefined;
                bookings?: true | undefined;
                selectedCalendars?: true | undefined;
                webhooks?: true | undefined;
                workflows?: true | undefined;
                hosts?: true | undefined;
                slug?: true | undefined;
                parentId?: true | undefined;
                bookingLimits?: true | undefined;
                parent?: true | undefined;
                teamId?: true | undefined;
                hidden?: true | undefined;
                _count?: true | undefined;
                interfaceLanguage?: true | undefined;
                position?: true | undefined;
                locations?: true | undefined;
                offsetStart?: true | undefined;
                profileId?: true | undefined;
                useEventLevelSelectedCalendars?: true | undefined;
                eventName?: true | undefined;
                bookingFields?: true | undefined;
                periodType?: true | undefined;
                periodStartDate?: true | undefined;
                periodEndDate?: true | undefined;
                periodDays?: true | undefined;
                periodCountCalendarDays?: true | undefined;
                lockTimeZoneToggleOnBookingPage?: true | undefined;
                lockedTimeZone?: true | undefined;
                requiresConfirmation?: true | undefined;
                requiresConfirmationWillBlockSlot?: true | undefined;
                requiresConfirmationForFreeEmail?: true | undefined;
                requiresBookerEmailVerification?: true | undefined;
                canSendCalVideoTranscriptionEmails?: true | undefined;
                autoTranslateDescriptionEnabled?: true | undefined;
                recurringEvent?: true | undefined;
                disableGuests?: true | undefined;
                hideCalendarNotes?: true | undefined;
                hideCalendarEventDetails?: true | undefined;
                minimumBookingNotice?: true | undefined;
                beforeEventBuffer?: true | undefined;
                afterEventBuffer?: true | undefined;
                seatsPerTimeSlot?: true | undefined;
                onlyShowFirstAvailableSlot?: true | undefined;
                disableCancelling?: true | undefined;
                disableRescheduling?: true | undefined;
                seatsShowAttendees?: true | undefined;
                seatsShowAvailabilityCount?: true | undefined;
                schedulingType?: true | undefined;
                scheduleId?: true | undefined;
                allowReschedulingCancelledBookings?: true | undefined;
                price?: true | undefined;
                currency?: true | undefined;
                slotInterval?: true | undefined;
                successRedirectUrl?: true | undefined;
                forwardParamsSuccessRedirect?: true | undefined;
                durationLimits?: true | undefined;
                isInstantEvent?: true | undefined;
                instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                instantMeetingScheduleId?: true | undefined;
                instantMeetingParameters?: true | undefined;
                assignAllTeamMembers?: true | undefined;
                assignRRMembersUsingSegment?: true | undefined;
                rrSegmentQueryValue?: true | undefined;
                useEventTypeDestinationCalendarEmail?: true | undefined;
                isRRWeightsEnabled?: true | undefined;
                maxLeadThreshold?: true | undefined;
                includeNoShowInRRCalculation?: true | undefined;
                allowReschedulingPastBookings?: true | undefined;
                hideOrganizerEmail?: true | undefined;
                maxActiveBookingsPerBooker?: true | undefined;
                maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                customReplyToEmail?: true | undefined;
                eventTypeColor?: true | undefined;
                rescheduleWithSameRoundRobinHost?: true | undefined;
                secondaryEmailId?: true | undefined;
                useBookerTimezone?: true | undefined;
                restrictionScheduleId?: true | undefined;
                bookingRequiresAuthentication?: true | undefined;
                owner?: true | undefined;
                instantMeetingSchedule?: true | undefined;
                aiPhoneCallConfig?: true | undefined;
                fieldTranslations?: true | undefined;
                restrictionSchedule?: true | undefined;
                hostGroups?: true | undefined;
            } | undefined;
        } | undefined;
        requiresConfirmationThreshold?: {
            time: number;
            unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
        } | undefined;
        bookerLayouts?: {
            enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
            defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
        } | null | undefined;
        apps?: unknown;
    } | null | undefined;
    description?: string | null | undefined;
    userId?: number | null | undefined;
    calVideoSettings?: {
        disableRecordingForGuests?: boolean | null | undefined;
        disableRecordingForOrganizer?: boolean | null | undefined;
        enableAutomaticTranscription?: boolean | null | undefined;
        enableAutomaticRecordingForOrganizer?: boolean | null | undefined;
        disableTranscriptionForGuests?: boolean | null | undefined;
        disableTranscriptionForOrganizer?: boolean | null | undefined;
        redirectUrlOnExit?: string | null | undefined;
    } | null | undefined;
    destinationCalendar?: {
        integration: string;
        externalId: string;
    } | null | undefined;
    schedule?: number | null | undefined;
    customInputs?: {
        id: number;
        type: "TEXT" | "TEXTLONG" | "NUMBER" | "BOOL" | "RADIO" | "PHONE";
        label: string;
        required: boolean;
        eventTypeId: number;
        placeholder: string;
        options?: {
            type: string;
            label: string;
        }[] | null | undefined;
        hasToBeCreated?: boolean | undefined;
    }[] | undefined;
    timeZone?: string | null | undefined;
    hosts?: {
        userId: number;
        profileId?: number | null | undefined;
        isFixed?: boolean | undefined;
        priority?: number | null | undefined;
        weight?: number | null | undefined;
        scheduleId?: number | null | undefined;
        groupId?: string | null | undefined;
    }[] | undefined;
    slug?: string | undefined;
    parentId?: number | null | undefined;
    bookingLimits?: Partial<Record<"PER_DAY" | "PER_MONTH" | "PER_WEEK" | "PER_YEAR", number | undefined>> | null | undefined;
    teamId?: number | null | undefined;
    hidden?: boolean | undefined;
    interfaceLanguage?: string | null | undefined;
    position?: number | undefined;
    locations?: {
        type: string;
        address?: string | undefined;
        link?: string | undefined;
        displayLocationPublicly?: boolean | undefined;
        hostPhoneNumber?: string | undefined;
        credentialId?: number | undefined;
        teamName?: string | undefined;
        customLabel?: string | undefined;
    }[] | undefined;
    offsetStart?: number | undefined;
    profileId?: number | null | undefined;
    useEventLevelSelectedCalendars?: boolean | undefined;
    eventName?: string | null | undefined;
    bookingFields?: {
        name: string;
        type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
        label?: string | undefined;
        options?: {
            label: string;
            value: string;
        }[] | undefined;
        required?: boolean | undefined;
        placeholder?: string | undefined;
        maxLength?: number | undefined;
        defaultLabel?: string | undefined;
        defaultPlaceholder?: string | undefined;
        labelAsSafeHtml?: string | undefined;
        getOptionsAt?: string | undefined;
        optionsInputs?: Record<string, {
            type: "phone" | "address" | "text";
            required?: boolean | undefined;
            placeholder?: string | undefined;
        }> | undefined;
        minLength?: number | undefined;
        excludeEmails?: string | undefined;
        requireEmails?: string | undefined;
        variant?: string | undefined;
        variantsConfig?: {
            variants: Record<string, {
                fields: {
                    name: string;
                    type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
                    label?: string | undefined;
                    required?: boolean | undefined;
                    placeholder?: string | undefined;
                    maxLength?: number | undefined;
                    labelAsSafeHtml?: string | undefined;
                    minLength?: number | undefined;
                    excludeEmails?: string | undefined;
                    requireEmails?: string | undefined;
                }[];
            }>;
        } | undefined;
        views?: {
            id: string;
            label: string;
            description?: string | undefined;
        }[] | undefined;
        hideWhenJustOneOption?: boolean | undefined;
        hidden?: boolean | undefined;
        editable?: "user" | "system" | "system-but-optional" | "system-but-hidden" | "user-readonly" | undefined;
        sources?: {
            id: string;
            type: string;
            label: string;
            editUrl?: string | undefined;
            fieldRequired?: boolean | undefined;
        }[] | undefined;
        disableOnPrefill?: boolean | undefined;
    }[] | undefined;
    periodType?: "UNLIMITED" | "ROLLING" | "ROLLING_WINDOW" | "RANGE" | undefined;
    periodStartDate?: Date | null | undefined;
    periodEndDate?: Date | null | undefined;
    periodDays?: number | null | undefined;
    periodCountCalendarDays?: boolean | null | undefined;
    lockTimeZoneToggleOnBookingPage?: boolean | undefined;
    lockedTimeZone?: string | null | undefined;
    requiresConfirmation?: boolean | undefined;
    requiresConfirmationWillBlockSlot?: boolean | undefined;
    requiresConfirmationForFreeEmail?: boolean | undefined;
    requiresBookerEmailVerification?: boolean | undefined;
    canSendCalVideoTranscriptionEmails?: boolean | undefined;
    autoTranslateDescriptionEnabled?: boolean | undefined;
    recurringEvent?: {
        count: number;
        interval: number;
        freq: import("@calcom/prisma/zod-utils").Frequency;
        dtstart?: Date | undefined;
        until?: Date | undefined;
        tzid?: string | undefined;
    } | null | undefined;
    disableGuests?: boolean | undefined;
    hideCalendarNotes?: boolean | undefined;
    hideCalendarEventDetails?: boolean | undefined;
    minimumBookingNotice?: number | undefined;
    beforeEventBuffer?: number | undefined;
    afterEventBuffer?: number | undefined;
    seatsPerTimeSlot?: number | null | undefined;
    onlyShowFirstAvailableSlot?: boolean | undefined;
    disableCancelling?: boolean | null | undefined;
    disableRescheduling?: boolean | null | undefined;
    seatsShowAttendees?: boolean | null | undefined;
    seatsShowAvailabilityCount?: boolean | null | undefined;
    schedulingType?: "ROUND_ROBIN" | "COLLECTIVE" | "MANAGED" | null | undefined;
    scheduleId?: number | null | undefined;
    allowReschedulingCancelledBookings?: boolean | null | undefined;
    price?: number | undefined;
    currency?: string | undefined;
    slotInterval?: number | null | undefined;
    successRedirectUrl?: string | null | undefined;
    forwardParamsSuccessRedirect?: boolean | null | undefined;
    durationLimits?: Partial<Record<"PER_DAY" | "PER_MONTH" | "PER_WEEK" | "PER_YEAR", number | undefined>> | null | undefined;
    isInstantEvent?: boolean | undefined;
    instantMeetingExpiryTimeOffsetInSeconds?: number | undefined;
    instantMeetingScheduleId?: number | null | undefined;
    instantMeetingParameters?: string[] | undefined;
    assignAllTeamMembers?: boolean | undefined;
    assignRRMembersUsingSegment?: boolean | undefined;
    rrSegmentQueryValue?: {
        type: "group";
        id?: string | undefined;
        children1?: Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }> | undefined;
        properties?: any;
    } | {
        type: "switch_group";
        id?: string | undefined;
        children1?: Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }> | undefined;
        properties?: any;
    } | null | undefined;
    useEventTypeDestinationCalendarEmail?: boolean | undefined;
    isRRWeightsEnabled?: boolean | undefined;
    maxLeadThreshold?: number | null | undefined;
    includeNoShowInRRCalculation?: boolean | undefined;
    allowReschedulingPastBookings?: boolean | undefined;
    hideOrganizerEmail?: boolean | undefined;
    maxActiveBookingsPerBooker?: number | null | undefined;
    maxActiveBookingPerBookerOfferReschedule?: boolean | undefined;
    customReplyToEmail?: string | null | undefined;
    eventTypeColor?: {
        lightEventTypeColor: string;
        darkEventTypeColor: string;
    } | null | undefined;
    rescheduleWithSameRoundRobinHost?: boolean | undefined;
    secondaryEmailId?: number | null | undefined;
    useBookerTimezone?: boolean | undefined;
    restrictionScheduleId?: number | null | undefined;
    bookingRequiresAuthentication?: boolean | undefined;
    instantMeetingSchedule?: number | null | undefined;
    aiPhoneCallConfig?: {
        enabled: boolean;
        templateType: "CHECK_IN_APPOINTMENT" | "CUSTOM_TEMPLATE";
        generalPrompt: string;
        yourPhoneNumber: string;
        numberToCall: string;
        beginMessage: string | null;
        guestName?: string | null | undefined;
        guestEmail?: string | null | undefined;
        guestCompany?: string | null | undefined;
    } | undefined;
    hostGroups?: {
        name: string;
        id: string;
    }[] | undefined;
    calAiPhoneScript?: string | undefined;
    multiplePrivateLinks?: (string | {
        link: string;
        expiresAt?: Date | null | undefined;
        maxUsageCount?: number | null | undefined;
        usageCount?: number | null | undefined;
    })[] | undefined;
}, {
    id: number;
    users?: (string | number)[] | undefined;
    children?: {
        hidden: boolean;
        owner: {
            name: string;
            id: number;
            email: string;
            eventTypeSlugs: string[];
        };
    }[] | undefined;
    length?: number | undefined;
    title?: string | undefined;
    metadata?: {
        config?: {
            useHostSchedulesForTeamEvent?: boolean | undefined;
        } | undefined;
        smartContractAddress?: string | undefined;
        blockchainId?: number | undefined;
        multipleDuration?: number[] | undefined;
        giphyThankYouPage?: string | undefined;
        additionalNotesRequired?: boolean | undefined;
        disableSuccessPage?: boolean | undefined;
        disableStandardEmails?: {
            all?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
            confirmation?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
        } | undefined;
        managedEventConfig?: {
            unlockedFields?: {
                users?: true | undefined;
                children?: true | undefined;
                length?: true | undefined;
                title?: true | undefined;
                metadata?: true | undefined;
                description?: true | undefined;
                userId?: true | undefined;
                calVideoSettings?: true | undefined;
                destinationCalendar?: true | undefined;
                profile?: true | undefined;
                team?: true | undefined;
                schedule?: true | undefined;
                availability?: true | undefined;
                hashedLink?: true | undefined;
                secondaryEmail?: true | undefined;
                customInputs?: true | undefined;
                timeZone?: true | undefined;
                bookings?: true | undefined;
                selectedCalendars?: true | undefined;
                webhooks?: true | undefined;
                workflows?: true | undefined;
                hosts?: true | undefined;
                slug?: true | undefined;
                parentId?: true | undefined;
                bookingLimits?: true | undefined;
                parent?: true | undefined;
                teamId?: true | undefined;
                hidden?: true | undefined;
                _count?: true | undefined;
                interfaceLanguage?: true | undefined;
                position?: true | undefined;
                locations?: true | undefined;
                offsetStart?: true | undefined;
                profileId?: true | undefined;
                useEventLevelSelectedCalendars?: true | undefined;
                eventName?: true | undefined;
                bookingFields?: true | undefined;
                periodType?: true | undefined;
                periodStartDate?: true | undefined;
                periodEndDate?: true | undefined;
                periodDays?: true | undefined;
                periodCountCalendarDays?: true | undefined;
                lockTimeZoneToggleOnBookingPage?: true | undefined;
                lockedTimeZone?: true | undefined;
                requiresConfirmation?: true | undefined;
                requiresConfirmationWillBlockSlot?: true | undefined;
                requiresConfirmationForFreeEmail?: true | undefined;
                requiresBookerEmailVerification?: true | undefined;
                canSendCalVideoTranscriptionEmails?: true | undefined;
                autoTranslateDescriptionEnabled?: true | undefined;
                recurringEvent?: true | undefined;
                disableGuests?: true | undefined;
                hideCalendarNotes?: true | undefined;
                hideCalendarEventDetails?: true | undefined;
                minimumBookingNotice?: true | undefined;
                beforeEventBuffer?: true | undefined;
                afterEventBuffer?: true | undefined;
                seatsPerTimeSlot?: true | undefined;
                onlyShowFirstAvailableSlot?: true | undefined;
                disableCancelling?: true | undefined;
                disableRescheduling?: true | undefined;
                seatsShowAttendees?: true | undefined;
                seatsShowAvailabilityCount?: true | undefined;
                schedulingType?: true | undefined;
                scheduleId?: true | undefined;
                allowReschedulingCancelledBookings?: true | undefined;
                price?: true | undefined;
                currency?: true | undefined;
                slotInterval?: true | undefined;
                successRedirectUrl?: true | undefined;
                forwardParamsSuccessRedirect?: true | undefined;
                durationLimits?: true | undefined;
                isInstantEvent?: true | undefined;
                instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                instantMeetingScheduleId?: true | undefined;
                instantMeetingParameters?: true | undefined;
                assignAllTeamMembers?: true | undefined;
                assignRRMembersUsingSegment?: true | undefined;
                rrSegmentQueryValue?: true | undefined;
                useEventTypeDestinationCalendarEmail?: true | undefined;
                isRRWeightsEnabled?: true | undefined;
                maxLeadThreshold?: true | undefined;
                includeNoShowInRRCalculation?: true | undefined;
                allowReschedulingPastBookings?: true | undefined;
                hideOrganizerEmail?: true | undefined;
                maxActiveBookingsPerBooker?: true | undefined;
                maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                customReplyToEmail?: true | undefined;
                eventTypeColor?: true | undefined;
                rescheduleWithSameRoundRobinHost?: true | undefined;
                secondaryEmailId?: true | undefined;
                useBookerTimezone?: true | undefined;
                restrictionScheduleId?: true | undefined;
                bookingRequiresAuthentication?: true | undefined;
                owner?: true | undefined;
                instantMeetingSchedule?: true | undefined;
                aiPhoneCallConfig?: true | undefined;
                fieldTranslations?: true | undefined;
                restrictionSchedule?: true | undefined;
                hostGroups?: true | undefined;
            } | undefined;
        } | undefined;
        requiresConfirmationThreshold?: {
            time: number;
            unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
        } | undefined;
        bookerLayouts?: {
            enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
            defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
        } | null | undefined;
        apps?: unknown;
    } | null | undefined;
    description?: string | null | undefined;
    userId?: number | null | undefined;
    calVideoSettings?: {
        disableRecordingForGuests?: boolean | null | undefined;
        disableRecordingForOrganizer?: boolean | null | undefined;
        enableAutomaticTranscription?: boolean | null | undefined;
        enableAutomaticRecordingForOrganizer?: boolean | null | undefined;
        disableTranscriptionForGuests?: boolean | null | undefined;
        disableTranscriptionForOrganizer?: boolean | null | undefined;
        redirectUrlOnExit?: string | null | undefined;
    } | null | undefined;
    destinationCalendar?: {
        integration: string;
        externalId: string;
    } | null | undefined;
    schedule?: number | null | undefined;
    customInputs?: {
        id: number;
        type: "TEXT" | "TEXTLONG" | "NUMBER" | "BOOL" | "RADIO" | "PHONE";
        label: string;
        required: boolean;
        eventTypeId: number;
        placeholder: string;
        options?: {
            type: string;
            label: string;
        }[] | null | undefined;
        hasToBeCreated?: boolean | undefined;
    }[] | undefined;
    timeZone?: string | null | undefined;
    hosts?: {
        userId: number;
        profileId?: number | null | undefined;
        isFixed?: boolean | undefined;
        priority?: number | null | undefined;
        weight?: number | null | undefined;
        scheduleId?: number | null | undefined;
        groupId?: string | null | undefined;
    }[] | undefined;
    slug?: string | undefined;
    parentId?: number | null | undefined;
    bookingLimits?: Partial<Record<"PER_DAY" | "PER_MONTH" | "PER_WEEK" | "PER_YEAR", number | undefined>> | null | undefined;
    teamId?: number | null | undefined;
    hidden?: boolean | undefined;
    interfaceLanguage?: string | null | undefined;
    position?: number | undefined;
    locations?: {
        type: string;
        address?: string | undefined;
        link?: string | undefined;
        displayLocationPublicly?: boolean | undefined;
        hostPhoneNumber?: string | undefined;
        credentialId?: number | undefined;
        teamName?: string | undefined;
        customLabel?: string | undefined;
    }[] | undefined;
    offsetStart?: number | undefined;
    profileId?: number | null | undefined;
    useEventLevelSelectedCalendars?: boolean | undefined;
    eventName?: string | null | undefined;
    bookingFields?: {
        name: string;
        type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
        label?: string | undefined;
        options?: {
            label: string;
            value: string;
        }[] | undefined;
        required?: boolean | undefined;
        placeholder?: string | undefined;
        maxLength?: number | undefined;
        defaultLabel?: string | undefined;
        defaultPlaceholder?: string | undefined;
        labelAsSafeHtml?: string | undefined;
        getOptionsAt?: string | undefined;
        optionsInputs?: Record<string, {
            type: "phone" | "address" | "text";
            required?: boolean | undefined;
            placeholder?: string | undefined;
        }> | undefined;
        minLength?: number | undefined;
        excludeEmails?: string | undefined;
        requireEmails?: string | undefined;
        variant?: string | undefined;
        variantsConfig?: {
            variants: Record<string, {
                fields: {
                    name: string;
                    type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
                    label?: string | undefined;
                    required?: boolean | undefined;
                    placeholder?: string | undefined;
                    maxLength?: number | undefined;
                    labelAsSafeHtml?: string | undefined;
                    minLength?: number | undefined;
                    excludeEmails?: string | undefined;
                    requireEmails?: string | undefined;
                }[];
            }>;
        } | undefined;
        views?: {
            id: string;
            label: string;
            description?: string | undefined;
        }[] | undefined;
        hideWhenJustOneOption?: boolean | undefined;
        hidden?: boolean | undefined;
        editable?: "user" | "system" | "system-but-optional" | "system-but-hidden" | "user-readonly" | undefined;
        sources?: {
            id: string;
            type: string;
            label: string;
            editUrl?: string | undefined;
            fieldRequired?: boolean | undefined;
        }[] | undefined;
        disableOnPrefill?: boolean | undefined;
    }[] | undefined;
    periodType?: "UNLIMITED" | "ROLLING" | "ROLLING_WINDOW" | "RANGE" | undefined;
    periodStartDate?: Date | null | undefined;
    periodEndDate?: Date | null | undefined;
    periodDays?: number | null | undefined;
    periodCountCalendarDays?: boolean | null | undefined;
    lockTimeZoneToggleOnBookingPage?: boolean | undefined;
    lockedTimeZone?: string | null | undefined;
    requiresConfirmation?: boolean | undefined;
    requiresConfirmationWillBlockSlot?: boolean | undefined;
    requiresConfirmationForFreeEmail?: boolean | undefined;
    requiresBookerEmailVerification?: boolean | undefined;
    canSendCalVideoTranscriptionEmails?: boolean | undefined;
    autoTranslateDescriptionEnabled?: boolean | undefined;
    recurringEvent?: {
        count: number;
        interval: number;
        freq: import("@calcom/prisma/zod-utils").Frequency;
        dtstart?: Date | undefined;
        until?: Date | undefined;
        tzid?: string | undefined;
    } | null | undefined;
    disableGuests?: boolean | undefined;
    hideCalendarNotes?: boolean | undefined;
    hideCalendarEventDetails?: boolean | undefined;
    minimumBookingNotice?: number | undefined;
    beforeEventBuffer?: number | undefined;
    afterEventBuffer?: number | undefined;
    seatsPerTimeSlot?: number | null | undefined;
    onlyShowFirstAvailableSlot?: boolean | undefined;
    disableCancelling?: boolean | null | undefined;
    disableRescheduling?: boolean | null | undefined;
    seatsShowAttendees?: boolean | null | undefined;
    seatsShowAvailabilityCount?: boolean | null | undefined;
    schedulingType?: "ROUND_ROBIN" | "COLLECTIVE" | "MANAGED" | null | undefined;
    scheduleId?: number | null | undefined;
    allowReschedulingCancelledBookings?: boolean | null | undefined;
    price?: number | undefined;
    currency?: string | undefined;
    slotInterval?: number | null | undefined;
    successRedirectUrl?: string | null | undefined;
    forwardParamsSuccessRedirect?: boolean | null | undefined;
    durationLimits?: Partial<Record<"PER_DAY" | "PER_MONTH" | "PER_WEEK" | "PER_YEAR", number | undefined>> | null | undefined;
    isInstantEvent?: boolean | undefined;
    instantMeetingExpiryTimeOffsetInSeconds?: number | undefined;
    instantMeetingScheduleId?: number | null | undefined;
    instantMeetingParameters?: string[] | undefined;
    assignAllTeamMembers?: boolean | undefined;
    assignRRMembersUsingSegment?: boolean | undefined;
    rrSegmentQueryValue?: {
        type: "group";
        id?: string | undefined;
        children1?: Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }> | undefined;
        properties?: any;
    } | {
        type: "switch_group";
        id?: string | undefined;
        children1?: Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }> | undefined;
        properties?: any;
    } | null | undefined;
    useEventTypeDestinationCalendarEmail?: boolean | undefined;
    isRRWeightsEnabled?: boolean | undefined;
    maxLeadThreshold?: number | null | undefined;
    includeNoShowInRRCalculation?: boolean | undefined;
    allowReschedulingPastBookings?: boolean | undefined;
    hideOrganizerEmail?: boolean | undefined;
    maxActiveBookingsPerBooker?: number | null | undefined;
    maxActiveBookingPerBookerOfferReschedule?: boolean | undefined;
    customReplyToEmail?: string | null | undefined;
    eventTypeColor?: {
        lightEventTypeColor: string;
        darkEventTypeColor: string;
    } | null | undefined;
    rescheduleWithSameRoundRobinHost?: boolean | undefined;
    secondaryEmailId?: number | null | undefined;
    useBookerTimezone?: boolean | undefined;
    restrictionScheduleId?: number | null | undefined;
    bookingRequiresAuthentication?: boolean | undefined;
    instantMeetingSchedule?: number | null | undefined;
    aiPhoneCallConfig?: {
        enabled: boolean;
        templateType: "CHECK_IN_APPOINTMENT" | "CUSTOM_TEMPLATE";
        generalPrompt: string;
        yourPhoneNumber: string;
        numberToCall: string;
        beginMessage: string | null;
        guestName?: string | null | undefined;
        guestEmail?: string | null | undefined;
        guestCompany?: string | null | undefined;
    } | undefined;
    hostGroups?: {
        name: string;
        id: string;
    }[] | undefined;
    calAiPhoneScript?: string | undefined;
    multiplePrivateLinks?: (string | {
        link: string;
        expiresAt?: Date | null | undefined;
        maxUsageCount?: number | null | undefined;
        usageCount?: number | null | undefined;
    })[] | undefined;
}>;
export declare const ZUpdateInputSchema: z.ZodObject<{
    users: z.ZodOptional<z.ZodArray<z.ZodUnion<[z.ZodEffects<z.ZodString, number, string>, z.ZodNumber]>, "many">>;
    id: z.ZodNumber;
    children: z.ZodOptional<z.ZodArray<z.ZodObject<{
        owner: z.ZodObject<{
            id: z.ZodNumber;
            name: z.ZodString;
            email: z.ZodString;
            eventTypeSlugs: z.ZodArray<z.ZodString, "many">;
        }, "strip", z.ZodTypeAny, {
            name: string;
            id: number;
            email: string;
            eventTypeSlugs: string[];
        }, {
            name: string;
            id: number;
            email: string;
            eventTypeSlugs: string[];
        }>;
        hidden: z.ZodBoolean;
    }, "strip", z.ZodTypeAny, {
        hidden: boolean;
        owner: {
            name: string;
            id: number;
            email: string;
            eventTypeSlugs: string[];
        };
    }, {
        hidden: boolean;
        owner: {
            name: string;
            id: number;
            email: string;
            eventTypeSlugs: string[];
        };
    }>, "many">>;
    length: z.ZodOptional<z.ZodNumber>;
    title: z.ZodOptional<z.ZodString>;
    metadata: z.ZodOptional<z.ZodNullable<z.ZodObject<{
        config: z.ZodOptional<z.ZodObject<{
            useHostSchedulesForTeamEvent: z.ZodOptional<z.ZodBoolean>;
        }, "strip", z.ZodTypeAny, {
            useHostSchedulesForTeamEvent?: boolean | undefined;
        }, {
            useHostSchedulesForTeamEvent?: boolean | undefined;
        }>>;
        smartContractAddress: z.ZodOptional<z.ZodString>;
        blockchainId: z.ZodOptional<z.ZodNumber>;
        multipleDuration: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        giphyThankYouPage: z.ZodOptional<z.ZodString>;
        additionalNotesRequired: z.ZodOptional<z.ZodBoolean>;
        disableSuccessPage: z.ZodOptional<z.ZodBoolean>;
        disableStandardEmails: z.ZodOptional<z.ZodObject<{
            all: z.ZodOptional<z.ZodObject<{
                host: z.ZodOptional<z.ZodBoolean>;
                attendee: z.ZodOptional<z.ZodBoolean>;
            }, "strip", z.ZodTypeAny, {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            }, {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            }>>;
            confirmation: z.ZodOptional<z.ZodObject<{
                host: z.ZodOptional<z.ZodBoolean>;
                attendee: z.ZodOptional<z.ZodBoolean>;
            }, "strip", z.ZodTypeAny, {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            }, {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            }>>;
        }, "strip", z.ZodTypeAny, {
            all?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
            confirmation?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
        }, {
            all?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
            confirmation?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
        }>>;
        managedEventConfig: z.ZodOptional<z.ZodObject<{
            unlockedFields: z.ZodOptional<z.ZodType<{
                users?: true | undefined;
                children?: true | undefined;
                length?: true | undefined;
                title?: true | undefined;
                metadata?: true | undefined;
                description?: true | undefined;
                userId?: true | undefined;
                calVideoSettings?: true | undefined;
                destinationCalendar?: true | undefined;
                profile?: true | undefined;
                team?: true | undefined;
                schedule?: true | undefined;
                availability?: true | undefined;
                hashedLink?: true | undefined;
                secondaryEmail?: true | undefined;
                customInputs?: true | undefined;
                timeZone?: true | undefined;
                bookings?: true | undefined;
                selectedCalendars?: true | undefined;
                webhooks?: true | undefined;
                workflows?: true | undefined;
                hosts?: true | undefined;
                slug?: true | undefined;
                parentId?: true | undefined;
                bookingLimits?: true | undefined;
                parent?: true | undefined;
                teamId?: true | undefined;
                hidden?: true | undefined;
                _count?: true | undefined;
                interfaceLanguage?: true | undefined;
                position?: true | undefined;
                locations?: true | undefined;
                offsetStart?: true | undefined;
                profileId?: true | undefined;
                useEventLevelSelectedCalendars?: true | undefined;
                eventName?: true | undefined;
                bookingFields?: true | undefined;
                periodType?: true | undefined;
                periodStartDate?: true | undefined;
                periodEndDate?: true | undefined;
                periodDays?: true | undefined;
                periodCountCalendarDays?: true | undefined;
                lockTimeZoneToggleOnBookingPage?: true | undefined;
                lockedTimeZone?: true | undefined;
                requiresConfirmation?: true | undefined;
                requiresConfirmationWillBlockSlot?: true | undefined;
                requiresConfirmationForFreeEmail?: true | undefined;
                requiresBookerEmailVerification?: true | undefined;
                canSendCalVideoTranscriptionEmails?: true | undefined;
                autoTranslateDescriptionEnabled?: true | undefined;
                recurringEvent?: true | undefined;
                disableGuests?: true | undefined;
                hideCalendarNotes?: true | undefined;
                hideCalendarEventDetails?: true | undefined;
                minimumBookingNotice?: true | undefined;
                beforeEventBuffer?: true | undefined;
                afterEventBuffer?: true | undefined;
                seatsPerTimeSlot?: true | undefined;
                onlyShowFirstAvailableSlot?: true | undefined;
                disableCancelling?: true | undefined;
                disableRescheduling?: true | undefined;
                seatsShowAttendees?: true | undefined;
                seatsShowAvailabilityCount?: true | undefined;
                schedulingType?: true | undefined;
                scheduleId?: true | undefined;
                allowReschedulingCancelledBookings?: true | undefined;
                price?: true | undefined;
                currency?: true | undefined;
                slotInterval?: true | undefined;
                successRedirectUrl?: true | undefined;
                forwardParamsSuccessRedirect?: true | undefined;
                durationLimits?: true | undefined;
                isInstantEvent?: true | undefined;
                instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                instantMeetingScheduleId?: true | undefined;
                instantMeetingParameters?: true | undefined;
                assignAllTeamMembers?: true | undefined;
                assignRRMembersUsingSegment?: true | undefined;
                rrSegmentQueryValue?: true | undefined;
                useEventTypeDestinationCalendarEmail?: true | undefined;
                isRRWeightsEnabled?: true | undefined;
                maxLeadThreshold?: true | undefined;
                includeNoShowInRRCalculation?: true | undefined;
                allowReschedulingPastBookings?: true | undefined;
                hideOrganizerEmail?: true | undefined;
                maxActiveBookingsPerBooker?: true | undefined;
                maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                customReplyToEmail?: true | undefined;
                eventTypeColor?: true | undefined;
                rescheduleWithSameRoundRobinHost?: true | undefined;
                secondaryEmailId?: true | undefined;
                useBookerTimezone?: true | undefined;
                restrictionScheduleId?: true | undefined;
                bookingRequiresAuthentication?: true | undefined;
                owner?: true | undefined;
                instantMeetingSchedule?: true | undefined;
                aiPhoneCallConfig?: true | undefined;
                fieldTranslations?: true | undefined;
                restrictionSchedule?: true | undefined;
                hostGroups?: true | undefined;
            }, z.ZodTypeDef, {
                users?: true | undefined;
                children?: true | undefined;
                length?: true | undefined;
                title?: true | undefined;
                metadata?: true | undefined;
                description?: true | undefined;
                userId?: true | undefined;
                calVideoSettings?: true | undefined;
                destinationCalendar?: true | undefined;
                profile?: true | undefined;
                team?: true | undefined;
                schedule?: true | undefined;
                availability?: true | undefined;
                hashedLink?: true | undefined;
                secondaryEmail?: true | undefined;
                customInputs?: true | undefined;
                timeZone?: true | undefined;
                bookings?: true | undefined;
                selectedCalendars?: true | undefined;
                webhooks?: true | undefined;
                workflows?: true | undefined;
                hosts?: true | undefined;
                slug?: true | undefined;
                parentId?: true | undefined;
                bookingLimits?: true | undefined;
                parent?: true | undefined;
                teamId?: true | undefined;
                hidden?: true | undefined;
                _count?: true | undefined;
                interfaceLanguage?: true | undefined;
                position?: true | undefined;
                locations?: true | undefined;
                offsetStart?: true | undefined;
                profileId?: true | undefined;
                useEventLevelSelectedCalendars?: true | undefined;
                eventName?: true | undefined;
                bookingFields?: true | undefined;
                periodType?: true | undefined;
                periodStartDate?: true | undefined;
                periodEndDate?: true | undefined;
                periodDays?: true | undefined;
                periodCountCalendarDays?: true | undefined;
                lockTimeZoneToggleOnBookingPage?: true | undefined;
                lockedTimeZone?: true | undefined;
                requiresConfirmation?: true | undefined;
                requiresConfirmationWillBlockSlot?: true | undefined;
                requiresConfirmationForFreeEmail?: true | undefined;
                requiresBookerEmailVerification?: true | undefined;
                canSendCalVideoTranscriptionEmails?: true | undefined;
                autoTranslateDescriptionEnabled?: true | undefined;
                recurringEvent?: true | undefined;
                disableGuests?: true | undefined;
                hideCalendarNotes?: true | undefined;
                hideCalendarEventDetails?: true | undefined;
                minimumBookingNotice?: true | undefined;
                beforeEventBuffer?: true | undefined;
                afterEventBuffer?: true | undefined;
                seatsPerTimeSlot?: true | undefined;
                onlyShowFirstAvailableSlot?: true | undefined;
                disableCancelling?: true | undefined;
                disableRescheduling?: true | undefined;
                seatsShowAttendees?: true | undefined;
                seatsShowAvailabilityCount?: true | undefined;
                schedulingType?: true | undefined;
                scheduleId?: true | undefined;
                allowReschedulingCancelledBookings?: true | undefined;
                price?: true | undefined;
                currency?: true | undefined;
                slotInterval?: true | undefined;
                successRedirectUrl?: true | undefined;
                forwardParamsSuccessRedirect?: true | undefined;
                durationLimits?: true | undefined;
                isInstantEvent?: true | undefined;
                instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                instantMeetingScheduleId?: true | undefined;
                instantMeetingParameters?: true | undefined;
                assignAllTeamMembers?: true | undefined;
                assignRRMembersUsingSegment?: true | undefined;
                rrSegmentQueryValue?: true | undefined;
                useEventTypeDestinationCalendarEmail?: true | undefined;
                isRRWeightsEnabled?: true | undefined;
                maxLeadThreshold?: true | undefined;
                includeNoShowInRRCalculation?: true | undefined;
                allowReschedulingPastBookings?: true | undefined;
                hideOrganizerEmail?: true | undefined;
                maxActiveBookingsPerBooker?: true | undefined;
                maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                customReplyToEmail?: true | undefined;
                eventTypeColor?: true | undefined;
                rescheduleWithSameRoundRobinHost?: true | undefined;
                secondaryEmailId?: true | undefined;
                useBookerTimezone?: true | undefined;
                restrictionScheduleId?: true | undefined;
                bookingRequiresAuthentication?: true | undefined;
                owner?: true | undefined;
                instantMeetingSchedule?: true | undefined;
                aiPhoneCallConfig?: true | undefined;
                fieldTranslations?: true | undefined;
                restrictionSchedule?: true | undefined;
                hostGroups?: true | undefined;
            }>>;
        }, "strip", z.ZodTypeAny, {
            unlockedFields?: {
                users?: true | undefined;
                children?: true | undefined;
                length?: true | undefined;
                title?: true | undefined;
                metadata?: true | undefined;
                description?: true | undefined;
                userId?: true | undefined;
                calVideoSettings?: true | undefined;
                destinationCalendar?: true | undefined;
                profile?: true | undefined;
                team?: true | undefined;
                schedule?: true | undefined;
                availability?: true | undefined;
                hashedLink?: true | undefined;
                secondaryEmail?: true | undefined;
                customInputs?: true | undefined;
                timeZone?: true | undefined;
                bookings?: true | undefined;
                selectedCalendars?: true | undefined;
                webhooks?: true | undefined;
                workflows?: true | undefined;
                hosts?: true | undefined;
                slug?: true | undefined;
                parentId?: true | undefined;
                bookingLimits?: true | undefined;
                parent?: true | undefined;
                teamId?: true | undefined;
                hidden?: true | undefined;
                _count?: true | undefined;
                interfaceLanguage?: true | undefined;
                position?: true | undefined;
                locations?: true | undefined;
                offsetStart?: true | undefined;
                profileId?: true | undefined;
                useEventLevelSelectedCalendars?: true | undefined;
                eventName?: true | undefined;
                bookingFields?: true | undefined;
                periodType?: true | undefined;
                periodStartDate?: true | undefined;
                periodEndDate?: true | undefined;
                periodDays?: true | undefined;
                periodCountCalendarDays?: true | undefined;
                lockTimeZoneToggleOnBookingPage?: true | undefined;
                lockedTimeZone?: true | undefined;
                requiresConfirmation?: true | undefined;
                requiresConfirmationWillBlockSlot?: true | undefined;
                requiresConfirmationForFreeEmail?: true | undefined;
                requiresBookerEmailVerification?: true | undefined;
                canSendCalVideoTranscriptionEmails?: true | undefined;
                autoTranslateDescriptionEnabled?: true | undefined;
                recurringEvent?: true | undefined;
                disableGuests?: true | undefined;
                hideCalendarNotes?: true | undefined;
                hideCalendarEventDetails?: true | undefined;
                minimumBookingNotice?: true | undefined;
                beforeEventBuffer?: true | undefined;
                afterEventBuffer?: true | undefined;
                seatsPerTimeSlot?: true | undefined;
                onlyShowFirstAvailableSlot?: true | undefined;
                disableCancelling?: true | undefined;
                disableRescheduling?: true | undefined;
                seatsShowAttendees?: true | undefined;
                seatsShowAvailabilityCount?: true | undefined;
                schedulingType?: true | undefined;
                scheduleId?: true | undefined;
                allowReschedulingCancelledBookings?: true | undefined;
                price?: true | undefined;
                currency?: true | undefined;
                slotInterval?: true | undefined;
                successRedirectUrl?: true | undefined;
                forwardParamsSuccessRedirect?: true | undefined;
                durationLimits?: true | undefined;
                isInstantEvent?: true | undefined;
                instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                instantMeetingScheduleId?: true | undefined;
                instantMeetingParameters?: true | undefined;
                assignAllTeamMembers?: true | undefined;
                assignRRMembersUsingSegment?: true | undefined;
                rrSegmentQueryValue?: true | undefined;
                useEventTypeDestinationCalendarEmail?: true | undefined;
                isRRWeightsEnabled?: true | undefined;
                maxLeadThreshold?: true | undefined;
                includeNoShowInRRCalculation?: true | undefined;
                allowReschedulingPastBookings?: true | undefined;
                hideOrganizerEmail?: true | undefined;
                maxActiveBookingsPerBooker?: true | undefined;
                maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                customReplyToEmail?: true | undefined;
                eventTypeColor?: true | undefined;
                rescheduleWithSameRoundRobinHost?: true | undefined;
                secondaryEmailId?: true | undefined;
                useBookerTimezone?: true | undefined;
                restrictionScheduleId?: true | undefined;
                bookingRequiresAuthentication?: true | undefined;
                owner?: true | undefined;
                instantMeetingSchedule?: true | undefined;
                aiPhoneCallConfig?: true | undefined;
                fieldTranslations?: true | undefined;
                restrictionSchedule?: true | undefined;
                hostGroups?: true | undefined;
            } | undefined;
        }, {
            unlockedFields?: {
                users?: true | undefined;
                children?: true | undefined;
                length?: true | undefined;
                title?: true | undefined;
                metadata?: true | undefined;
                description?: true | undefined;
                userId?: true | undefined;
                calVideoSettings?: true | undefined;
                destinationCalendar?: true | undefined;
                profile?: true | undefined;
                team?: true | undefined;
                schedule?: true | undefined;
                availability?: true | undefined;
                hashedLink?: true | undefined;
                secondaryEmail?: true | undefined;
                customInputs?: true | undefined;
                timeZone?: true | undefined;
                bookings?: true | undefined;
                selectedCalendars?: true | undefined;
                webhooks?: true | undefined;
                workflows?: true | undefined;
                hosts?: true | undefined;
                slug?: true | undefined;
                parentId?: true | undefined;
                bookingLimits?: true | undefined;
                parent?: true | undefined;
                teamId?: true | undefined;
                hidden?: true | undefined;
                _count?: true | undefined;
                interfaceLanguage?: true | undefined;
                position?: true | undefined;
                locations?: true | undefined;
                offsetStart?: true | undefined;
                profileId?: true | undefined;
                useEventLevelSelectedCalendars?: true | undefined;
                eventName?: true | undefined;
                bookingFields?: true | undefined;
                periodType?: true | undefined;
                periodStartDate?: true | undefined;
                periodEndDate?: true | undefined;
                periodDays?: true | undefined;
                periodCountCalendarDays?: true | undefined;
                lockTimeZoneToggleOnBookingPage?: true | undefined;
                lockedTimeZone?: true | undefined;
                requiresConfirmation?: true | undefined;
                requiresConfirmationWillBlockSlot?: true | undefined;
                requiresConfirmationForFreeEmail?: true | undefined;
                requiresBookerEmailVerification?: true | undefined;
                canSendCalVideoTranscriptionEmails?: true | undefined;
                autoTranslateDescriptionEnabled?: true | undefined;
                recurringEvent?: true | undefined;
                disableGuests?: true | undefined;
                hideCalendarNotes?: true | undefined;
                hideCalendarEventDetails?: true | undefined;
                minimumBookingNotice?: true | undefined;
                beforeEventBuffer?: true | undefined;
                afterEventBuffer?: true | undefined;
                seatsPerTimeSlot?: true | undefined;
                onlyShowFirstAvailableSlot?: true | undefined;
                disableCancelling?: true | undefined;
                disableRescheduling?: true | undefined;
                seatsShowAttendees?: true | undefined;
                seatsShowAvailabilityCount?: true | undefined;
                schedulingType?: true | undefined;
                scheduleId?: true | undefined;
                allowReschedulingCancelledBookings?: true | undefined;
                price?: true | undefined;
                currency?: true | undefined;
                slotInterval?: true | undefined;
                successRedirectUrl?: true | undefined;
                forwardParamsSuccessRedirect?: true | undefined;
                durationLimits?: true | undefined;
                isInstantEvent?: true | undefined;
                instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                instantMeetingScheduleId?: true | undefined;
                instantMeetingParameters?: true | undefined;
                assignAllTeamMembers?: true | undefined;
                assignRRMembersUsingSegment?: true | undefined;
                rrSegmentQueryValue?: true | undefined;
                useEventTypeDestinationCalendarEmail?: true | undefined;
                isRRWeightsEnabled?: true | undefined;
                maxLeadThreshold?: true | undefined;
                includeNoShowInRRCalculation?: true | undefined;
                allowReschedulingPastBookings?: true | undefined;
                hideOrganizerEmail?: true | undefined;
                maxActiveBookingsPerBooker?: true | undefined;
                maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                customReplyToEmail?: true | undefined;
                eventTypeColor?: true | undefined;
                rescheduleWithSameRoundRobinHost?: true | undefined;
                secondaryEmailId?: true | undefined;
                useBookerTimezone?: true | undefined;
                restrictionScheduleId?: true | undefined;
                bookingRequiresAuthentication?: true | undefined;
                owner?: true | undefined;
                instantMeetingSchedule?: true | undefined;
                aiPhoneCallConfig?: true | undefined;
                fieldTranslations?: true | undefined;
                restrictionSchedule?: true | undefined;
                hostGroups?: true | undefined;
            } | undefined;
        }>>;
        requiresConfirmationThreshold: z.ZodOptional<z.ZodObject<{
            time: z.ZodNumber;
            unit: z.ZodType<import("dayjs").UnitTypeLongPlural, z.ZodTypeDef, import("dayjs").UnitTypeLongPlural>;
        }, "strip", z.ZodTypeAny, {
            time: number;
            unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
        }, {
            time: number;
            unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
        }>>;
        bookerLayouts: z.ZodOptional<z.ZodNullable<z.ZodObject<{
            enabledLayouts: z.ZodArray<z.ZodUnion<[z.ZodLiteral<import("@calcom/prisma/zod-utils").BookerLayouts>, z.ZodLiteral<import("@calcom/prisma/zod-utils").BookerLayouts>, z.ZodLiteral<import("@calcom/prisma/zod-utils").BookerLayouts>]>, "many">;
            defaultLayout: z.ZodUnion<[z.ZodLiteral<import("@calcom/prisma/zod-utils").BookerLayouts>, z.ZodLiteral<import("@calcom/prisma/zod-utils").BookerLayouts>, z.ZodLiteral<import("@calcom/prisma/zod-utils").BookerLayouts>]>;
        }, "strip", z.ZodTypeAny, {
            enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
            defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
        }, {
            enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
            defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
        }>>>;
        apps: z.ZodOptional<z.ZodUnknown>;
    }, "strip", z.ZodTypeAny, {
        config?: {
            useHostSchedulesForTeamEvent?: boolean | undefined;
        } | undefined;
        smartContractAddress?: string | undefined;
        blockchainId?: number | undefined;
        multipleDuration?: number[] | undefined;
        giphyThankYouPage?: string | undefined;
        additionalNotesRequired?: boolean | undefined;
        disableSuccessPage?: boolean | undefined;
        disableStandardEmails?: {
            all?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
            confirmation?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
        } | undefined;
        managedEventConfig?: {
            unlockedFields?: {
                users?: true | undefined;
                children?: true | undefined;
                length?: true | undefined;
                title?: true | undefined;
                metadata?: true | undefined;
                description?: true | undefined;
                userId?: true | undefined;
                calVideoSettings?: true | undefined;
                destinationCalendar?: true | undefined;
                profile?: true | undefined;
                team?: true | undefined;
                schedule?: true | undefined;
                availability?: true | undefined;
                hashedLink?: true | undefined;
                secondaryEmail?: true | undefined;
                customInputs?: true | undefined;
                timeZone?: true | undefined;
                bookings?: true | undefined;
                selectedCalendars?: true | undefined;
                webhooks?: true | undefined;
                workflows?: true | undefined;
                hosts?: true | undefined;
                slug?: true | undefined;
                parentId?: true | undefined;
                bookingLimits?: true | undefined;
                parent?: true | undefined;
                teamId?: true | undefined;
                hidden?: true | undefined;
                _count?: true | undefined;
                interfaceLanguage?: true | undefined;
                position?: true | undefined;
                locations?: true | undefined;
                offsetStart?: true | undefined;
                profileId?: true | undefined;
                useEventLevelSelectedCalendars?: true | undefined;
                eventName?: true | undefined;
                bookingFields?: true | undefined;
                periodType?: true | undefined;
                periodStartDate?: true | undefined;
                periodEndDate?: true | undefined;
                periodDays?: true | undefined;
                periodCountCalendarDays?: true | undefined;
                lockTimeZoneToggleOnBookingPage?: true | undefined;
                lockedTimeZone?: true | undefined;
                requiresConfirmation?: true | undefined;
                requiresConfirmationWillBlockSlot?: true | undefined;
                requiresConfirmationForFreeEmail?: true | undefined;
                requiresBookerEmailVerification?: true | undefined;
                canSendCalVideoTranscriptionEmails?: true | undefined;
                autoTranslateDescriptionEnabled?: true | undefined;
                recurringEvent?: true | undefined;
                disableGuests?: true | undefined;
                hideCalendarNotes?: true | undefined;
                hideCalendarEventDetails?: true | undefined;
                minimumBookingNotice?: true | undefined;
                beforeEventBuffer?: true | undefined;
                afterEventBuffer?: true | undefined;
                seatsPerTimeSlot?: true | undefined;
                onlyShowFirstAvailableSlot?: true | undefined;
                disableCancelling?: true | undefined;
                disableRescheduling?: true | undefined;
                seatsShowAttendees?: true | undefined;
                seatsShowAvailabilityCount?: true | undefined;
                schedulingType?: true | undefined;
                scheduleId?: true | undefined;
                allowReschedulingCancelledBookings?: true | undefined;
                price?: true | undefined;
                currency?: true | undefined;
                slotInterval?: true | undefined;
                successRedirectUrl?: true | undefined;
                forwardParamsSuccessRedirect?: true | undefined;
                durationLimits?: true | undefined;
                isInstantEvent?: true | undefined;
                instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                instantMeetingScheduleId?: true | undefined;
                instantMeetingParameters?: true | undefined;
                assignAllTeamMembers?: true | undefined;
                assignRRMembersUsingSegment?: true | undefined;
                rrSegmentQueryValue?: true | undefined;
                useEventTypeDestinationCalendarEmail?: true | undefined;
                isRRWeightsEnabled?: true | undefined;
                maxLeadThreshold?: true | undefined;
                includeNoShowInRRCalculation?: true | undefined;
                allowReschedulingPastBookings?: true | undefined;
                hideOrganizerEmail?: true | undefined;
                maxActiveBookingsPerBooker?: true | undefined;
                maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                customReplyToEmail?: true | undefined;
                eventTypeColor?: true | undefined;
                rescheduleWithSameRoundRobinHost?: true | undefined;
                secondaryEmailId?: true | undefined;
                useBookerTimezone?: true | undefined;
                restrictionScheduleId?: true | undefined;
                bookingRequiresAuthentication?: true | undefined;
                owner?: true | undefined;
                instantMeetingSchedule?: true | undefined;
                aiPhoneCallConfig?: true | undefined;
                fieldTranslations?: true | undefined;
                restrictionSchedule?: true | undefined;
                hostGroups?: true | undefined;
            } | undefined;
        } | undefined;
        requiresConfirmationThreshold?: {
            time: number;
            unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
        } | undefined;
        bookerLayouts?: {
            enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
            defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
        } | null | undefined;
        apps?: unknown;
    }, {
        config?: {
            useHostSchedulesForTeamEvent?: boolean | undefined;
        } | undefined;
        smartContractAddress?: string | undefined;
        blockchainId?: number | undefined;
        multipleDuration?: number[] | undefined;
        giphyThankYouPage?: string | undefined;
        additionalNotesRequired?: boolean | undefined;
        disableSuccessPage?: boolean | undefined;
        disableStandardEmails?: {
            all?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
            confirmation?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
        } | undefined;
        managedEventConfig?: {
            unlockedFields?: {
                users?: true | undefined;
                children?: true | undefined;
                length?: true | undefined;
                title?: true | undefined;
                metadata?: true | undefined;
                description?: true | undefined;
                userId?: true | undefined;
                calVideoSettings?: true | undefined;
                destinationCalendar?: true | undefined;
                profile?: true | undefined;
                team?: true | undefined;
                schedule?: true | undefined;
                availability?: true | undefined;
                hashedLink?: true | undefined;
                secondaryEmail?: true | undefined;
                customInputs?: true | undefined;
                timeZone?: true | undefined;
                bookings?: true | undefined;
                selectedCalendars?: true | undefined;
                webhooks?: true | undefined;
                workflows?: true | undefined;
                hosts?: true | undefined;
                slug?: true | undefined;
                parentId?: true | undefined;
                bookingLimits?: true | undefined;
                parent?: true | undefined;
                teamId?: true | undefined;
                hidden?: true | undefined;
                _count?: true | undefined;
                interfaceLanguage?: true | undefined;
                position?: true | undefined;
                locations?: true | undefined;
                offsetStart?: true | undefined;
                profileId?: true | undefined;
                useEventLevelSelectedCalendars?: true | undefined;
                eventName?: true | undefined;
                bookingFields?: true | undefined;
                periodType?: true | undefined;
                periodStartDate?: true | undefined;
                periodEndDate?: true | undefined;
                periodDays?: true | undefined;
                periodCountCalendarDays?: true | undefined;
                lockTimeZoneToggleOnBookingPage?: true | undefined;
                lockedTimeZone?: true | undefined;
                requiresConfirmation?: true | undefined;
                requiresConfirmationWillBlockSlot?: true | undefined;
                requiresConfirmationForFreeEmail?: true | undefined;
                requiresBookerEmailVerification?: true | undefined;
                canSendCalVideoTranscriptionEmails?: true | undefined;
                autoTranslateDescriptionEnabled?: true | undefined;
                recurringEvent?: true | undefined;
                disableGuests?: true | undefined;
                hideCalendarNotes?: true | undefined;
                hideCalendarEventDetails?: true | undefined;
                minimumBookingNotice?: true | undefined;
                beforeEventBuffer?: true | undefined;
                afterEventBuffer?: true | undefined;
                seatsPerTimeSlot?: true | undefined;
                onlyShowFirstAvailableSlot?: true | undefined;
                disableCancelling?: true | undefined;
                disableRescheduling?: true | undefined;
                seatsShowAttendees?: true | undefined;
                seatsShowAvailabilityCount?: true | undefined;
                schedulingType?: true | undefined;
                scheduleId?: true | undefined;
                allowReschedulingCancelledBookings?: true | undefined;
                price?: true | undefined;
                currency?: true | undefined;
                slotInterval?: true | undefined;
                successRedirectUrl?: true | undefined;
                forwardParamsSuccessRedirect?: true | undefined;
                durationLimits?: true | undefined;
                isInstantEvent?: true | undefined;
                instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                instantMeetingScheduleId?: true | undefined;
                instantMeetingParameters?: true | undefined;
                assignAllTeamMembers?: true | undefined;
                assignRRMembersUsingSegment?: true | undefined;
                rrSegmentQueryValue?: true | undefined;
                useEventTypeDestinationCalendarEmail?: true | undefined;
                isRRWeightsEnabled?: true | undefined;
                maxLeadThreshold?: true | undefined;
                includeNoShowInRRCalculation?: true | undefined;
                allowReschedulingPastBookings?: true | undefined;
                hideOrganizerEmail?: true | undefined;
                maxActiveBookingsPerBooker?: true | undefined;
                maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                customReplyToEmail?: true | undefined;
                eventTypeColor?: true | undefined;
                rescheduleWithSameRoundRobinHost?: true | undefined;
                secondaryEmailId?: true | undefined;
                useBookerTimezone?: true | undefined;
                restrictionScheduleId?: true | undefined;
                bookingRequiresAuthentication?: true | undefined;
                owner?: true | undefined;
                instantMeetingSchedule?: true | undefined;
                aiPhoneCallConfig?: true | undefined;
                fieldTranslations?: true | undefined;
                restrictionSchedule?: true | undefined;
                hostGroups?: true | undefined;
            } | undefined;
        } | undefined;
        requiresConfirmationThreshold?: {
            time: number;
            unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
        } | undefined;
        bookerLayouts?: {
            enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
            defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
        } | null | undefined;
        apps?: unknown;
    }>>>;
    description: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodString>>>;
    userId: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodNumber>>>;
    calVideoSettings: z.ZodOptional<z.ZodNullable<z.ZodOptional<z.ZodObject<{
        disableRecordingForGuests: z.ZodOptional<z.ZodNullable<z.ZodBoolean>>;
        disableRecordingForOrganizer: z.ZodOptional<z.ZodNullable<z.ZodBoolean>>;
        enableAutomaticTranscription: z.ZodOptional<z.ZodNullable<z.ZodBoolean>>;
        enableAutomaticRecordingForOrganizer: z.ZodOptional<z.ZodNullable<z.ZodBoolean>>;
        disableTranscriptionForGuests: z.ZodOptional<z.ZodNullable<z.ZodBoolean>>;
        disableTranscriptionForOrganizer: z.ZodOptional<z.ZodNullable<z.ZodBoolean>>;
        redirectUrlOnExit: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    }, "strip", z.ZodTypeAny, {
        disableRecordingForGuests?: boolean | null | undefined;
        disableRecordingForOrganizer?: boolean | null | undefined;
        enableAutomaticTranscription?: boolean | null | undefined;
        enableAutomaticRecordingForOrganizer?: boolean | null | undefined;
        disableTranscriptionForGuests?: boolean | null | undefined;
        disableTranscriptionForOrganizer?: boolean | null | undefined;
        redirectUrlOnExit?: string | null | undefined;
    }, {
        disableRecordingForGuests?: boolean | null | undefined;
        disableRecordingForOrganizer?: boolean | null | undefined;
        enableAutomaticTranscription?: boolean | null | undefined;
        enableAutomaticRecordingForOrganizer?: boolean | null | undefined;
        disableTranscriptionForGuests?: boolean | null | undefined;
        disableTranscriptionForOrganizer?: boolean | null | undefined;
        redirectUrlOnExit?: string | null | undefined;
    }>>>>;
    destinationCalendar: z.ZodOptional<z.ZodNullable<z.ZodObject<Pick<{
        id: z.ZodNumber;
        integration: z.ZodString;
        externalId: z.ZodString;
        primaryEmail: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        userId: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
        eventTypeId: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
        credentialId: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
        createdAt: z.ZodOptional<z.ZodNullable<z.ZodDate>>;
        updatedAt: z.ZodOptional<z.ZodNullable<z.ZodDate>>;
        delegationCredentialId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        domainWideDelegationCredentialId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    }, "integration" | "externalId">, "strip", z.ZodTypeAny, {
        integration: string;
        externalId: string;
    }, {
        integration: string;
        externalId: string;
    }>>>;
    schedule: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
    customInputs: z.ZodOptional<z.ZodArray<z.ZodObject<{
        id: z.ZodNumber;
        eventTypeId: z.ZodNumber;
        label: z.ZodString;
        type: z.ZodNativeEnum<{
            readonly TEXT: "TEXT";
            readonly TEXTLONG: "TEXTLONG";
            readonly NUMBER: "NUMBER";
            readonly BOOL: "BOOL";
            readonly RADIO: "RADIO";
            readonly PHONE: "PHONE";
        }>;
        options: z.ZodNullable<z.ZodOptional<z.ZodArray<z.ZodObject<{
            label: z.ZodString;
            type: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            type: string;
            label: string;
        }, {
            type: string;
            label: string;
        }>, "many">>>;
        required: z.ZodBoolean;
        placeholder: z.ZodString;
        hasToBeCreated: z.ZodOptional<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        id: number;
        type: "TEXT" | "TEXTLONG" | "NUMBER" | "BOOL" | "RADIO" | "PHONE";
        label: string;
        required: boolean;
        eventTypeId: number;
        placeholder: string;
        options?: {
            type: string;
            label: string;
        }[] | null | undefined;
        hasToBeCreated?: boolean | undefined;
    }, {
        id: number;
        type: "TEXT" | "TEXTLONG" | "NUMBER" | "BOOL" | "RADIO" | "PHONE";
        label: string;
        required: boolean;
        eventTypeId: number;
        placeholder: string;
        options?: {
            type: string;
            label: string;
        }[] | null | undefined;
        hasToBeCreated?: boolean | undefined;
    }>, "many">>;
    timeZone: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodString>>>;
    hosts: z.ZodOptional<z.ZodArray<z.ZodObject<{
        userId: z.ZodNumber;
        profileId: z.ZodOptional<z.ZodUnion<[z.ZodNumber, z.ZodNull]>>;
        isFixed: z.ZodOptional<z.ZodBoolean>;
        priority: z.ZodNullable<z.ZodOptional<z.ZodNumber>>;
        weight: z.ZodNullable<z.ZodOptional<z.ZodNumber>>;
        scheduleId: z.ZodNullable<z.ZodOptional<z.ZodNumber>>;
        groupId: z.ZodNullable<z.ZodOptional<z.ZodString>>;
    }, "strip", z.ZodTypeAny, {
        userId: number;
        profileId?: number | null | undefined;
        isFixed?: boolean | undefined;
        priority?: number | null | undefined;
        weight?: number | null | undefined;
        scheduleId?: number | null | undefined;
        groupId?: string | null | undefined;
    }, {
        userId: number;
        profileId?: number | null | undefined;
        isFixed?: boolean | undefined;
        priority?: number | null | undefined;
        weight?: number | null | undefined;
        scheduleId?: number | null | undefined;
        groupId?: string | null | undefined;
    }>, "many">>;
    slug: z.ZodOptional<z.ZodEffects<z.ZodEffects<z.ZodString, string, string>, string, string>>;
    parentId: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodNumber>>>;
    bookingLimits: z.ZodOptional<z.ZodType<Partial<Record<"PER_DAY" | "PER_MONTH" | "PER_WEEK" | "PER_YEAR", number | undefined>> | null, z.ZodTypeDef, Partial<Record<"PER_DAY" | "PER_MONTH" | "PER_WEEK" | "PER_YEAR", number | undefined>> | null>>;
    teamId: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodNumber>>>;
    hidden: z.ZodOptional<z.ZodBoolean>;
    interfaceLanguage: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodString>>>;
    position: z.ZodOptional<z.ZodNumber>;
    locations: z.ZodOptional<z.ZodArray<z.ZodObject<{
        type: z.ZodString;
        address: z.ZodOptional<z.ZodString>;
        link: z.ZodOptional<z.ZodString>;
        displayLocationPublicly: z.ZodOptional<z.ZodBoolean>;
        hostPhoneNumber: z.ZodOptional<z.ZodString>;
        credentialId: z.ZodOptional<z.ZodNumber>;
        teamName: z.ZodOptional<z.ZodString>;
        customLabel: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        type: string;
        address?: string | undefined;
        link?: string | undefined;
        displayLocationPublicly?: boolean | undefined;
        hostPhoneNumber?: string | undefined;
        credentialId?: number | undefined;
        teamName?: string | undefined;
        customLabel?: string | undefined;
    }, {
        type: string;
        address?: string | undefined;
        link?: string | undefined;
        displayLocationPublicly?: boolean | undefined;
        hostPhoneNumber?: string | undefined;
        credentialId?: number | undefined;
        teamName?: string | undefined;
        customLabel?: string | undefined;
    }>, "many">>;
    offsetStart: z.ZodOptional<z.ZodNumber>;
    profileId: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodNumber>>>;
    useEventLevelSelectedCalendars: z.ZodOptional<z.ZodOptional<z.ZodBoolean>>;
    eventName: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodString>>>;
    bookingFields: z.ZodOptional<z.ZodArray<z.ZodObject<{
        name: z.ZodEffects<z.ZodString, string, string>;
        type: z.ZodEnum<["name", "text", "textarea", "number", "email", "phone", "address", "multiemail", "select", "multiselect", "checkbox", "radio", "radioInput", "boolean", "url"]>;
        label: z.ZodOptional<z.ZodString>;
        options: z.ZodOptional<z.ZodArray<z.ZodObject<{
            label: z.ZodString;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            label: string;
            value: string;
        }, {
            label: string;
            value: string;
        }>, "many">>;
        required: z.ZodOptional<z.ZodDefault<z.ZodBoolean>>;
        placeholder: z.ZodOptional<z.ZodString>;
        maxLength: z.ZodOptional<z.ZodNumber>;
        defaultLabel: z.ZodOptional<z.ZodString>;
        defaultPlaceholder: z.ZodOptional<z.ZodString>;
        labelAsSafeHtml: z.ZodOptional<z.ZodString>;
        getOptionsAt: z.ZodOptional<z.ZodString>;
        optionsInputs: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodObject<{
            type: z.ZodEnum<["address", "phone", "text"]>;
            required: z.ZodOptional<z.ZodBoolean>;
            placeholder: z.ZodOptional<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            type: "phone" | "address" | "text";
            required?: boolean | undefined;
            placeholder?: string | undefined;
        }, {
            type: "phone" | "address" | "text";
            required?: boolean | undefined;
            placeholder?: string | undefined;
        }>>>;
        minLength: z.ZodOptional<z.ZodNumber>;
        excludeEmails: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
        requireEmails: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
        variant: z.ZodOptional<z.ZodString>;
        variantsConfig: z.ZodOptional<z.ZodObject<{
            variants: z.ZodRecord<z.ZodString, z.ZodObject<{
                fields: z.ZodArray<z.ZodObject<Omit<{
                    name: z.ZodEffects<z.ZodString, string, string>;
                    type: z.ZodEnum<["name", "text", "textarea", "number", "email", "phone", "address", "multiemail", "select", "multiselect", "checkbox", "radio", "radioInput", "boolean", "url"]>;
                    label: z.ZodOptional<z.ZodString>;
                    labelAsSafeHtml: z.ZodOptional<z.ZodString>;
                    defaultLabel: z.ZodOptional<z.ZodString>;
                    placeholder: z.ZodOptional<z.ZodString>;
                    defaultPlaceholder: z.ZodOptional<z.ZodString>;
                    required: z.ZodOptional<z.ZodDefault<z.ZodBoolean>>;
                    options: z.ZodOptional<z.ZodArray<z.ZodObject<{
                        label: z.ZodString;
                        value: z.ZodString;
                    }, "strip", z.ZodTypeAny, {
                        label: string;
                        value: string;
                    }, {
                        label: string;
                        value: string;
                    }>, "many">>;
                    getOptionsAt: z.ZodOptional<z.ZodString>;
                    optionsInputs: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodObject<{
                        type: z.ZodEnum<["address", "phone", "text"]>;
                        required: z.ZodOptional<z.ZodBoolean>;
                        placeholder: z.ZodOptional<z.ZodString>;
                    }, "strip", z.ZodTypeAny, {
                        type: "phone" | "address" | "text";
                        required?: boolean | undefined;
                        placeholder?: string | undefined;
                    }, {
                        type: "phone" | "address" | "text";
                        required?: boolean | undefined;
                        placeholder?: string | undefined;
                    }>>>;
                    minLength: z.ZodOptional<z.ZodNumber>;
                    maxLength: z.ZodOptional<z.ZodNumber>;
                    excludeEmails: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
                    requireEmails: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
                }, "options" | "defaultLabel" | "defaultPlaceholder" | "getOptionsAt" | "optionsInputs">, "strip", z.ZodTypeAny, {
                    name: string;
                    type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
                    label?: string | undefined;
                    required?: boolean | undefined;
                    placeholder?: string | undefined;
                    maxLength?: number | undefined;
                    labelAsSafeHtml?: string | undefined;
                    minLength?: number | undefined;
                    excludeEmails?: string | undefined;
                    requireEmails?: string | undefined;
                }, {
                    name: string;
                    type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
                    label?: string | undefined;
                    required?: boolean | undefined;
                    placeholder?: string | undefined;
                    maxLength?: number | undefined;
                    labelAsSafeHtml?: string | undefined;
                    minLength?: number | undefined;
                    excludeEmails?: string | undefined;
                    requireEmails?: string | undefined;
                }>, "many">;
            }, "strip", z.ZodTypeAny, {
                fields: {
                    name: string;
                    type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
                    label?: string | undefined;
                    required?: boolean | undefined;
                    placeholder?: string | undefined;
                    maxLength?: number | undefined;
                    labelAsSafeHtml?: string | undefined;
                    minLength?: number | undefined;
                    excludeEmails?: string | undefined;
                    requireEmails?: string | undefined;
                }[];
            }, {
                fields: {
                    name: string;
                    type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
                    label?: string | undefined;
                    required?: boolean | undefined;
                    placeholder?: string | undefined;
                    maxLength?: number | undefined;
                    labelAsSafeHtml?: string | undefined;
                    minLength?: number | undefined;
                    excludeEmails?: string | undefined;
                    requireEmails?: string | undefined;
                }[];
            }>>;
        }, "strip", z.ZodTypeAny, {
            variants: Record<string, {
                fields: {
                    name: string;
                    type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
                    label?: string | undefined;
                    required?: boolean | undefined;
                    placeholder?: string | undefined;
                    maxLength?: number | undefined;
                    labelAsSafeHtml?: string | undefined;
                    minLength?: number | undefined;
                    excludeEmails?: string | undefined;
                    requireEmails?: string | undefined;
                }[];
            }>;
        }, {
            variants: Record<string, {
                fields: {
                    name: string;
                    type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
                    label?: string | undefined;
                    required?: boolean | undefined;
                    placeholder?: string | undefined;
                    maxLength?: number | undefined;
                    labelAsSafeHtml?: string | undefined;
                    minLength?: number | undefined;
                    excludeEmails?: string | undefined;
                    requireEmails?: string | undefined;
                }[];
            }>;
        }>>;
        views: z.ZodOptional<z.ZodArray<z.ZodObject<{
            label: z.ZodString;
            id: z.ZodString;
            description: z.ZodOptional<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            id: string;
            label: string;
            description?: string | undefined;
        }, {
            id: string;
            label: string;
            description?: string | undefined;
        }>, "many">>;
        hideWhenJustOneOption: z.ZodOptional<z.ZodDefault<z.ZodBoolean>>;
        hidden: z.ZodOptional<z.ZodBoolean>;
        editable: z.ZodOptional<z.ZodDefault<z.ZodEnum<["system", "system-but-optional", "system-but-hidden", "user", "user-readonly"]>>>;
        sources: z.ZodOptional<z.ZodArray<z.ZodObject<{
            id: z.ZodString;
            type: z.ZodUnion<[z.ZodLiteral<"user">, z.ZodLiteral<"system">, z.ZodString]>;
            label: z.ZodString;
            editUrl: z.ZodOptional<z.ZodString>;
            fieldRequired: z.ZodOptional<z.ZodBoolean>;
        }, "strip", z.ZodTypeAny, {
            id: string;
            type: string;
            label: string;
            editUrl?: string | undefined;
            fieldRequired?: boolean | undefined;
        }, {
            id: string;
            type: string;
            label: string;
            editUrl?: string | undefined;
            fieldRequired?: boolean | undefined;
        }>, "many">>;
        disableOnPrefill: z.ZodOptional<z.ZodDefault<z.ZodBoolean>>;
    }, "strip", z.ZodTypeAny, {
        name: string;
        type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
        label?: string | undefined;
        options?: {
            label: string;
            value: string;
        }[] | undefined;
        required?: boolean | undefined;
        placeholder?: string | undefined;
        maxLength?: number | undefined;
        defaultLabel?: string | undefined;
        defaultPlaceholder?: string | undefined;
        labelAsSafeHtml?: string | undefined;
        getOptionsAt?: string | undefined;
        optionsInputs?: Record<string, {
            type: "phone" | "address" | "text";
            required?: boolean | undefined;
            placeholder?: string | undefined;
        }> | undefined;
        minLength?: number | undefined;
        excludeEmails?: string | undefined;
        requireEmails?: string | undefined;
        variant?: string | undefined;
        variantsConfig?: {
            variants: Record<string, {
                fields: {
                    name: string;
                    type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
                    label?: string | undefined;
                    required?: boolean | undefined;
                    placeholder?: string | undefined;
                    maxLength?: number | undefined;
                    labelAsSafeHtml?: string | undefined;
                    minLength?: number | undefined;
                    excludeEmails?: string | undefined;
                    requireEmails?: string | undefined;
                }[];
            }>;
        } | undefined;
        views?: {
            id: string;
            label: string;
            description?: string | undefined;
        }[] | undefined;
        hideWhenJustOneOption?: boolean | undefined;
        hidden?: boolean | undefined;
        editable?: "user" | "system" | "system-but-optional" | "system-but-hidden" | "user-readonly" | undefined;
        sources?: {
            id: string;
            type: string;
            label: string;
            editUrl?: string | undefined;
            fieldRequired?: boolean | undefined;
        }[] | undefined;
        disableOnPrefill?: boolean | undefined;
    }, {
        name: string;
        type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
        label?: string | undefined;
        options?: {
            label: string;
            value: string;
        }[] | undefined;
        required?: boolean | undefined;
        placeholder?: string | undefined;
        maxLength?: number | undefined;
        defaultLabel?: string | undefined;
        defaultPlaceholder?: string | undefined;
        labelAsSafeHtml?: string | undefined;
        getOptionsAt?: string | undefined;
        optionsInputs?: Record<string, {
            type: "phone" | "address" | "text";
            required?: boolean | undefined;
            placeholder?: string | undefined;
        }> | undefined;
        minLength?: number | undefined;
        excludeEmails?: string | undefined;
        requireEmails?: string | undefined;
        variant?: string | undefined;
        variantsConfig?: {
            variants: Record<string, {
                fields: {
                    name: string;
                    type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
                    label?: string | undefined;
                    required?: boolean | undefined;
                    placeholder?: string | undefined;
                    maxLength?: number | undefined;
                    labelAsSafeHtml?: string | undefined;
                    minLength?: number | undefined;
                    excludeEmails?: string | undefined;
                    requireEmails?: string | undefined;
                }[];
            }>;
        } | undefined;
        views?: {
            id: string;
            label: string;
            description?: string | undefined;
        }[] | undefined;
        hideWhenJustOneOption?: boolean | undefined;
        hidden?: boolean | undefined;
        editable?: "user" | "system" | "system-but-optional" | "system-but-hidden" | "user-readonly" | undefined;
        sources?: {
            id: string;
            type: string;
            label: string;
            editUrl?: string | undefined;
            fieldRequired?: boolean | undefined;
        }[] | undefined;
        disableOnPrefill?: boolean | undefined;
    }>, "many">>;
    periodType: z.ZodOptional<z.ZodNativeEnum<{
        UNLIMITED: "UNLIMITED";
        ROLLING: "ROLLING";
        ROLLING_WINDOW: "ROLLING_WINDOW";
        RANGE: "RANGE";
    }>>;
    periodStartDate: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodDate>>>;
    periodEndDate: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodDate>>>;
    periodDays: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodNumber>>>;
    periodCountCalendarDays: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodBoolean>>>;
    lockTimeZoneToggleOnBookingPage: z.ZodOptional<z.ZodBoolean>;
    lockedTimeZone: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodString>>>;
    requiresConfirmation: z.ZodOptional<z.ZodBoolean>;
    requiresConfirmationWillBlockSlot: z.ZodOptional<z.ZodBoolean>;
    requiresConfirmationForFreeEmail: z.ZodOptional<z.ZodBoolean>;
    requiresBookerEmailVerification: z.ZodOptional<z.ZodBoolean>;
    canSendCalVideoTranscriptionEmails: z.ZodOptional<z.ZodBoolean>;
    autoTranslateDescriptionEnabled: z.ZodOptional<z.ZodBoolean>;
    recurringEvent: z.ZodOptional<z.ZodNullable<z.ZodObject<{
        dtstart: z.ZodOptional<z.ZodDate>;
        interval: z.ZodNumber;
        count: z.ZodNumber;
        freq: z.ZodNativeEnum<typeof import("@calcom/prisma/zod-utils").Frequency>;
        until: z.ZodOptional<z.ZodDate>;
        tzid: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        count: number;
        interval: number;
        freq: import("@calcom/prisma/zod-utils").Frequency;
        dtstart?: Date | undefined;
        until?: Date | undefined;
        tzid?: string | undefined;
    }, {
        count: number;
        interval: number;
        freq: import("@calcom/prisma/zod-utils").Frequency;
        dtstart?: Date | undefined;
        until?: Date | undefined;
        tzid?: string | undefined;
    }>>>;
    disableGuests: z.ZodOptional<z.ZodBoolean>;
    hideCalendarNotes: z.ZodOptional<z.ZodBoolean>;
    hideCalendarEventDetails: z.ZodOptional<z.ZodBoolean>;
    minimumBookingNotice: z.ZodOptional<z.ZodNumber>;
    beforeEventBuffer: z.ZodOptional<z.ZodNumber>;
    afterEventBuffer: z.ZodOptional<z.ZodNumber>;
    seatsPerTimeSlot: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodNumber>>>;
    onlyShowFirstAvailableSlot: z.ZodOptional<z.ZodBoolean>;
    disableCancelling: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodBoolean>>>;
    disableRescheduling: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodBoolean>>>;
    seatsShowAttendees: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodBoolean>>>;
    seatsShowAvailabilityCount: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodBoolean>>>;
    schedulingType: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodNativeEnum<{
        ROUND_ROBIN: "ROUND_ROBIN";
        COLLECTIVE: "COLLECTIVE";
        MANAGED: "MANAGED";
    }>>>>;
    scheduleId: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodNumber>>>;
    allowReschedulingCancelledBookings: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodBoolean>>>;
    price: z.ZodOptional<z.ZodNumber>;
    currency: z.ZodOptional<z.ZodString>;
    slotInterval: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodNumber>>>;
    successRedirectUrl: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodOptional<z.ZodUnion<[z.ZodLiteral<"">, z.ZodString]>>>>>;
    forwardParamsSuccessRedirect: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodBoolean>>>;
    durationLimits: z.ZodOptional<z.ZodType<Partial<Record<"PER_DAY" | "PER_MONTH" | "PER_WEEK" | "PER_YEAR", number | undefined>> | null, z.ZodTypeDef, Partial<Record<"PER_DAY" | "PER_MONTH" | "PER_WEEK" | "PER_YEAR", number | undefined>> | null>>;
    isInstantEvent: z.ZodOptional<z.ZodBoolean>;
    instantMeetingExpiryTimeOffsetInSeconds: z.ZodOptional<z.ZodNumber>;
    instantMeetingScheduleId: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodNumber>>>;
    instantMeetingParameters: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    assignAllTeamMembers: z.ZodOptional<z.ZodBoolean>;
    assignRRMembersUsingSegment: z.ZodOptional<z.ZodOptional<z.ZodBoolean>>;
    rrSegmentQueryValue: z.ZodOptional<z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodUnion<[z.ZodObject<{
        id: z.ZodOptional<z.ZodString>;
        type: z.ZodLiteral<"group">;
        children1: z.ZodOptional<z.ZodEffects<z.ZodRecord<z.ZodString, z.ZodObject<{
            type: z.ZodOptional<z.ZodString>;
            properties: z.ZodOptional<z.ZodObject<{
                field: z.ZodOptional<z.ZodAny>;
                operator: z.ZodOptional<z.ZodAny>;
                value: z.ZodOptional<z.ZodAny>;
                valueSrc: z.ZodOptional<z.ZodAny>;
                valueError: z.ZodOptional<z.ZodArray<z.ZodUnion<[z.ZodString, z.ZodNull]>, "many">>;
                valueType: z.ZodOptional<z.ZodAny>;
            }, "strip", z.ZodTypeAny, {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            }, {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            }>>;
        }, "strip", z.ZodTypeAny, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }>>, Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }>, Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }>>>;
        properties: z.ZodAny;
    }, "strip", z.ZodTypeAny, {
        type: "group";
        id?: string | undefined;
        children1?: Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }> | undefined;
        properties?: any;
    }, {
        type: "group";
        id?: string | undefined;
        children1?: Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }> | undefined;
        properties?: any;
    }>, z.ZodObject<{
        id: z.ZodOptional<z.ZodString>;
        type: z.ZodLiteral<"switch_group">;
        children1: z.ZodOptional<z.ZodEffects<z.ZodRecord<z.ZodString, z.ZodObject<{
            type: z.ZodOptional<z.ZodString>;
            properties: z.ZodOptional<z.ZodObject<{
                field: z.ZodOptional<z.ZodAny>;
                operator: z.ZodOptional<z.ZodAny>;
                value: z.ZodOptional<z.ZodAny>;
                valueSrc: z.ZodOptional<z.ZodAny>;
                valueError: z.ZodOptional<z.ZodArray<z.ZodUnion<[z.ZodString, z.ZodNull]>, "many">>;
                valueType: z.ZodOptional<z.ZodAny>;
            }, "strip", z.ZodTypeAny, {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            }, {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            }>>;
        }, "strip", z.ZodTypeAny, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }>>, Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }>, Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }>>>;
        properties: z.ZodAny;
    }, "strip", z.ZodTypeAny, {
        type: "switch_group";
        id?: string | undefined;
        children1?: Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }> | undefined;
        properties?: any;
    }, {
        type: "switch_group";
        id?: string | undefined;
        children1?: Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }> | undefined;
        properties?: any;
    }>]>>>>>;
    useEventTypeDestinationCalendarEmail: z.ZodOptional<z.ZodBoolean>;
    isRRWeightsEnabled: z.ZodOptional<z.ZodBoolean>;
    maxLeadThreshold: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodNumber>>>;
    includeNoShowInRRCalculation: z.ZodOptional<z.ZodBoolean>;
    allowReschedulingPastBookings: z.ZodOptional<z.ZodBoolean>;
    hideOrganizerEmail: z.ZodOptional<z.ZodBoolean>;
    maxActiveBookingsPerBooker: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodNumber>>>;
    maxActiveBookingPerBookerOfferReschedule: z.ZodOptional<z.ZodBoolean>;
    customReplyToEmail: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodString>>>;
    eventTypeColor: z.ZodOptional<z.ZodNullable<z.ZodObject<{
        lightEventTypeColor: z.ZodString;
        darkEventTypeColor: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        lightEventTypeColor: string;
        darkEventTypeColor: string;
    }, {
        lightEventTypeColor: string;
        darkEventTypeColor: string;
    }>>>;
    rescheduleWithSameRoundRobinHost: z.ZodOptional<z.ZodBoolean>;
    secondaryEmailId: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodNumber>>>;
    useBookerTimezone: z.ZodOptional<z.ZodBoolean>;
    restrictionScheduleId: z.ZodOptional<z.ZodOptional<z.ZodNullable<z.ZodNumber>>>;
    bookingRequiresAuthentication: z.ZodOptional<z.ZodBoolean>;
    instantMeetingSchedule: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
    hostGroups: z.ZodOptional<z.ZodOptional<z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        name: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        name: string;
        id: string;
    }, {
        name: string;
        id: string;
    }>, "many">>>;
    calAiPhoneScript: z.ZodOptional<z.ZodString>;
    multiplePrivateLinks: z.ZodOptional<z.ZodArray<z.ZodUnion<[z.ZodString, z.ZodObject<{
        link: z.ZodString;
        expiresAt: z.ZodOptional<z.ZodNullable<z.ZodDate>>;
        maxUsageCount: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
        usageCount: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
    }, "strict", z.ZodTypeAny, {
        link: string;
        expiresAt?: Date | null | undefined;
        maxUsageCount?: number | null | undefined;
        usageCount?: number | null | undefined;
    }, {
        link: string;
        expiresAt?: Date | null | undefined;
        maxUsageCount?: number | null | undefined;
        usageCount?: number | null | undefined;
    }>]>, "many">>;
    aiPhoneCallConfig: z.ZodEffects<z.ZodOptional<z.ZodObject<{
        generalPrompt: z.ZodString;
        enabled: z.ZodBoolean;
        beginMessage: z.ZodNullable<z.ZodString>;
        yourPhoneNumber: z.ZodString;
        numberToCall: z.ZodString;
        guestName: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        guestEmail: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        guestCompany: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        templateType: z.ZodEnum<["CHECK_IN_APPOINTMENT", "CUSTOM_TEMPLATE"]>;
    }, "strip", z.ZodTypeAny, {
        enabled: boolean;
        templateType: "CHECK_IN_APPOINTMENT" | "CUSTOM_TEMPLATE";
        generalPrompt: string;
        yourPhoneNumber: string;
        numberToCall: string;
        beginMessage: string | null;
        guestName?: string | null | undefined;
        guestEmail?: string | null | undefined;
        guestCompany?: string | null | undefined;
    }, {
        enabled: boolean;
        templateType: "CHECK_IN_APPOINTMENT" | "CUSTOM_TEMPLATE";
        generalPrompt: string;
        yourPhoneNumber: string;
        numberToCall: string;
        beginMessage: string | null;
        guestName?: string | null | undefined;
        guestEmail?: string | null | undefined;
        guestCompany?: string | null | undefined;
    }>>, {
        enabled: boolean;
        templateType: "CHECK_IN_APPOINTMENT" | "CUSTOM_TEMPLATE";
        generalPrompt: string;
        yourPhoneNumber: string;
        numberToCall: string;
        beginMessage: string | null;
        guestName?: string | null | undefined;
        guestEmail?: string | null | undefined;
        guestCompany?: string | null | undefined;
    } | undefined, {
        enabled: boolean;
        templateType: "CHECK_IN_APPOINTMENT" | "CUSTOM_TEMPLATE";
        generalPrompt: string;
        yourPhoneNumber: string;
        numberToCall: string;
        beginMessage: string | null;
        guestName?: string | null | undefined;
        guestEmail?: string | null | undefined;
        guestCompany?: string | null | undefined;
    } | undefined>;
}, "strict", z.ZodTypeAny, {
    id: number;
    users?: number[] | undefined;
    children?: {
        hidden: boolean;
        owner: {
            name: string;
            id: number;
            email: string;
            eventTypeSlugs: string[];
        };
    }[] | undefined;
    length?: number | undefined;
    title?: string | undefined;
    metadata?: {
        config?: {
            useHostSchedulesForTeamEvent?: boolean | undefined;
        } | undefined;
        smartContractAddress?: string | undefined;
        blockchainId?: number | undefined;
        multipleDuration?: number[] | undefined;
        giphyThankYouPage?: string | undefined;
        additionalNotesRequired?: boolean | undefined;
        disableSuccessPage?: boolean | undefined;
        disableStandardEmails?: {
            all?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
            confirmation?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
        } | undefined;
        managedEventConfig?: {
            unlockedFields?: {
                users?: true | undefined;
                children?: true | undefined;
                length?: true | undefined;
                title?: true | undefined;
                metadata?: true | undefined;
                description?: true | undefined;
                userId?: true | undefined;
                calVideoSettings?: true | undefined;
                destinationCalendar?: true | undefined;
                profile?: true | undefined;
                team?: true | undefined;
                schedule?: true | undefined;
                availability?: true | undefined;
                hashedLink?: true | undefined;
                secondaryEmail?: true | undefined;
                customInputs?: true | undefined;
                timeZone?: true | undefined;
                bookings?: true | undefined;
                selectedCalendars?: true | undefined;
                webhooks?: true | undefined;
                workflows?: true | undefined;
                hosts?: true | undefined;
                slug?: true | undefined;
                parentId?: true | undefined;
                bookingLimits?: true | undefined;
                parent?: true | undefined;
                teamId?: true | undefined;
                hidden?: true | undefined;
                _count?: true | undefined;
                interfaceLanguage?: true | undefined;
                position?: true | undefined;
                locations?: true | undefined;
                offsetStart?: true | undefined;
                profileId?: true | undefined;
                useEventLevelSelectedCalendars?: true | undefined;
                eventName?: true | undefined;
                bookingFields?: true | undefined;
                periodType?: true | undefined;
                periodStartDate?: true | undefined;
                periodEndDate?: true | undefined;
                periodDays?: true | undefined;
                periodCountCalendarDays?: true | undefined;
                lockTimeZoneToggleOnBookingPage?: true | undefined;
                lockedTimeZone?: true | undefined;
                requiresConfirmation?: true | undefined;
                requiresConfirmationWillBlockSlot?: true | undefined;
                requiresConfirmationForFreeEmail?: true | undefined;
                requiresBookerEmailVerification?: true | undefined;
                canSendCalVideoTranscriptionEmails?: true | undefined;
                autoTranslateDescriptionEnabled?: true | undefined;
                recurringEvent?: true | undefined;
                disableGuests?: true | undefined;
                hideCalendarNotes?: true | undefined;
                hideCalendarEventDetails?: true | undefined;
                minimumBookingNotice?: true | undefined;
                beforeEventBuffer?: true | undefined;
                afterEventBuffer?: true | undefined;
                seatsPerTimeSlot?: true | undefined;
                onlyShowFirstAvailableSlot?: true | undefined;
                disableCancelling?: true | undefined;
                disableRescheduling?: true | undefined;
                seatsShowAttendees?: true | undefined;
                seatsShowAvailabilityCount?: true | undefined;
                schedulingType?: true | undefined;
                scheduleId?: true | undefined;
                allowReschedulingCancelledBookings?: true | undefined;
                price?: true | undefined;
                currency?: true | undefined;
                slotInterval?: true | undefined;
                successRedirectUrl?: true | undefined;
                forwardParamsSuccessRedirect?: true | undefined;
                durationLimits?: true | undefined;
                isInstantEvent?: true | undefined;
                instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                instantMeetingScheduleId?: true | undefined;
                instantMeetingParameters?: true | undefined;
                assignAllTeamMembers?: true | undefined;
                assignRRMembersUsingSegment?: true | undefined;
                rrSegmentQueryValue?: true | undefined;
                useEventTypeDestinationCalendarEmail?: true | undefined;
                isRRWeightsEnabled?: true | undefined;
                maxLeadThreshold?: true | undefined;
                includeNoShowInRRCalculation?: true | undefined;
                allowReschedulingPastBookings?: true | undefined;
                hideOrganizerEmail?: true | undefined;
                maxActiveBookingsPerBooker?: true | undefined;
                maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                customReplyToEmail?: true | undefined;
                eventTypeColor?: true | undefined;
                rescheduleWithSameRoundRobinHost?: true | undefined;
                secondaryEmailId?: true | undefined;
                useBookerTimezone?: true | undefined;
                restrictionScheduleId?: true | undefined;
                bookingRequiresAuthentication?: true | undefined;
                owner?: true | undefined;
                instantMeetingSchedule?: true | undefined;
                aiPhoneCallConfig?: true | undefined;
                fieldTranslations?: true | undefined;
                restrictionSchedule?: true | undefined;
                hostGroups?: true | undefined;
            } | undefined;
        } | undefined;
        requiresConfirmationThreshold?: {
            time: number;
            unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
        } | undefined;
        bookerLayouts?: {
            enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
            defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
        } | null | undefined;
        apps?: unknown;
    } | null | undefined;
    description?: string | null | undefined;
    userId?: number | null | undefined;
    calVideoSettings?: {
        disableRecordingForGuests?: boolean | null | undefined;
        disableRecordingForOrganizer?: boolean | null | undefined;
        enableAutomaticTranscription?: boolean | null | undefined;
        enableAutomaticRecordingForOrganizer?: boolean | null | undefined;
        disableTranscriptionForGuests?: boolean | null | undefined;
        disableTranscriptionForOrganizer?: boolean | null | undefined;
        redirectUrlOnExit?: string | null | undefined;
    } | null | undefined;
    destinationCalendar?: {
        integration: string;
        externalId: string;
    } | null | undefined;
    schedule?: number | null | undefined;
    customInputs?: {
        id: number;
        type: "TEXT" | "TEXTLONG" | "NUMBER" | "BOOL" | "RADIO" | "PHONE";
        label: string;
        required: boolean;
        eventTypeId: number;
        placeholder: string;
        options?: {
            type: string;
            label: string;
        }[] | null | undefined;
        hasToBeCreated?: boolean | undefined;
    }[] | undefined;
    timeZone?: string | null | undefined;
    hosts?: {
        userId: number;
        profileId?: number | null | undefined;
        isFixed?: boolean | undefined;
        priority?: number | null | undefined;
        weight?: number | null | undefined;
        scheduleId?: number | null | undefined;
        groupId?: string | null | undefined;
    }[] | undefined;
    slug?: string | undefined;
    parentId?: number | null | undefined;
    bookingLimits?: Partial<Record<"PER_DAY" | "PER_MONTH" | "PER_WEEK" | "PER_YEAR", number | undefined>> | null | undefined;
    teamId?: number | null | undefined;
    hidden?: boolean | undefined;
    interfaceLanguage?: string | null | undefined;
    position?: number | undefined;
    locations?: {
        type: string;
        address?: string | undefined;
        link?: string | undefined;
        displayLocationPublicly?: boolean | undefined;
        hostPhoneNumber?: string | undefined;
        credentialId?: number | undefined;
        teamName?: string | undefined;
        customLabel?: string | undefined;
    }[] | undefined;
    offsetStart?: number | undefined;
    profileId?: number | null | undefined;
    useEventLevelSelectedCalendars?: boolean | undefined;
    eventName?: string | null | undefined;
    bookingFields?: {
        name: string;
        type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
        label?: string | undefined;
        options?: {
            label: string;
            value: string;
        }[] | undefined;
        required?: boolean | undefined;
        placeholder?: string | undefined;
        maxLength?: number | undefined;
        defaultLabel?: string | undefined;
        defaultPlaceholder?: string | undefined;
        labelAsSafeHtml?: string | undefined;
        getOptionsAt?: string | undefined;
        optionsInputs?: Record<string, {
            type: "phone" | "address" | "text";
            required?: boolean | undefined;
            placeholder?: string | undefined;
        }> | undefined;
        minLength?: number | undefined;
        excludeEmails?: string | undefined;
        requireEmails?: string | undefined;
        variant?: string | undefined;
        variantsConfig?: {
            variants: Record<string, {
                fields: {
                    name: string;
                    type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
                    label?: string | undefined;
                    required?: boolean | undefined;
                    placeholder?: string | undefined;
                    maxLength?: number | undefined;
                    labelAsSafeHtml?: string | undefined;
                    minLength?: number | undefined;
                    excludeEmails?: string | undefined;
                    requireEmails?: string | undefined;
                }[];
            }>;
        } | undefined;
        views?: {
            id: string;
            label: string;
            description?: string | undefined;
        }[] | undefined;
        hideWhenJustOneOption?: boolean | undefined;
        hidden?: boolean | undefined;
        editable?: "user" | "system" | "system-but-optional" | "system-but-hidden" | "user-readonly" | undefined;
        sources?: {
            id: string;
            type: string;
            label: string;
            editUrl?: string | undefined;
            fieldRequired?: boolean | undefined;
        }[] | undefined;
        disableOnPrefill?: boolean | undefined;
    }[] | undefined;
    periodType?: "UNLIMITED" | "ROLLING" | "ROLLING_WINDOW" | "RANGE" | undefined;
    periodStartDate?: Date | null | undefined;
    periodEndDate?: Date | null | undefined;
    periodDays?: number | null | undefined;
    periodCountCalendarDays?: boolean | null | undefined;
    lockTimeZoneToggleOnBookingPage?: boolean | undefined;
    lockedTimeZone?: string | null | undefined;
    requiresConfirmation?: boolean | undefined;
    requiresConfirmationWillBlockSlot?: boolean | undefined;
    requiresConfirmationForFreeEmail?: boolean | undefined;
    requiresBookerEmailVerification?: boolean | undefined;
    canSendCalVideoTranscriptionEmails?: boolean | undefined;
    autoTranslateDescriptionEnabled?: boolean | undefined;
    recurringEvent?: {
        count: number;
        interval: number;
        freq: import("@calcom/prisma/zod-utils").Frequency;
        dtstart?: Date | undefined;
        until?: Date | undefined;
        tzid?: string | undefined;
    } | null | undefined;
    disableGuests?: boolean | undefined;
    hideCalendarNotes?: boolean | undefined;
    hideCalendarEventDetails?: boolean | undefined;
    minimumBookingNotice?: number | undefined;
    beforeEventBuffer?: number | undefined;
    afterEventBuffer?: number | undefined;
    seatsPerTimeSlot?: number | null | undefined;
    onlyShowFirstAvailableSlot?: boolean | undefined;
    disableCancelling?: boolean | null | undefined;
    disableRescheduling?: boolean | null | undefined;
    seatsShowAttendees?: boolean | null | undefined;
    seatsShowAvailabilityCount?: boolean | null | undefined;
    schedulingType?: "ROUND_ROBIN" | "COLLECTIVE" | "MANAGED" | null | undefined;
    scheduleId?: number | null | undefined;
    allowReschedulingCancelledBookings?: boolean | null | undefined;
    price?: number | undefined;
    currency?: string | undefined;
    slotInterval?: number | null | undefined;
    successRedirectUrl?: string | null | undefined;
    forwardParamsSuccessRedirect?: boolean | null | undefined;
    durationLimits?: Partial<Record<"PER_DAY" | "PER_MONTH" | "PER_WEEK" | "PER_YEAR", number | undefined>> | null | undefined;
    isInstantEvent?: boolean | undefined;
    instantMeetingExpiryTimeOffsetInSeconds?: number | undefined;
    instantMeetingScheduleId?: number | null | undefined;
    instantMeetingParameters?: string[] | undefined;
    assignAllTeamMembers?: boolean | undefined;
    assignRRMembersUsingSegment?: boolean | undefined;
    rrSegmentQueryValue?: {
        type: "group";
        id?: string | undefined;
        children1?: Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }> | undefined;
        properties?: any;
    } | {
        type: "switch_group";
        id?: string | undefined;
        children1?: Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }> | undefined;
        properties?: any;
    } | null | undefined;
    useEventTypeDestinationCalendarEmail?: boolean | undefined;
    isRRWeightsEnabled?: boolean | undefined;
    maxLeadThreshold?: number | null | undefined;
    includeNoShowInRRCalculation?: boolean | undefined;
    allowReschedulingPastBookings?: boolean | undefined;
    hideOrganizerEmail?: boolean | undefined;
    maxActiveBookingsPerBooker?: number | null | undefined;
    maxActiveBookingPerBookerOfferReschedule?: boolean | undefined;
    customReplyToEmail?: string | null | undefined;
    eventTypeColor?: {
        lightEventTypeColor: string;
        darkEventTypeColor: string;
    } | null | undefined;
    rescheduleWithSameRoundRobinHost?: boolean | undefined;
    secondaryEmailId?: number | null | undefined;
    useBookerTimezone?: boolean | undefined;
    restrictionScheduleId?: number | null | undefined;
    bookingRequiresAuthentication?: boolean | undefined;
    instantMeetingSchedule?: number | null | undefined;
    hostGroups?: {
        name: string;
        id: string;
    }[] | undefined;
    calAiPhoneScript?: string | undefined;
    multiplePrivateLinks?: (string | {
        link: string;
        expiresAt?: Date | null | undefined;
        maxUsageCount?: number | null | undefined;
        usageCount?: number | null | undefined;
    })[] | undefined;
    aiPhoneCallConfig?: {
        enabled: boolean;
        templateType: "CHECK_IN_APPOINTMENT" | "CUSTOM_TEMPLATE";
        generalPrompt: string;
        yourPhoneNumber: string;
        numberToCall: string;
        beginMessage: string | null;
        guestName?: string | null | undefined;
        guestEmail?: string | null | undefined;
        guestCompany?: string | null | undefined;
    } | undefined;
}, {
    id: number;
    users?: (string | number)[] | undefined;
    children?: {
        hidden: boolean;
        owner: {
            name: string;
            id: number;
            email: string;
            eventTypeSlugs: string[];
        };
    }[] | undefined;
    length?: number | undefined;
    title?: string | undefined;
    metadata?: {
        config?: {
            useHostSchedulesForTeamEvent?: boolean | undefined;
        } | undefined;
        smartContractAddress?: string | undefined;
        blockchainId?: number | undefined;
        multipleDuration?: number[] | undefined;
        giphyThankYouPage?: string | undefined;
        additionalNotesRequired?: boolean | undefined;
        disableSuccessPage?: boolean | undefined;
        disableStandardEmails?: {
            all?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
            confirmation?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
        } | undefined;
        managedEventConfig?: {
            unlockedFields?: {
                users?: true | undefined;
                children?: true | undefined;
                length?: true | undefined;
                title?: true | undefined;
                metadata?: true | undefined;
                description?: true | undefined;
                userId?: true | undefined;
                calVideoSettings?: true | undefined;
                destinationCalendar?: true | undefined;
                profile?: true | undefined;
                team?: true | undefined;
                schedule?: true | undefined;
                availability?: true | undefined;
                hashedLink?: true | undefined;
                secondaryEmail?: true | undefined;
                customInputs?: true | undefined;
                timeZone?: true | undefined;
                bookings?: true | undefined;
                selectedCalendars?: true | undefined;
                webhooks?: true | undefined;
                workflows?: true | undefined;
                hosts?: true | undefined;
                slug?: true | undefined;
                parentId?: true | undefined;
                bookingLimits?: true | undefined;
                parent?: true | undefined;
                teamId?: true | undefined;
                hidden?: true | undefined;
                _count?: true | undefined;
                interfaceLanguage?: true | undefined;
                position?: true | undefined;
                locations?: true | undefined;
                offsetStart?: true | undefined;
                profileId?: true | undefined;
                useEventLevelSelectedCalendars?: true | undefined;
                eventName?: true | undefined;
                bookingFields?: true | undefined;
                periodType?: true | undefined;
                periodStartDate?: true | undefined;
                periodEndDate?: true | undefined;
                periodDays?: true | undefined;
                periodCountCalendarDays?: true | undefined;
                lockTimeZoneToggleOnBookingPage?: true | undefined;
                lockedTimeZone?: true | undefined;
                requiresConfirmation?: true | undefined;
                requiresConfirmationWillBlockSlot?: true | undefined;
                requiresConfirmationForFreeEmail?: true | undefined;
                requiresBookerEmailVerification?: true | undefined;
                canSendCalVideoTranscriptionEmails?: true | undefined;
                autoTranslateDescriptionEnabled?: true | undefined;
                recurringEvent?: true | undefined;
                disableGuests?: true | undefined;
                hideCalendarNotes?: true | undefined;
                hideCalendarEventDetails?: true | undefined;
                minimumBookingNotice?: true | undefined;
                beforeEventBuffer?: true | undefined;
                afterEventBuffer?: true | undefined;
                seatsPerTimeSlot?: true | undefined;
                onlyShowFirstAvailableSlot?: true | undefined;
                disableCancelling?: true | undefined;
                disableRescheduling?: true | undefined;
                seatsShowAttendees?: true | undefined;
                seatsShowAvailabilityCount?: true | undefined;
                schedulingType?: true | undefined;
                scheduleId?: true | undefined;
                allowReschedulingCancelledBookings?: true | undefined;
                price?: true | undefined;
                currency?: true | undefined;
                slotInterval?: true | undefined;
                successRedirectUrl?: true | undefined;
                forwardParamsSuccessRedirect?: true | undefined;
                durationLimits?: true | undefined;
                isInstantEvent?: true | undefined;
                instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                instantMeetingScheduleId?: true | undefined;
                instantMeetingParameters?: true | undefined;
                assignAllTeamMembers?: true | undefined;
                assignRRMembersUsingSegment?: true | undefined;
                rrSegmentQueryValue?: true | undefined;
                useEventTypeDestinationCalendarEmail?: true | undefined;
                isRRWeightsEnabled?: true | undefined;
                maxLeadThreshold?: true | undefined;
                includeNoShowInRRCalculation?: true | undefined;
                allowReschedulingPastBookings?: true | undefined;
                hideOrganizerEmail?: true | undefined;
                maxActiveBookingsPerBooker?: true | undefined;
                maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                customReplyToEmail?: true | undefined;
                eventTypeColor?: true | undefined;
                rescheduleWithSameRoundRobinHost?: true | undefined;
                secondaryEmailId?: true | undefined;
                useBookerTimezone?: true | undefined;
                restrictionScheduleId?: true | undefined;
                bookingRequiresAuthentication?: true | undefined;
                owner?: true | undefined;
                instantMeetingSchedule?: true | undefined;
                aiPhoneCallConfig?: true | undefined;
                fieldTranslations?: true | undefined;
                restrictionSchedule?: true | undefined;
                hostGroups?: true | undefined;
            } | undefined;
        } | undefined;
        requiresConfirmationThreshold?: {
            time: number;
            unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
        } | undefined;
        bookerLayouts?: {
            enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
            defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
        } | null | undefined;
        apps?: unknown;
    } | null | undefined;
    description?: string | null | undefined;
    userId?: number | null | undefined;
    calVideoSettings?: {
        disableRecordingForGuests?: boolean | null | undefined;
        disableRecordingForOrganizer?: boolean | null | undefined;
        enableAutomaticTranscription?: boolean | null | undefined;
        enableAutomaticRecordingForOrganizer?: boolean | null | undefined;
        disableTranscriptionForGuests?: boolean | null | undefined;
        disableTranscriptionForOrganizer?: boolean | null | undefined;
        redirectUrlOnExit?: string | null | undefined;
    } | null | undefined;
    destinationCalendar?: {
        integration: string;
        externalId: string;
    } | null | undefined;
    schedule?: number | null | undefined;
    customInputs?: {
        id: number;
        type: "TEXT" | "TEXTLONG" | "NUMBER" | "BOOL" | "RADIO" | "PHONE";
        label: string;
        required: boolean;
        eventTypeId: number;
        placeholder: string;
        options?: {
            type: string;
            label: string;
        }[] | null | undefined;
        hasToBeCreated?: boolean | undefined;
    }[] | undefined;
    timeZone?: string | null | undefined;
    hosts?: {
        userId: number;
        profileId?: number | null | undefined;
        isFixed?: boolean | undefined;
        priority?: number | null | undefined;
        weight?: number | null | undefined;
        scheduleId?: number | null | undefined;
        groupId?: string | null | undefined;
    }[] | undefined;
    slug?: string | undefined;
    parentId?: number | null | undefined;
    bookingLimits?: Partial<Record<"PER_DAY" | "PER_MONTH" | "PER_WEEK" | "PER_YEAR", number | undefined>> | null | undefined;
    teamId?: number | null | undefined;
    hidden?: boolean | undefined;
    interfaceLanguage?: string | null | undefined;
    position?: number | undefined;
    locations?: {
        type: string;
        address?: string | undefined;
        link?: string | undefined;
        displayLocationPublicly?: boolean | undefined;
        hostPhoneNumber?: string | undefined;
        credentialId?: number | undefined;
        teamName?: string | undefined;
        customLabel?: string | undefined;
    }[] | undefined;
    offsetStart?: number | undefined;
    profileId?: number | null | undefined;
    useEventLevelSelectedCalendars?: boolean | undefined;
    eventName?: string | null | undefined;
    bookingFields?: {
        name: string;
        type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
        label?: string | undefined;
        options?: {
            label: string;
            value: string;
        }[] | undefined;
        required?: boolean | undefined;
        placeholder?: string | undefined;
        maxLength?: number | undefined;
        defaultLabel?: string | undefined;
        defaultPlaceholder?: string | undefined;
        labelAsSafeHtml?: string | undefined;
        getOptionsAt?: string | undefined;
        optionsInputs?: Record<string, {
            type: "phone" | "address" | "text";
            required?: boolean | undefined;
            placeholder?: string | undefined;
        }> | undefined;
        minLength?: number | undefined;
        excludeEmails?: string | undefined;
        requireEmails?: string | undefined;
        variant?: string | undefined;
        variantsConfig?: {
            variants: Record<string, {
                fields: {
                    name: string;
                    type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
                    label?: string | undefined;
                    required?: boolean | undefined;
                    placeholder?: string | undefined;
                    maxLength?: number | undefined;
                    labelAsSafeHtml?: string | undefined;
                    minLength?: number | undefined;
                    excludeEmails?: string | undefined;
                    requireEmails?: string | undefined;
                }[];
            }>;
        } | undefined;
        views?: {
            id: string;
            label: string;
            description?: string | undefined;
        }[] | undefined;
        hideWhenJustOneOption?: boolean | undefined;
        hidden?: boolean | undefined;
        editable?: "user" | "system" | "system-but-optional" | "system-but-hidden" | "user-readonly" | undefined;
        sources?: {
            id: string;
            type: string;
            label: string;
            editUrl?: string | undefined;
            fieldRequired?: boolean | undefined;
        }[] | undefined;
        disableOnPrefill?: boolean | undefined;
    }[] | undefined;
    periodType?: "UNLIMITED" | "ROLLING" | "ROLLING_WINDOW" | "RANGE" | undefined;
    periodStartDate?: Date | null | undefined;
    periodEndDate?: Date | null | undefined;
    periodDays?: number | null | undefined;
    periodCountCalendarDays?: boolean | null | undefined;
    lockTimeZoneToggleOnBookingPage?: boolean | undefined;
    lockedTimeZone?: string | null | undefined;
    requiresConfirmation?: boolean | undefined;
    requiresConfirmationWillBlockSlot?: boolean | undefined;
    requiresConfirmationForFreeEmail?: boolean | undefined;
    requiresBookerEmailVerification?: boolean | undefined;
    canSendCalVideoTranscriptionEmails?: boolean | undefined;
    autoTranslateDescriptionEnabled?: boolean | undefined;
    recurringEvent?: {
        count: number;
        interval: number;
        freq: import("@calcom/prisma/zod-utils").Frequency;
        dtstart?: Date | undefined;
        until?: Date | undefined;
        tzid?: string | undefined;
    } | null | undefined;
    disableGuests?: boolean | undefined;
    hideCalendarNotes?: boolean | undefined;
    hideCalendarEventDetails?: boolean | undefined;
    minimumBookingNotice?: number | undefined;
    beforeEventBuffer?: number | undefined;
    afterEventBuffer?: number | undefined;
    seatsPerTimeSlot?: number | null | undefined;
    onlyShowFirstAvailableSlot?: boolean | undefined;
    disableCancelling?: boolean | null | undefined;
    disableRescheduling?: boolean | null | undefined;
    seatsShowAttendees?: boolean | null | undefined;
    seatsShowAvailabilityCount?: boolean | null | undefined;
    schedulingType?: "ROUND_ROBIN" | "COLLECTIVE" | "MANAGED" | null | undefined;
    scheduleId?: number | null | undefined;
    allowReschedulingCancelledBookings?: boolean | null | undefined;
    price?: number | undefined;
    currency?: string | undefined;
    slotInterval?: number | null | undefined;
    successRedirectUrl?: string | null | undefined;
    forwardParamsSuccessRedirect?: boolean | null | undefined;
    durationLimits?: Partial<Record<"PER_DAY" | "PER_MONTH" | "PER_WEEK" | "PER_YEAR", number | undefined>> | null | undefined;
    isInstantEvent?: boolean | undefined;
    instantMeetingExpiryTimeOffsetInSeconds?: number | undefined;
    instantMeetingScheduleId?: number | null | undefined;
    instantMeetingParameters?: string[] | undefined;
    assignAllTeamMembers?: boolean | undefined;
    assignRRMembersUsingSegment?: boolean | undefined;
    rrSegmentQueryValue?: {
        type: "group";
        id?: string | undefined;
        children1?: Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }> | undefined;
        properties?: any;
    } | {
        type: "switch_group";
        id?: string | undefined;
        children1?: Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }> | undefined;
        properties?: any;
    } | null | undefined;
    useEventTypeDestinationCalendarEmail?: boolean | undefined;
    isRRWeightsEnabled?: boolean | undefined;
    maxLeadThreshold?: number | null | undefined;
    includeNoShowInRRCalculation?: boolean | undefined;
    allowReschedulingPastBookings?: boolean | undefined;
    hideOrganizerEmail?: boolean | undefined;
    maxActiveBookingsPerBooker?: number | null | undefined;
    maxActiveBookingPerBookerOfferReschedule?: boolean | undefined;
    customReplyToEmail?: string | null | undefined;
    eventTypeColor?: {
        lightEventTypeColor: string;
        darkEventTypeColor: string;
    } | null | undefined;
    rescheduleWithSameRoundRobinHost?: boolean | undefined;
    secondaryEmailId?: number | null | undefined;
    useBookerTimezone?: boolean | undefined;
    restrictionScheduleId?: number | null | undefined;
    bookingRequiresAuthentication?: boolean | undefined;
    instantMeetingSchedule?: number | null | undefined;
    hostGroups?: {
        name: string;
        id: string;
    }[] | undefined;
    calAiPhoneScript?: string | undefined;
    multiplePrivateLinks?: (string | {
        link: string;
        expiresAt?: Date | null | undefined;
        maxUsageCount?: number | null | undefined;
        usageCount?: number | null | undefined;
    })[] | undefined;
    aiPhoneCallConfig?: {
        enabled: boolean;
        templateType: "CHECK_IN_APPOINTMENT" | "CUSTOM_TEMPLATE";
        generalPrompt: string;
        yourPhoneNumber: string;
        numberToCall: string;
        beginMessage: string | null;
        guestName?: string | null | undefined;
        guestEmail?: string | null | undefined;
        guestCompany?: string | null | undefined;
    } | undefined;
}>;
export type TUpdateInputSchema = z.infer<typeof BaseEventTypeUpdateInput>;
export {};
