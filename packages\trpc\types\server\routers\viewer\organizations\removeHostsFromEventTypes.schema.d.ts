import { z } from "zod";
export declare const ZRemoveHostsFromEventTypes: z.ZodObject<{
    userIds: z.<PERSON><z.ZodN<PERSON><PERSON>, "many">;
    eventTypeIds: z.<PERSON><z.ZodNumber, "many">;
}, "strip", z.<PERSON>ny, {
    userIds: number[];
    eventTypeIds: number[];
}, {
    userIds: number[];
    eventTypeIds: number[];
}>;
export type TRemoveHostsFromEventTypes = z.infer<typeof ZRemoveHostsFromEventTypes>;
