export declare const webhookRouter: import("@trpc/server/unstable-core-do-not-import").CreateRouterInner<import("@trpc/server/unstable-core-do-not-import").RootConfig<{
    ctx: import("../../../createContext").InnerContext;
    meta: object;
    errorShape: {
        message: string;
        code: number;
        data: {
            code: string;
            httpStatus: number;
            path?: string;
            [key: string]: unknown;
        };
    };
    transformer: {
        stringify: (object: any) => string;
        parse: <T = unknown>(string: string) => T;
        serialize: (object: any) => import("superjson/dist/types").SuperJSONResult;
        deserialize: <T_1 = unknown>(payload: import("superjson/dist/types").SuperJSONResult) => T_1;
        registerClass: (v: import("superjson/dist/types").Class, options?: string | import("superjson/dist/class-registry").RegisterOptions | undefined) => void;
        registerSymbol: (v: Symbol, identifier?: string | undefined) => void;
        registerCustom: <I, O extends import("superjson/dist/types").JSONValue>(transformer: Omit<import("superjson/dist/custom-transformer-registry").CustomTransfomer<I, O>, "name">, name: string) => void;
        allowErrorProps: (...props: string[]) => void;
    };
}>, {
    list: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            id?: string | undefined;
            eventTypeId?: number | undefined;
            teamId?: number | undefined;
            appId?: string | undefined;
            eventTriggers?: ("BOOKING_CREATED" | "BOOKING_PAYMENT_INITIATED" | "BOOKING_PAID" | "BOOKING_RESCHEDULED" | "BOOKING_REQUESTED" | "BOOKING_CANCELLED" | "BOOKING_REJECTED" | "BOOKING_NO_SHOW_UPDATED" | "FORM_SUBMITTED" | "MEETING_ENDED" | "MEETING_STARTED" | "RECORDING_READY" | "INSTANT_MEETING" | "RECORDING_TRANSCRIPTION_GENERATED" | "OOO_CREATED" | "AFTER_HOSTS_CAL_VIDEO_NO_SHOW" | "AFTER_GUESTS_CAL_VIDEO_NO_SHOW" | "FORM_SUBMITTED_NO_EVENT")[] | undefined;
        } | undefined;
        output: {
            id: string;
            time: number | null;
            userId: number | null;
            eventTypeId: number | null;
            createdAt: Date;
            teamId: number | null;
            secret: string | null;
            appId: string | null;
            active: boolean;
            platform: boolean;
            subscriberUrl: string;
            payloadTemplate: string | null;
            eventTriggers: import(".prisma/client").$Enums.WebhookTriggerEvents[];
            timeUnit: import(".prisma/client").$Enums.TimeUnit | null;
            platformOAuthClientId: string | null;
        }[];
    }>;
    get: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            id?: string | undefined;
            eventTypeId?: number | undefined;
            teamId?: number | undefined;
            webhookId?: string | undefined;
        };
        output: {
            id: string;
            time: number | null;
            userId: number | null;
            teamId: number | null;
            secret: string | null;
            active: boolean;
            platform: boolean;
            subscriberUrl: string;
            payloadTemplate: string | null;
            eventTriggers: import(".prisma/client").$Enums.WebhookTriggerEvents[];
            timeUnit: import(".prisma/client").$Enums.TimeUnit | null;
        };
    }>;
    create: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            id?: string | undefined;
            eventTypeId?: number | undefined;
            teamId?: number | undefined;
            active: boolean;
            subscriberUrl: string;
            payloadTemplate: string | null;
            eventTriggers: ("BOOKING_CREATED" | "BOOKING_PAYMENT_INITIATED" | "BOOKING_PAID" | "BOOKING_RESCHEDULED" | "BOOKING_REQUESTED" | "BOOKING_CANCELLED" | "BOOKING_REJECTED" | "BOOKING_NO_SHOW_UPDATED" | "FORM_SUBMITTED" | "MEETING_ENDED" | "MEETING_STARTED" | "RECORDING_READY" | "INSTANT_MEETING" | "RECORDING_TRANSCRIPTION_GENERATED" | "OOO_CREATED" | "AFTER_HOSTS_CAL_VIDEO_NO_SHOW" | "AFTER_GUESTS_CAL_VIDEO_NO_SHOW" | "FORM_SUBMITTED_NO_EVENT")[];
            appId?: string | null | undefined;
            secret?: string | null | undefined;
            platform?: boolean | undefined;
            time?: number | null | undefined;
            timeUnit?: "DAY" | "HOUR" | "MINUTE" | null | undefined;
        };
        output: {
            id: string;
            userId: number | null;
            teamId: number | null;
            eventTypeId: number | null;
            platformOAuthClientId: string | null;
            subscriberUrl: string;
            payloadTemplate: string | null;
            createdAt: Date;
            active: boolean;
            eventTriggers: import(".prisma/client").$Enums.WebhookTriggerEvents[];
            appId: string | null;
            secret: string | null;
            platform: boolean;
            time: number | null;
            timeUnit: import(".prisma/client").$Enums.TimeUnit | null;
        };
    }>;
    edit: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            id: string;
            eventTypeId?: number | undefined;
            teamId?: number | undefined;
            payloadTemplate: string | null;
            subscriberUrl?: string | undefined;
            eventTriggers?: ("BOOKING_CREATED" | "BOOKING_PAYMENT_INITIATED" | "BOOKING_PAID" | "BOOKING_RESCHEDULED" | "BOOKING_REQUESTED" | "BOOKING_CANCELLED" | "BOOKING_REJECTED" | "BOOKING_NO_SHOW_UPDATED" | "FORM_SUBMITTED" | "MEETING_ENDED" | "MEETING_STARTED" | "RECORDING_READY" | "INSTANT_MEETING" | "RECORDING_TRANSCRIPTION_GENERATED" | "OOO_CREATED" | "AFTER_HOSTS_CAL_VIDEO_NO_SHOW" | "AFTER_GUESTS_CAL_VIDEO_NO_SHOW" | "FORM_SUBMITTED_NO_EVENT")[] | undefined;
            active?: boolean | undefined;
            appId?: string | null | undefined;
            secret?: string | null | undefined;
            time?: number | null | undefined;
            timeUnit?: "DAY" | "HOUR" | "MINUTE" | null | undefined;
        };
        output: {
            id: string;
            time: number | null;
            userId: number | null;
            eventTypeId: number | null;
            createdAt: Date;
            teamId: number | null;
            secret: string | null;
            appId: string | null;
            active: boolean;
            platform: boolean;
            subscriberUrl: string;
            payloadTemplate: string | null;
            eventTriggers: import(".prisma/client").$Enums.WebhookTriggerEvents[];
            timeUnit: import(".prisma/client").$Enums.TimeUnit | null;
            platformOAuthClientId: string | null;
        } | null;
    }>;
    delete: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            id: string;
            eventTypeId?: number | undefined;
            teamId?: number | undefined;
        };
        output: {
            id: string;
        };
    }>;
    testTrigger: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            id?: string | undefined;
            eventTypeId?: number | undefined;
            teamId?: number | undefined;
            type: string;
            url: string;
            secret?: string | undefined;
            payloadTemplate?: string | null | undefined;
        };
        output: {
            message?: string | undefined;
            ok: boolean;
            status: number;
        };
    }>;
    getByViewer: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            id?: string | undefined;
            eventTypeId?: number | undefined;
            teamId?: number | undefined;
        } | undefined;
        output: {
            webhookGroups: {
                teamId?: number | null;
                profile: {
                    slug: string | null;
                    name: string | null;
                    image?: string;
                };
                metadata?: {
                    readOnly: boolean;
                };
                webhooks: import(".prisma/client").Webhook[];
            }[];
            profiles: {
                readOnly?: boolean | undefined;
                slug: string | null;
                name: string | null;
                image?: string;
                teamId: number | null | undefined;
            }[];
        };
    }>;
}>;
