export declare const viewerOrganizationsRouter: import("@trpc/server/unstable-core-do-not-import").CreateRouterInner<import("@trpc/server/unstable-core-do-not-import").RootConfig<{
    ctx: import("../../../createContext").InnerContext;
    meta: object;
    errorShape: {
        message: string;
        code: number;
        data: {
            code: string;
            httpStatus: number;
            path?: string;
            [key: string]: unknown;
        };
    };
    transformer: {
        stringify: (object: any) => string;
        parse: <T = unknown>(string: string) => T;
        serialize: (object: any) => import("superjson/dist/types").SuperJSONResult;
        deserialize: <T_1 = unknown>(payload: import("superjson/dist/types").SuperJSONResult) => T_1;
        registerClass: (v: import("superjson/dist/types").Class, options?: string | import("superjson/dist/class-registry").RegisterOptions | undefined) => void;
        registerSymbol: (v: Symbol, identifier?: string | undefined) => void;
        registerCustom: <I, O extends import("superjson/dist/types").JSONValue>(transformer: Omit<import("superjson/dist/custom-transformer-registry").CustomTransfomer<I, O>, "name">, name: string) => void;
        allowErrorProps: (...props: string[]) => void;
    };
}>, {
    getOrganizationOnboarding: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: {
            name: string;
            id: string;
            error: string | null;
            createdAt: Date;
            updatedAt: Date;
            bio: string | null;
            organizationId: number | null;
            teams: import(".prisma/client").Prisma.JsonValue;
            slug: string;
            isPlatform: boolean;
            logo: string | null;
            createdById: number;
            orgOwnerEmail: string;
            billingPeriod: import(".prisma/client").$Enums.BillingPeriod;
            pricePerSeat: number;
            seats: number;
            isDomainConfigured: boolean;
            stripeCustomerId: string | null;
            stripeSubscriptionId: string | null;
            stripeSubscriptionItemId: string | null;
            invitedMembers: import(".prisma/client").Prisma.JsonValue;
            isComplete: boolean;
        } | null;
    }>;
    create: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            name: string;
            creationSource: "API_V1" | "API_V2" | "WEBAPP";
            slug: string;
            orgOwnerEmail: string;
            language?: string | undefined;
            seats?: number | undefined;
            pricePerSeat?: number | undefined;
            isPlatform?: boolean | undefined;
            billingPeriod?: import("./create.schema").BillingPeriod | undefined;
        };
        output: {
            userId: number;
            email: string;
            organizationId: number;
            upId: string;
        };
    }>;
    intentToCreateOrg: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            name: string;
            creationSource: "API_V1" | "API_V2" | "WEBAPP";
            slug: string;
            orgOwnerEmail: string;
            language?: string | undefined;
            seats?: number | null | undefined;
            pricePerSeat?: number | null | undefined;
            isPlatform?: boolean | undefined;
            billingPeriod?: import("./intentToCreateOrg.schema").BillingPeriod | undefined;
        };
        output: {
            userId: number;
            orgOwnerEmail: string;
            name: string;
            slug: string;
            seats: number | null | undefined;
            pricePerSeat: number | null | undefined;
            billingPeriod: import("./intentToCreateOrg.schema").BillingPeriod;
            isPlatform: boolean;
            organizationOnboardingId: string;
        };
    }>;
    createWithPaymentIntent: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            onboardingId: string;
            language?: string | undefined;
            logo?: string | null | undefined;
            bio?: string | null | undefined;
            invitedMembers?: {
                email: string;
                name?: string | undefined;
            }[] | undefined;
            teams?: {
                name: string;
                id: number;
                slug: string | null;
                isBeingMigrated: boolean;
            }[] | undefined;
        };
        output: {
            checkoutUrl: string | null;
        };
    }>;
    update: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            name?: string | undefined;
            orgId?: string | number | undefined;
            bio?: string | undefined;
            logoUrl?: string | null | undefined;
            calVideoLogo?: string | null | undefined;
            banner?: string | null | undefined;
            slug?: string | undefined;
            hideBranding?: boolean | undefined;
            hideBookATeamMember?: boolean | undefined;
            brandColor?: string | undefined;
            darkBrandColor?: string | undefined;
            theme?: string | null | undefined;
            timeZone?: string | undefined;
            weekStart?: string | undefined;
            timeFormat?: number | undefined;
            metadata?: {
                billingPeriod?: import("@calcom/prisma/zod-utils").BillingPeriod | undefined;
                requestedSlug?: string | null | undefined;
                defaultConferencingApp?: {
                    appSlug?: string | undefined;
                    appLink?: string | undefined;
                } | undefined;
                migratedToOrgFrom?: {
                    teamSlug?: string | null | undefined;
                    lastMigrationTime?: string | undefined;
                    reverted?: boolean | undefined;
                    lastRevertTime?: string | undefined;
                } | undefined;
                paymentId?: string | undefined;
                orgSeats?: number | null | undefined;
                orgPricePerSeat?: number | null | undefined;
                subscriptionId?: string | null | undefined;
                subscriptionItemId?: string | null | undefined;
            } | undefined;
            lockEventTypeCreation?: boolean | undefined;
            lockEventTypeCreationOptions?: "DELETE" | "HIDE" | undefined;
            adminGetsNoSlotsNotification?: boolean | undefined;
            allowSEOIndexing?: boolean | undefined;
            orgProfileRedirectsToVerifiedDomain?: boolean | undefined;
            disablePhoneOnlySMSNotifications?: boolean | undefined;
        };
        output: {
            update: boolean;
            userId: number;
            data: {
                name: string;
                id: number;
                metadata: import(".prisma/client").Prisma.JsonValue;
                createdAt: Date;
                bio: string | null;
                timeZone: string;
                weekStart: string;
                hideBranding: boolean;
                theme: string | null;
                timeFormat: number | null;
                brandColor: string | null;
                darkBrandColor: string | null;
                smsLockState: import(".prisma/client").$Enums.SMSLockState;
                smsLockReviewedByAdmin: boolean;
                slug: string | null;
                logoUrl: string | null;
                calVideoLogo: string | null;
                appLogo: string | null;
                appIconLogo: string | null;
                hideTeamProfileLink: boolean;
                isPrivate: boolean;
                hideBookATeamMember: boolean;
                rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
                rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
                bannerUrl: string | null;
                parentId: number | null;
                isOrganization: boolean;
                pendingPayment: boolean;
                isPlatform: boolean;
                createdByOAuthClientId: string | null;
                bookingLimits: import(".prisma/client").Prisma.JsonValue;
                includeManagedEventsInLimits: boolean;
            };
        };
    }>;
    verifyCode: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            code: string;
            email: string;
        };
        output: true;
    }>;
    createTeams: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            creationSource: "API_V1" | "API_V2" | "WEBAPP";
            orgId: number;
            teamNames: string[];
            moveTeams: {
                id: number;
                newSlug: string | null;
                shouldMove: boolean;
            }[];
        };
        output: {
            duplicatedSlugs: string[];
        };
    }>;
    listCurrent: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: {
            features: {
                delegationCredential: boolean;
            };
            metadata: {
                defaultConferencingApp?: {
                    appSlug?: string | undefined;
                    appLink?: string | undefined;
                } | undefined;
                requestedSlug?: string | null | undefined;
                paymentId?: string | undefined;
                subscriptionId?: string | null | undefined;
                subscriptionItemId?: string | null | undefined;
                orgSeats?: number | null | undefined;
                orgPricePerSeat?: number | null | undefined;
                migratedToOrgFrom?: {
                    teamSlug?: string | null | undefined;
                    lastMigrationTime?: string | undefined;
                    reverted?: boolean | undefined;
                    lastRevertTime?: string | undefined;
                } | undefined;
                billingPeriod?: import("@calcom/prisma/zod-utils").BillingPeriod | undefined;
            } | null;
            name: string;
            id: number;
            createdAt: Date;
            bio: string | null;
            timeZone: string;
            weekStart: string;
            hideBranding: boolean;
            theme: string | null;
            timeFormat: number | null;
            brandColor: string | null;
            darkBrandColor: string | null;
            smsLockState: import(".prisma/client").$Enums.SMSLockState;
            smsLockReviewedByAdmin: boolean;
            slug: string | null;
            logoUrl: string | null;
            calVideoLogo: string | null;
            appLogo: string | null;
            appIconLogo: string | null;
            hideTeamProfileLink: boolean;
            isPrivate: boolean;
            hideBookATeamMember: boolean;
            rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
            rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
            bannerUrl: string | null;
            parentId: number | null;
            isOrganization: boolean;
            pendingPayment: boolean;
            isPlatform: boolean;
            createdByOAuthClientId: string | null;
            bookingLimits: import(".prisma/client").Prisma.JsonValue;
            includeManagedEventsInLimits: boolean;
            canAdminImpersonate: boolean;
            organizationSettings: {
                lockEventTypeCreationForUsers: boolean | undefined;
                adminGetsNoSlotsNotification: boolean | undefined;
                allowSEOIndexing: boolean | undefined;
                orgProfileRedirectsToVerifiedDomain: boolean | undefined;
                orgAutoAcceptEmail: string | undefined;
                disablePhoneOnlySMSNotifications: boolean | undefined;
            };
            user: {
                role: import(".prisma/client").$Enums.MembershipRole;
                accepted: boolean;
            };
        };
    }>;
    checkIfOrgNeedsUpgrade: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: ({
            team: {
                name: string;
                id: number;
                metadata: import(".prisma/client").Prisma.JsonValue;
                createdAt: Date;
                bio: string | null;
                timeZone: string;
                weekStart: string;
                hideBranding: boolean;
                theme: string | null;
                timeFormat: number | null;
                brandColor: string | null;
                darkBrandColor: string | null;
                smsLockState: import(".prisma/client").$Enums.SMSLockState;
                smsLockReviewedByAdmin: boolean;
                slug: string | null;
                logoUrl: string | null;
                calVideoLogo: string | null;
                appLogo: string | null;
                appIconLogo: string | null;
                hideTeamProfileLink: boolean;
                isPrivate: boolean;
                hideBookATeamMember: boolean;
                rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
                rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
                bannerUrl: string | null;
                parentId: number | null;
                isOrganization: boolean;
                pendingPayment: boolean;
                isPlatform: boolean;
                createdByOAuthClientId: string | null;
                bookingLimits: import(".prisma/client").Prisma.JsonValue;
                includeManagedEventsInLimits: boolean;
            };
        } & {
            id: number;
            role: import(".prisma/client").$Enums.MembershipRole;
            userId: number;
            createdAt: Date | null;
            updatedAt: Date | null;
            disableImpersonation: boolean;
            teamId: number;
            accepted: boolean;
            customRoleId: string | null;
        })[];
    }>;
    publish: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: void;
        output: {
            url: string;
            message: string;
        };
    }>;
    setPassword: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            newPassword: string;
        };
        output: {
            update: boolean;
        };
    }>;
    getMembers: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            teamIdToExclude?: number | undefined;
            accepted?: boolean | undefined;
            distinctUser?: boolean | undefined;
        };
        output: {
            user: {
                name: string | null;
                id: number;
                email: string;
                username: string | null;
                avatarUrl: string | null;
                completedOnboarding: boolean;
            };
            id: number;
            role: import(".prisma/client").$Enums.MembershipRole;
            userId: number;
            disableImpersonation: boolean;
            teamId: number;
            accepted: boolean;
        }[];
    }>;
    listMembers: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            offset: number;
            limit: number;
            searchTerm?: string | undefined;
            expand?: "attributes"[] | undefined;
            filters?: {
                id: string;
                value: {
                    type: import("@calcom/features/data-table/lib/types").ColumnFilterType.SINGLE_SELECT;
                    data: string | number;
                } | {
                    type: import("@calcom/features/data-table/lib/types").ColumnFilterType.MULTI_SELECT;
                    data: (string | number)[];
                } | {
                    type: import("@calcom/features/data-table/lib/types").ColumnFilterType.TEXT;
                    data: {
                        operator: "endsWith" | "startsWith" | "equals" | "notEquals" | "contains" | "notContains" | "isEmpty" | "isNotEmpty";
                        operand: string;
                    };
                } | {
                    type: import("@calcom/features/data-table/lib/types").ColumnFilterType.NUMBER;
                    data: {
                        operator: "eq" | "neq" | "gt" | "gte" | "lt" | "lte";
                        operand: number;
                    };
                } | {
                    type: import("@calcom/features/data-table/lib/types").ColumnFilterType.DATE_RANGE;
                    data: {
                        startDate: string | null;
                        endDate: string | null;
                        preset: string;
                    };
                };
            }[] | undefined;
            oAuthClientId?: string | undefined;
        };
        output: {
            canUserGetMembers: boolean;
            rows: never[];
            meta: {
                totalRowCount: number;
            };
        } | {
            rows: {
                teams: {
                    id: number;
                    name: string;
                    slug: string | null;
                }[];
                attributes: {
                    weight: number;
                    id: string;
                    contains: string[];
                    value: string;
                    slug: string;
                    attributeId: string;
                    isGroup: boolean;
                }[] | undefined;
                twoFactorEnabled?: boolean | undefined;
                id: number;
                username: string | null;
                email: string;
                timeZone: string;
                role: import(".prisma/client").$Enums.MembershipRole;
                customRole: {
                    name: string;
                    color: string | null;
                    id: string;
                    type: import(".prisma/client").$Enums.RoleType;
                    description: string | null;
                    createdAt: Date;
                    updatedAt: Date;
                    teamId: number | null;
                } | null;
                accepted: boolean;
                disableImpersonation: boolean;
                completedOnboarding: boolean;
                lastActiveAt: string | null;
                createdAt: string | null;
                updatedAt: string | null;
                avatarUrl: string | null;
            }[];
            meta: {
                totalRowCount: number;
            };
            canUserGetMembers?: undefined;
        };
    }>;
    getBrand: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: {
            metadata: {
                defaultConferencingApp?: {
                    appSlug?: string | undefined;
                    appLink?: string | undefined;
                } | undefined;
                requestedSlug?: string | null | undefined;
                paymentId?: string | undefined;
                subscriptionId?: string | null | undefined;
                subscriptionItemId?: string | null | undefined;
                orgSeats?: number | null | undefined;
                orgPricePerSeat?: number | null | undefined;
                migratedToOrgFrom?: {
                    teamSlug?: string | null | undefined;
                    lastMigrationTime?: string | undefined;
                    reverted?: boolean | undefined;
                    lastRevertTime?: string | undefined;
                } | undefined;
                billingPeriod?: import("@calcom/prisma/zod-utils").BillingPeriod | undefined;
            } | null;
            slug: string;
            fullDomain: string;
            domainSuffix: string;
            name: string;
            logoUrl: string | null;
            isPlatform: boolean;
        } | null;
    }>;
    getUser: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            userId?: number | undefined;
        };
        output: {
            username: string | null;
            teams: {
                accepted: boolean;
                name: string;
                id: number;
            }[];
            role: import(".prisma/client").$Enums.MembershipRole;
            name: string | null;
            id: number;
            email: string;
            bio: string | null;
            avatarUrl: string | null;
            timeZone: string;
            schedules: {
                name: string;
                id: number;
            }[];
            profiles: {
                username: string;
            }[];
        };
    }>;
    updateUser: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            role: string;
            userId: number;
            timeZone: string;
            username?: string | undefined;
            bio?: string | undefined;
            name?: string | undefined;
            email?: string | undefined;
            avatar?: string | undefined;
            attributeOptions?: {
                userId: number;
                attributes: {
                    id: string;
                    options?: {
                        value: string;
                        label?: string | undefined;
                        weight?: number | undefined;
                    }[] | undefined;
                    value?: string | undefined;
                }[];
            } | undefined;
        };
        output: {
            success: boolean;
        };
    }>;
    getTeams: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: {
            name: string;
            id: number;
        }[];
    }>;
    addMembersToTeams: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            userIds: number[];
            teamIds: number[];
        };
        output: {
            success: boolean;
            invitedTotalUsers: number;
        };
    }>;
    addMembersToEventTypes: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            userIds: number[];
            teamIds: number[];
            eventTypeIds: number[];
        };
        output: import("@prisma/client/runtime/library").GetBatchResult;
    }>;
    removeHostsFromEventTypes: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            userIds: number[];
            eventTypeIds: number[];
        };
        output: import("@prisma/client/runtime/library").GetBatchResult;
    }>;
    bulkDeleteUsers: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            userIds: number[];
        };
        output: {
            success: boolean;
            usersDeleted: number;
        };
    }>;
    listOtherTeamMembers: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            teamId: number;
            limit: number;
            query?: string | undefined;
            offset?: number | undefined;
            cursor?: number | null | undefined;
        };
        output: {
            rows: {
                bookerUrl: string;
                user: {
                    name: string | null;
                    id: number;
                    email: string;
                    username: string | null;
                    avatarUrl: string | null;
                } & {
                    nonProfileUsername: string | null;
                    profile: import("@calcom/types/UserProfile").UserProfile;
                };
                id: number;
                role: import(".prisma/client").$Enums.MembershipRole;
                disableImpersonation: boolean;
                accepted: boolean;
            }[];
            nextCursor: number | null | undefined;
        };
    }>;
    getOtherTeam: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            teamId: number;
        };
        output: {
            safeBio: string;
            name: string;
            id: number;
            metadata: import(".prisma/client").Prisma.JsonValue;
            bio: string | null;
            slug: string | null;
            logoUrl: string | null;
            isPrivate: boolean;
            parent: {
                id: number;
                slug: string | null;
            } | null;
        };
    }>;
    listOtherTeams: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: {
            name: string;
            id: number;
            slug: string | null;
            logoUrl: string | null;
            parentId: number | null;
        }[];
    }>;
    deleteTeam: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            teamId: number;
        };
        output: void;
    }>;
    adminGetAll: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: {
            metadata: {
                defaultConferencingApp?: {
                    appSlug?: string | undefined;
                    appLink?: string | undefined;
                } | undefined;
                requestedSlug?: string | null | undefined;
                paymentId?: string | undefined;
                subscriptionId?: string | null | undefined;
                subscriptionItemId?: string | null | undefined;
                orgSeats?: number | null | undefined;
                orgPricePerSeat?: number | null | undefined;
                migratedToOrgFrom?: {
                    teamSlug?: string | null | undefined;
                    lastMigrationTime?: string | undefined;
                    reverted?: boolean | undefined;
                    lastRevertTime?: string | undefined;
                } | undefined;
                billingPeriod?: import("@calcom/prisma/zod-utils").BillingPeriod | undefined;
            } | null;
            name: string;
            id: number;
            organizationSettings: {
                id: number;
                allowSEOIndexing: boolean;
                organizationId: number;
                isOrganizationConfigured: boolean;
                isOrganizationVerified: boolean;
                orgAutoAcceptEmail: string;
                lockEventTypeCreationForUsers: boolean;
                adminGetsNoSlotsNotification: boolean;
                isAdminReviewed: boolean;
                isAdminAPIEnabled: boolean;
                orgProfileRedirectsToVerifiedDomain: boolean;
                disablePhoneOnlySMSNotifications: boolean;
            } | null;
            slug: string | null;
            members: {
                user: {
                    name: string | null;
                    id: number;
                    email: string;
                };
            }[];
        }[];
    }>;
    adminGet: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            id: number;
        };
        output: {
            metadata: {
                defaultConferencingApp?: {
                    appSlug?: string | undefined;
                    appLink?: string | undefined;
                } | undefined;
                requestedSlug?: string | null | undefined;
                paymentId?: string | undefined;
                subscriptionId?: string | null | undefined;
                subscriptionItemId?: string | null | undefined;
                orgSeats?: number | null | undefined;
                orgPricePerSeat?: number | null | undefined;
                migratedToOrgFrom?: {
                    teamSlug?: string | null | undefined;
                    lastMigrationTime?: string | undefined;
                    reverted?: boolean | undefined;
                    lastRevertTime?: string | undefined;
                } | undefined;
                billingPeriod?: import("@calcom/prisma/zod-utils").BillingPeriod | undefined;
            } | null;
            name: string;
            id: number;
            organizationSettings: {
                isOrganizationConfigured: boolean;
                isOrganizationVerified: boolean;
                orgAutoAcceptEmail: string;
            } | null;
            slug: string | null;
            isOrganization: boolean;
            members: {
                user: {
                    name: string | null;
                    id: number;
                    email: string;
                };
            }[];
        };
    }>;
    adminUpdate: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            id: number;
            name?: string | undefined;
            slug?: string | null | undefined;
            organizationSettings?: {
                isOrganizationVerified?: boolean | undefined;
                isOrganizationConfigured?: boolean | undefined;
                isAdminReviewed?: boolean | undefined;
                orgAutoAcceptEmail?: string | undefined;
                isAdminAPIEnabled?: boolean | undefined;
            } | undefined;
        };
        output: {
            name: string;
            id: number;
            metadata: import(".prisma/client").Prisma.JsonValue;
            createdAt: Date;
            bio: string | null;
            timeZone: string;
            weekStart: string;
            hideBranding: boolean;
            theme: string | null;
            timeFormat: number | null;
            brandColor: string | null;
            darkBrandColor: string | null;
            smsLockState: import(".prisma/client").$Enums.SMSLockState;
            smsLockReviewedByAdmin: boolean;
            slug: string | null;
            logoUrl: string | null;
            calVideoLogo: string | null;
            appLogo: string | null;
            appIconLogo: string | null;
            hideTeamProfileLink: boolean;
            isPrivate: boolean;
            hideBookATeamMember: boolean;
            rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
            rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
            bannerUrl: string | null;
            parentId: number | null;
            isOrganization: boolean;
            pendingPayment: boolean;
            isPlatform: boolean;
            createdByOAuthClientId: string | null;
            bookingLimits: import(".prisma/client").Prisma.JsonValue;
            includeManagedEventsInLimits: boolean;
        };
    }>;
    adminVerify: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            orgId: number;
        };
        output: {
            ok: boolean;
            message: string;
        };
    }>;
    adminDelete: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            orgId: number;
        };
        output: {
            ok: boolean;
            message: string;
        };
    }>;
    createPhoneCall: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            id?: number | undefined;
            eventTypeId: number;
            users?: number[] | undefined;
            templateType: "CHECK_IN_APPOINTMENT" | "CUSTOM_TEMPLATE";
            yourPhoneNumber: string;
            numberToCall: string;
            calApiKey: string;
            enabled?: boolean | undefined;
            schedulerName?: string | null | undefined;
            guestName?: string | undefined;
            guestEmail?: string | undefined;
            guestCompany?: string | undefined;
            beginMessage?: string | undefined;
            generalPrompt?: string | undefined;
        };
        output: {
            callId: string;
            agentId: string | undefined;
        };
    }>;
    createSelfHosted: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            onboardingId: string;
            language?: string | undefined;
            logo?: string | null | undefined;
            bio?: string | null | undefined;
            invitedMembers?: {
                email: string;
                name?: string | undefined;
            }[] | undefined;
            teams?: {
                name: string;
                id: number;
                slug: string | null;
                isBeingMigrated: boolean;
            }[] | undefined;
        };
        output: {
            organization: {
                metadata: import(".prisma/client").Prisma.JsonValue;
                name: string;
                id: number;
                createdAt: Date;
                bio: string | null;
                timeZone: string;
                weekStart: string;
                hideBranding: boolean;
                theme: string | null;
                timeFormat: number | null;
                brandColor: string | null;
                darkBrandColor: string | null;
                smsLockState: import(".prisma/client").$Enums.SMSLockState;
                smsLockReviewedByAdmin: boolean;
                slug: string | null;
                logoUrl: string | null;
                calVideoLogo: string | null;
                appLogo: string | null;
                appIconLogo: string | null;
                hideTeamProfileLink: boolean;
                isPrivate: boolean;
                hideBookATeamMember: boolean;
                rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
                rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
                bannerUrl: string | null;
                parentId: number | null;
                isOrganization: boolean;
                pendingPayment: boolean;
                isPlatform: boolean;
                createdByOAuthClientId: string | null;
                bookingLimits: import(".prisma/client").Prisma.JsonValue;
                includeManagedEventsInLimits: boolean;
            };
        };
    }>;
}>;
