import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TLocationOptionsInputSchema } from "./locationOptions.schema";
type LocationOptionsOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TLocationOptionsInputSchema;
};
export declare const locationOptionsHandler: ({ ctx, input }: LocationOptionsOptions) => Promise<{
    label: string;
    options: {
        label: string;
        value: string;
        disabled?: boolean;
        icon?: string;
        slug?: string;
        credentialId?: number;
        supportsCustomLabel?: boolean;
    }[];
}[]>;
export {};
