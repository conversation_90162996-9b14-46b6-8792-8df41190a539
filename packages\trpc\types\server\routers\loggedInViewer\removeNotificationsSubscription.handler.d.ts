import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TRemoveNotificationsSubscriptionInputSchema } from "./removeNotificationsSubscription.schema";
type AddSecondaryEmailOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TRemoveNotificationsSubscriptionInputSchema;
};
export declare const removeNotificationsSubscriptionHandler: ({ ctx }: AddSecondaryEmailOptions) => Promise<{
    message: string;
}>;
export {};
