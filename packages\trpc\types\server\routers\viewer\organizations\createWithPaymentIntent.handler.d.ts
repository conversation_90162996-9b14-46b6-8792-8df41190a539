import type { TrpcSessionUser } from "../../../types";
import type { TCreateWithPaymentIntentInputSchema } from "./createWithPaymentIntent.schema";
type CreateOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TCreateWithPaymentIntentInputSchema;
};
export declare const createHandler: ({ input, ctx }: CreateOptions) => Promise<{
    checkoutUrl: string | null;
}>;
export default createHandler;
