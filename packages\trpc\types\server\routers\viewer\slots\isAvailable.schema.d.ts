import { z } from "zod";
export declare const ZIsAvailableInputSchema: z.ZodObject<{
    slots: z.<PERSON><z.ZodObject<{
        utcStartIso: z.ZodString;
        utcEndIso: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        utcStartIso: string;
        utcEndIso: string;
    }, {
        utcStartIso: string;
        utcEndIso: string;
    }>, "many">;
    eventTypeId: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    eventTypeId: number;
    slots: {
        utcStartIso: string;
        utcEndIso: string;
    }[];
}, {
    eventTypeId: number;
    slots: {
        utcStartIso: string;
        utcEndIso: string;
    }[];
}>;
export type TIsAvailableInputSchema = z.infer<typeof ZIsAvailableInputSchema>;
export declare const ZIsAvailableOutputSchema: z.ZodObject<{
    slots: z.ZodArray<z.ZodObject<{
        utcStartIso: z.ZodString;
        utcEndIso: z.ZodString;
        status: z.ZodEnum<["available", "reserved", "minBookNoticeViolation", "slotInPast"]>;
        realStatus: z.ZodOptional<z.ZodEnum<["available", "reserved", "minBookNoticeViolation", "slotInPast"]>>;
    }, "strip", z.ZodTypeAny, {
        status: "available" | "reserved" | "minBookNoticeViolation" | "slotInPast";
        utcStartIso: string;
        utcEndIso: string;
        realStatus?: "available" | "reserved" | "minBookNoticeViolation" | "slotInPast" | undefined;
    }, {
        status: "available" | "reserved" | "minBookNoticeViolation" | "slotInPast";
        utcStartIso: string;
        utcEndIso: string;
        realStatus?: "available" | "reserved" | "minBookNoticeViolation" | "slotInPast" | undefined;
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    slots: {
        status: "available" | "reserved" | "minBookNoticeViolation" | "slotInPast";
        utcStartIso: string;
        utcEndIso: string;
        realStatus?: "available" | "reserved" | "minBookNoticeViolation" | "slotInPast" | undefined;
    }[];
}, {
    slots: {
        status: "available" | "reserved" | "minBookNoticeViolation" | "slotInPast";
        utcStartIso: string;
        utcEndIso: string;
        realStatus?: "available" | "reserved" | "minBookNoticeViolation" | "slotInPast" | undefined;
    }[];
}>;
export type TIsAvailableOutputSchema = z.infer<typeof ZIsAvailableOutputSchema>;
