import { z } from "zod";
import type { TrpcSessionUser } from "../../../types";
export declare const ZGetOtherTeamInputSchema: z.ZodObject<{
    teamId: z.ZodNumber;
}, "strip", z.<PERSON>od<PERSON>ype<PERSON>ny, {
    teamId: number;
}, {
    teamId: number;
}>;
export type TGetOtherTeamInputSchema = z.infer<typeof ZGetOtherTeamInputSchema>;
type GetOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TGetOtherTeamInputSchema;
};
export declare const getOtherTeamHandler: ({ input }: GetOptions) => Promise<{
    safeBio: string;
    name: string;
    id: number;
    metadata: import(".prisma/client").Prisma.JsonValue;
    bio: string | null;
    slug: string | null;
    logoUrl: string | null;
    isPrivate: boolean;
    parent: {
        id: number;
        slug: string | null;
    } | null;
}>;
export default getOtherTeamHandler;
