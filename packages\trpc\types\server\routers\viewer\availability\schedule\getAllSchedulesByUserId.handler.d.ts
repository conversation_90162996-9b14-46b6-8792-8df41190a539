import type { TrpcSessionUser } from "../../../../types";
import type { TGetAllByUserIdInputSchema } from "./getAllSchedulesByUserId.schema";
type GetOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TGetAllByUserIdInputSchema;
};
export declare const getAllSchedulesByUserIdHandler: ({ ctx, input }: GetOptions) => Promise<{
    schedules: {
        isDefault: boolean;
        readOnly: boolean;
        name: string;
        id: number;
        userId: number;
    }[];
}>;
export {};
