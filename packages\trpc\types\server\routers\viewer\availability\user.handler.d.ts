import type { TrpcSessionUser } from "../../../types";
import type { TUserInputSchema } from "./user.schema";
type UserOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TUserInputSchema;
};
export declare const userHandler: ({ input }: UserOptions) => Promise<{
    busy: import("@calcom/types/Calendar").EventBusyDetails[];
    timeZone: string;
    dateRanges: {
        start: import("dayjs").Dayjs;
        end: import("dayjs").Dayjs;
    }[];
    oooExcludedDateRanges: {
        start: import("dayjs").Dayjs;
        end: import("dayjs").Dayjs;
    }[];
    workingHours: import("@calcom/types/schedule").WorkingHours[];
    dateOverrides: import("@calcom/types/schedule").TimeRange[];
    currentSeats: {
        uid: string;
        startTime: Date;
        _count: {
            attendees: number;
        };
    }[] | null;
    datesOutOfOffice: import("@calcom/lib/getUserAvailability").IOutOfOfficeData;
} | {
    busy: never[];
    timeZone: string;
    dateRanges: never[];
    oooExcludedDateRanges: never[];
    workingHours: never[];
    dateOverrides: never[];
    currentSeats: never[];
    datesOutOfOffice: undefined;
}>;
export {};
