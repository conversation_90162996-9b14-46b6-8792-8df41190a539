import type { IncomingMessage } from "http";
import { z } from "zod";
export declare const getScheduleSchemaObject: z.ZodObject<{
    startTime: z.ZodEffects<z.ZodString, string, string>;
    endTime: z.ZodEffects<z.ZodString, string, string>;
    eventTypeId: z.ZodOptional<z.ZodNumber>;
    eventTypeSlug: z.ZodOptional<z.ZodString>;
    timeZone: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
    usernameList: z.<PERSON>odOptional<z.ZodArray<z.ZodString, "many">>;
    debug: z.ZodOptional<z.ZodBoolean>;
    duration: z.ZodEffects<z.ZodOptional<z.ZodString>, number | "" | undefined, string | undefined>;
    rescheduleUid: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    isTeamEvent: z.ZodDefault<z.ZodOptional<z.ZodBoolean>>;
    orgSlug: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    teamMemberEmail: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    routedTeamMemberIds: z.ZodOptional<z.ZodNullable<z.ZodArray<z.ZodNumber, "many">>>;
    skipContactOwner: z.ZodOptional<z.ZodNullable<z.ZodBoolean>>;
    _enableTroubleshooter: z.ZodOptional<z.ZodBoolean>;
    _bypassCalendarBusyTimes: z.ZodOptional<z.ZodBoolean>;
    _silentCalendarFailures: z.ZodOptional<z.ZodBoolean>;
    _shouldServeCache: z.ZodOptional<z.ZodBoolean>;
    routingFormResponseId: z.ZodOptional<z.ZodNumber>;
    queuedFormResponseId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    email: z.ZodOptional<z.ZodNullable<z.ZodString>>;
}, "strip", z.ZodTypeAny, {
    startTime: string;
    endTime: string;
    isTeamEvent: boolean;
    eventTypeId?: number | undefined;
    eventTypeSlug?: string | undefined;
    timeZone?: string | undefined;
    usernameList?: string[] | undefined;
    debug?: boolean | undefined;
    duration?: number | "" | undefined;
    rescheduleUid?: string | null | undefined;
    orgSlug?: string | null | undefined;
    teamMemberEmail?: string | null | undefined;
    routedTeamMemberIds?: number[] | null | undefined;
    skipContactOwner?: boolean | null | undefined;
    _enableTroubleshooter?: boolean | undefined;
    _bypassCalendarBusyTimes?: boolean | undefined;
    _silentCalendarFailures?: boolean | undefined;
    _shouldServeCache?: boolean | undefined;
    routingFormResponseId?: number | undefined;
    queuedFormResponseId?: string | null | undefined;
    email?: string | null | undefined;
}, {
    startTime: string;
    endTime: string;
    eventTypeId?: number | undefined;
    eventTypeSlug?: string | undefined;
    timeZone?: string | undefined;
    usernameList?: string[] | undefined;
    debug?: boolean | undefined;
    duration?: string | undefined;
    rescheduleUid?: string | null | undefined;
    isTeamEvent?: boolean | undefined;
    orgSlug?: string | null | undefined;
    teamMemberEmail?: string | null | undefined;
    routedTeamMemberIds?: number[] | null | undefined;
    skipContactOwner?: boolean | null | undefined;
    _enableTroubleshooter?: boolean | undefined;
    _bypassCalendarBusyTimes?: boolean | undefined;
    _silentCalendarFailures?: boolean | undefined;
    _shouldServeCache?: boolean | undefined;
    routingFormResponseId?: number | undefined;
    queuedFormResponseId?: string | null | undefined;
    email?: string | null | undefined;
}>;
export declare const getScheduleSchema: z.ZodEffects<z.ZodEffects<z.ZodEffects<z.ZodObject<{
    startTime: z.ZodEffects<z.ZodString, string, string>;
    endTime: z.ZodEffects<z.ZodString, string, string>;
    eventTypeId: z.ZodOptional<z.ZodNumber>;
    eventTypeSlug: z.ZodOptional<z.ZodString>;
    timeZone: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
    usernameList: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    debug: z.ZodOptional<z.ZodBoolean>;
    duration: z.ZodEffects<z.ZodOptional<z.ZodString>, number | "" | undefined, string | undefined>;
    rescheduleUid: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    isTeamEvent: z.ZodDefault<z.ZodOptional<z.ZodBoolean>>;
    orgSlug: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    teamMemberEmail: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    routedTeamMemberIds: z.ZodOptional<z.ZodNullable<z.ZodArray<z.ZodNumber, "many">>>;
    skipContactOwner: z.ZodOptional<z.ZodNullable<z.ZodBoolean>>;
    _enableTroubleshooter: z.ZodOptional<z.ZodBoolean>;
    _bypassCalendarBusyTimes: z.ZodOptional<z.ZodBoolean>;
    _silentCalendarFailures: z.ZodOptional<z.ZodBoolean>;
    _shouldServeCache: z.ZodOptional<z.ZodBoolean>;
    routingFormResponseId: z.ZodOptional<z.ZodNumber>;
    queuedFormResponseId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    email: z.ZodOptional<z.ZodNullable<z.ZodString>>;
}, "strip", z.ZodTypeAny, {
    startTime: string;
    endTime: string;
    isTeamEvent: boolean;
    eventTypeId?: number | undefined;
    eventTypeSlug?: string | undefined;
    timeZone?: string | undefined;
    usernameList?: string[] | undefined;
    debug?: boolean | undefined;
    duration?: number | "" | undefined;
    rescheduleUid?: string | null | undefined;
    orgSlug?: string | null | undefined;
    teamMemberEmail?: string | null | undefined;
    routedTeamMemberIds?: number[] | null | undefined;
    skipContactOwner?: boolean | null | undefined;
    _enableTroubleshooter?: boolean | undefined;
    _bypassCalendarBusyTimes?: boolean | undefined;
    _silentCalendarFailures?: boolean | undefined;
    _shouldServeCache?: boolean | undefined;
    routingFormResponseId?: number | undefined;
    queuedFormResponseId?: string | null | undefined;
    email?: string | null | undefined;
}, {
    startTime: string;
    endTime: string;
    eventTypeId?: number | undefined;
    eventTypeSlug?: string | undefined;
    timeZone?: string | undefined;
    usernameList?: string[] | undefined;
    debug?: boolean | undefined;
    duration?: string | undefined;
    rescheduleUid?: string | null | undefined;
    isTeamEvent?: boolean | undefined;
    orgSlug?: string | null | undefined;
    teamMemberEmail?: string | null | undefined;
    routedTeamMemberIds?: number[] | null | undefined;
    skipContactOwner?: boolean | null | undefined;
    _enableTroubleshooter?: boolean | undefined;
    _bypassCalendarBusyTimes?: boolean | undefined;
    _silentCalendarFailures?: boolean | undefined;
    _shouldServeCache?: boolean | undefined;
    routingFormResponseId?: number | undefined;
    queuedFormResponseId?: string | null | undefined;
    email?: string | null | undefined;
}>, {
    startTime: string;
    endTime: string;
    isTeamEvent: boolean;
    eventTypeId?: number | undefined;
    eventTypeSlug?: string | undefined;
    timeZone?: string | undefined;
    usernameList?: string[] | undefined;
    debug?: boolean | undefined;
    duration?: number | "" | undefined;
    rescheduleUid?: string | null | undefined;
    orgSlug?: string | null | undefined;
    teamMemberEmail?: string | null | undefined;
    routedTeamMemberIds?: number[] | null | undefined;
    skipContactOwner?: boolean | null | undefined;
    _enableTroubleshooter?: boolean | undefined;
    _bypassCalendarBusyTimes?: boolean | undefined;
    _silentCalendarFailures?: boolean | undefined;
    _shouldServeCache?: boolean | undefined;
    routingFormResponseId?: number | undefined;
    queuedFormResponseId?: string | null | undefined;
    email?: string | null | undefined;
}, {
    startTime: string;
    endTime: string;
    eventTypeId?: number | undefined;
    eventTypeSlug?: string | undefined;
    timeZone?: string | undefined;
    usernameList?: string[] | undefined;
    debug?: boolean | undefined;
    duration?: string | undefined;
    rescheduleUid?: string | null | undefined;
    isTeamEvent?: boolean | undefined;
    orgSlug?: string | null | undefined;
    teamMemberEmail?: string | null | undefined;
    routedTeamMemberIds?: number[] | null | undefined;
    skipContactOwner?: boolean | null | undefined;
    _enableTroubleshooter?: boolean | undefined;
    _bypassCalendarBusyTimes?: boolean | undefined;
    _silentCalendarFailures?: boolean | undefined;
    _shouldServeCache?: boolean | undefined;
    routingFormResponseId?: number | undefined;
    queuedFormResponseId?: string | null | undefined;
    email?: string | null | undefined;
}>, {
    startTime: string;
    endTime: string;
    isTeamEvent: boolean;
    eventTypeId?: number | undefined;
    eventTypeSlug?: string | undefined;
    timeZone?: string | undefined;
    usernameList?: string[] | undefined;
    debug?: boolean | undefined;
    duration?: number | "" | undefined;
    rescheduleUid?: string | null | undefined;
    orgSlug?: string | null | undefined;
    teamMemberEmail?: string | null | undefined;
    routedTeamMemberIds?: number[] | null | undefined;
    skipContactOwner?: boolean | null | undefined;
    _enableTroubleshooter?: boolean | undefined;
    _bypassCalendarBusyTimes?: boolean | undefined;
    _silentCalendarFailures?: boolean | undefined;
    _shouldServeCache?: boolean | undefined;
    routingFormResponseId?: number | undefined;
    queuedFormResponseId?: string | null | undefined;
    email?: string | null | undefined;
}, {
    startTime: string;
    endTime: string;
    eventTypeId?: number | undefined;
    eventTypeSlug?: string | undefined;
    timeZone?: string | undefined;
    usernameList?: string[] | undefined;
    debug?: boolean | undefined;
    duration?: string | undefined;
    rescheduleUid?: string | null | undefined;
    isTeamEvent?: boolean | undefined;
    orgSlug?: string | null | undefined;
    teamMemberEmail?: string | null | undefined;
    routedTeamMemberIds?: number[] | null | undefined;
    skipContactOwner?: boolean | null | undefined;
    _enableTroubleshooter?: boolean | undefined;
    _bypassCalendarBusyTimes?: boolean | undefined;
    _silentCalendarFailures?: boolean | undefined;
    _shouldServeCache?: boolean | undefined;
    routingFormResponseId?: number | undefined;
    queuedFormResponseId?: string | null | undefined;
    email?: string | null | undefined;
}>, {
    startTime: string;
    endTime: string;
    isTeamEvent: boolean;
    eventTypeId?: number | undefined;
    eventTypeSlug?: string | undefined;
    timeZone?: string | undefined;
    usernameList?: string[] | undefined;
    debug?: boolean | undefined;
    duration?: number | "" | undefined;
    rescheduleUid?: string | null | undefined;
    orgSlug?: string | null | undefined;
    teamMemberEmail?: string | null | undefined;
    routedTeamMemberIds?: number[] | null | undefined;
    skipContactOwner?: boolean | null | undefined;
    _enableTroubleshooter?: boolean | undefined;
    _bypassCalendarBusyTimes?: boolean | undefined;
    _silentCalendarFailures?: boolean | undefined;
    _shouldServeCache?: boolean | undefined;
    routingFormResponseId?: number | undefined;
    queuedFormResponseId?: string | null | undefined;
    email?: string | null | undefined;
}, {
    startTime: string;
    endTime: string;
    eventTypeId?: number | undefined;
    eventTypeSlug?: string | undefined;
    timeZone?: string | undefined;
    usernameList?: string[] | undefined;
    debug?: boolean | undefined;
    duration?: string | undefined;
    rescheduleUid?: string | null | undefined;
    isTeamEvent?: boolean | undefined;
    orgSlug?: string | null | undefined;
    teamMemberEmail?: string | null | undefined;
    routedTeamMemberIds?: number[] | null | undefined;
    skipContactOwner?: boolean | null | undefined;
    _enableTroubleshooter?: boolean | undefined;
    _bypassCalendarBusyTimes?: boolean | undefined;
    _silentCalendarFailures?: boolean | undefined;
    _shouldServeCache?: boolean | undefined;
    routingFormResponseId?: number | undefined;
    queuedFormResponseId?: string | null | undefined;
    email?: string | null | undefined;
}>;
export declare const reserveSlotSchema: z.ZodEffects<z.ZodObject<{
    eventTypeId: z.ZodNumber;
    slotUtcStartDate: z.ZodString;
    slotUtcEndDate: z.ZodString;
    _isDryRun: z.ZodOptional<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    eventTypeId: number;
    slotUtcStartDate: string;
    slotUtcEndDate: string;
    _isDryRun?: boolean | undefined;
}, {
    eventTypeId: number;
    slotUtcStartDate: string;
    slotUtcEndDate: string;
    _isDryRun?: boolean | undefined;
}>, {
    eventTypeId: number;
    slotUtcStartDate: string;
    slotUtcEndDate: string;
    _isDryRun?: boolean | undefined;
}, {
    eventTypeId: number;
    slotUtcStartDate: string;
    slotUtcEndDate: string;
    _isDryRun?: boolean | undefined;
}>;
export type Slot = {
    time: string;
    userIds?: number[];
    attendees?: number;
    bookingUid?: string;
    users?: string[];
};
export declare const removeSelectedSlotSchema: z.ZodObject<{
    uid: z.ZodNullable<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    uid: string | null;
}, {
    uid: string | null;
}>;
export interface ContextForGetSchedule extends Record<string, unknown> {
    req?: (IncomingMessage & {
        cookies: Partial<{
            [key: string]: string;
        }>;
    }) | undefined;
}
export type TGetScheduleInputSchema = z.infer<typeof getScheduleSchemaObject>;
export declare const ZGetScheduleInputSchema: z.ZodEffects<z.ZodEffects<z.ZodEffects<z.ZodObject<{
    startTime: z.ZodEffects<z.ZodString, string, string>;
    endTime: z.ZodEffects<z.ZodString, string, string>;
    eventTypeId: z.ZodOptional<z.ZodNumber>;
    eventTypeSlug: z.ZodOptional<z.ZodString>;
    timeZone: z.ZodOptional<z.ZodEffects<z.ZodString, string, string>>;
    usernameList: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    debug: z.ZodOptional<z.ZodBoolean>;
    duration: z.ZodEffects<z.ZodOptional<z.ZodString>, number | "" | undefined, string | undefined>;
    rescheduleUid: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    isTeamEvent: z.ZodDefault<z.ZodOptional<z.ZodBoolean>>;
    orgSlug: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    teamMemberEmail: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    routedTeamMemberIds: z.ZodOptional<z.ZodNullable<z.ZodArray<z.ZodNumber, "many">>>;
    skipContactOwner: z.ZodOptional<z.ZodNullable<z.ZodBoolean>>;
    _enableTroubleshooter: z.ZodOptional<z.ZodBoolean>;
    _bypassCalendarBusyTimes: z.ZodOptional<z.ZodBoolean>;
    _silentCalendarFailures: z.ZodOptional<z.ZodBoolean>;
    _shouldServeCache: z.ZodOptional<z.ZodBoolean>;
    routingFormResponseId: z.ZodOptional<z.ZodNumber>;
    queuedFormResponseId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    email: z.ZodOptional<z.ZodNullable<z.ZodString>>;
}, "strip", z.ZodTypeAny, {
    startTime: string;
    endTime: string;
    isTeamEvent: boolean;
    eventTypeId?: number | undefined;
    eventTypeSlug?: string | undefined;
    timeZone?: string | undefined;
    usernameList?: string[] | undefined;
    debug?: boolean | undefined;
    duration?: number | "" | undefined;
    rescheduleUid?: string | null | undefined;
    orgSlug?: string | null | undefined;
    teamMemberEmail?: string | null | undefined;
    routedTeamMemberIds?: number[] | null | undefined;
    skipContactOwner?: boolean | null | undefined;
    _enableTroubleshooter?: boolean | undefined;
    _bypassCalendarBusyTimes?: boolean | undefined;
    _silentCalendarFailures?: boolean | undefined;
    _shouldServeCache?: boolean | undefined;
    routingFormResponseId?: number | undefined;
    queuedFormResponseId?: string | null | undefined;
    email?: string | null | undefined;
}, {
    startTime: string;
    endTime: string;
    eventTypeId?: number | undefined;
    eventTypeSlug?: string | undefined;
    timeZone?: string | undefined;
    usernameList?: string[] | undefined;
    debug?: boolean | undefined;
    duration?: string | undefined;
    rescheduleUid?: string | null | undefined;
    isTeamEvent?: boolean | undefined;
    orgSlug?: string | null | undefined;
    teamMemberEmail?: string | null | undefined;
    routedTeamMemberIds?: number[] | null | undefined;
    skipContactOwner?: boolean | null | undefined;
    _enableTroubleshooter?: boolean | undefined;
    _bypassCalendarBusyTimes?: boolean | undefined;
    _silentCalendarFailures?: boolean | undefined;
    _shouldServeCache?: boolean | undefined;
    routingFormResponseId?: number | undefined;
    queuedFormResponseId?: string | null | undefined;
    email?: string | null | undefined;
}>, {
    startTime: string;
    endTime: string;
    isTeamEvent: boolean;
    eventTypeId?: number | undefined;
    eventTypeSlug?: string | undefined;
    timeZone?: string | undefined;
    usernameList?: string[] | undefined;
    debug?: boolean | undefined;
    duration?: number | "" | undefined;
    rescheduleUid?: string | null | undefined;
    orgSlug?: string | null | undefined;
    teamMemberEmail?: string | null | undefined;
    routedTeamMemberIds?: number[] | null | undefined;
    skipContactOwner?: boolean | null | undefined;
    _enableTroubleshooter?: boolean | undefined;
    _bypassCalendarBusyTimes?: boolean | undefined;
    _silentCalendarFailures?: boolean | undefined;
    _shouldServeCache?: boolean | undefined;
    routingFormResponseId?: number | undefined;
    queuedFormResponseId?: string | null | undefined;
    email?: string | null | undefined;
}, {
    startTime: string;
    endTime: string;
    eventTypeId?: number | undefined;
    eventTypeSlug?: string | undefined;
    timeZone?: string | undefined;
    usernameList?: string[] | undefined;
    debug?: boolean | undefined;
    duration?: string | undefined;
    rescheduleUid?: string | null | undefined;
    isTeamEvent?: boolean | undefined;
    orgSlug?: string | null | undefined;
    teamMemberEmail?: string | null | undefined;
    routedTeamMemberIds?: number[] | null | undefined;
    skipContactOwner?: boolean | null | undefined;
    _enableTroubleshooter?: boolean | undefined;
    _bypassCalendarBusyTimes?: boolean | undefined;
    _silentCalendarFailures?: boolean | undefined;
    _shouldServeCache?: boolean | undefined;
    routingFormResponseId?: number | undefined;
    queuedFormResponseId?: string | null | undefined;
    email?: string | null | undefined;
}>, {
    startTime: string;
    endTime: string;
    isTeamEvent: boolean;
    eventTypeId?: number | undefined;
    eventTypeSlug?: string | undefined;
    timeZone?: string | undefined;
    usernameList?: string[] | undefined;
    debug?: boolean | undefined;
    duration?: number | "" | undefined;
    rescheduleUid?: string | null | undefined;
    orgSlug?: string | null | undefined;
    teamMemberEmail?: string | null | undefined;
    routedTeamMemberIds?: number[] | null | undefined;
    skipContactOwner?: boolean | null | undefined;
    _enableTroubleshooter?: boolean | undefined;
    _bypassCalendarBusyTimes?: boolean | undefined;
    _silentCalendarFailures?: boolean | undefined;
    _shouldServeCache?: boolean | undefined;
    routingFormResponseId?: number | undefined;
    queuedFormResponseId?: string | null | undefined;
    email?: string | null | undefined;
}, {
    startTime: string;
    endTime: string;
    eventTypeId?: number | undefined;
    eventTypeSlug?: string | undefined;
    timeZone?: string | undefined;
    usernameList?: string[] | undefined;
    debug?: boolean | undefined;
    duration?: string | undefined;
    rescheduleUid?: string | null | undefined;
    isTeamEvent?: boolean | undefined;
    orgSlug?: string | null | undefined;
    teamMemberEmail?: string | null | undefined;
    routedTeamMemberIds?: number[] | null | undefined;
    skipContactOwner?: boolean | null | undefined;
    _enableTroubleshooter?: boolean | undefined;
    _bypassCalendarBusyTimes?: boolean | undefined;
    _silentCalendarFailures?: boolean | undefined;
    _shouldServeCache?: boolean | undefined;
    routingFormResponseId?: number | undefined;
    queuedFormResponseId?: string | null | undefined;
    email?: string | null | undefined;
}>, {
    startTime: string;
    endTime: string;
    isTeamEvent: boolean;
    eventTypeId?: number | undefined;
    eventTypeSlug?: string | undefined;
    timeZone?: string | undefined;
    usernameList?: string[] | undefined;
    debug?: boolean | undefined;
    duration?: number | "" | undefined;
    rescheduleUid?: string | null | undefined;
    orgSlug?: string | null | undefined;
    teamMemberEmail?: string | null | undefined;
    routedTeamMemberIds?: number[] | null | undefined;
    skipContactOwner?: boolean | null | undefined;
    _enableTroubleshooter?: boolean | undefined;
    _bypassCalendarBusyTimes?: boolean | undefined;
    _silentCalendarFailures?: boolean | undefined;
    _shouldServeCache?: boolean | undefined;
    routingFormResponseId?: number | undefined;
    queuedFormResponseId?: string | null | undefined;
    email?: string | null | undefined;
}, {
    startTime: string;
    endTime: string;
    eventTypeId?: number | undefined;
    eventTypeSlug?: string | undefined;
    timeZone?: string | undefined;
    usernameList?: string[] | undefined;
    debug?: boolean | undefined;
    duration?: string | undefined;
    rescheduleUid?: string | null | undefined;
    isTeamEvent?: boolean | undefined;
    orgSlug?: string | null | undefined;
    teamMemberEmail?: string | null | undefined;
    routedTeamMemberIds?: number[] | null | undefined;
    skipContactOwner?: boolean | null | undefined;
    _enableTroubleshooter?: boolean | undefined;
    _bypassCalendarBusyTimes?: boolean | undefined;
    _silentCalendarFailures?: boolean | undefined;
    _shouldServeCache?: boolean | undefined;
    routingFormResponseId?: number | undefined;
    queuedFormResponseId?: string | null | undefined;
    email?: string | null | undefined;
}>;
export type GetScheduleOptions = {
    ctx?: ContextForGetSchedule;
    input: TGetScheduleInputSchema;
};
