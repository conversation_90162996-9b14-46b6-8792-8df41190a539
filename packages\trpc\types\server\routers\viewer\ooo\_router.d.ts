export declare const oooRouter: import("@trpc/server/unstable-core-do-not-import").CreateRouterInner<import("@trpc/server/unstable-core-do-not-import").RootConfig<{
    ctx: import("../../../createContext").InnerContext;
    meta: object;
    errorShape: {
        message: string;
        code: number;
        data: {
            code: string;
            httpStatus: number;
            path?: string;
            [key: string]: unknown;
        };
    };
    transformer: {
        stringify: (object: any) => string;
        parse: <T = unknown>(string: string) => T;
        serialize: (object: any) => import("superjson/dist/types").SuperJSONResult;
        deserialize: <T_1 = unknown>(payload: import("superjson/dist/types").SuperJSONResult) => T_1;
        registerClass: (v: import("superjson/dist/types").Class, options?: string | import("superjson/dist/class-registry").RegisterOptions | undefined) => void;
        registerSymbol: (v: Symbol, identifier?: string | undefined) => void;
        registerCustom: <I, O extends import("superjson/dist/types").JSONValue>(transformer: Omit<import("superjson/dist/custom-transformer-registry").CustomTransfomer<I, O>, "name">, name: string) => void;
        allowErrorProps: (...props: string[]) => void;
    };
}>, {
    outOfOfficeCreateOrUpdate: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            reasonId: number;
            dateRange: {
                startDate: Date;
                endDate: Date;
            };
            startDateOffset: number;
            endDateOffset: number;
            toTeamUserId: number | null;
            uuid?: string | null | undefined;
            forUserId?: number | null | undefined;
            notes?: string | null | undefined;
        };
        output: {} | undefined;
    }>;
    outOfOfficeEntryDelete: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            outOfOfficeUid: string;
            userId?: number | null | undefined;
        };
        output: {};
    }>;
    outOfOfficeEntriesList: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            limit: number;
            cursor?: number | null | undefined;
            fetchTeamMembersEntries?: boolean | undefined;
            searchTerm?: string | undefined;
            endDateFilterStartRange?: string | undefined;
            endDateFilterEndRange?: string | undefined;
        };
        output: {
            rows: {
                canEditAndDelete: boolean;
                user: {
                    name: string | null;
                    id: number;
                    role: import(".prisma/client").$Enums.UserPermissionRole;
                    metadata: import(".prisma/client").Prisma.JsonValue;
                    locale: string | null;
                    startTime: number;
                    endTime: number;
                    creationSource: import(".prisma/client").$Enums.CreationSource | null;
                    email: string;
                    movedToProfileId: number | null;
                    username: string | null;
                    emailVerified: Date | null;
                    bio: string | null;
                    avatarUrl: string | null;
                    timeZone: string;
                    weekStart: string;
                    bufferTime: number;
                    hideBranding: boolean;
                    theme: string | null;
                    appTheme: string | null;
                    createdDate: Date;
                    trialEndsAt: Date | null;
                    lastActiveAt: Date | null;
                    defaultScheduleId: number | null;
                    completedOnboarding: boolean;
                    timeFormat: number | null;
                    twoFactorSecret: string | null;
                    twoFactorEnabled: boolean;
                    backupCodes: string | null;
                    identityProvider: import(".prisma/client").$Enums.IdentityProvider;
                    identityProviderId: string | null;
                    invitedTo: number | null;
                    brandColor: string | null;
                    darkBrandColor: string | null;
                    allowDynamicBooking: boolean | null;
                    allowSEOIndexing: boolean | null;
                    receiveMonthlyDigestEmail: boolean | null;
                    verified: boolean | null;
                    disableImpersonation: boolean;
                    organizationId: number | null;
                    locked: boolean;
                    isPlatformManaged: boolean;
                    smsLockState: import(".prisma/client").$Enums.SMSLockState;
                    smsLockReviewedByAdmin: boolean;
                    referralLinkId: string | null;
                    whitelistWorkflows: boolean;
                };
                id: number;
                end: Date;
                notes: string | null;
                reason: {
                    id: number;
                    userId: number | null;
                    reason: string;
                    emoji: string;
                } | null;
                uuid: string;
                start: Date;
                toUser: {
                    name: string | null;
                    email: string;
                    username: string | null;
                } | null;
                toUserId: number | null;
            }[];
            nextCursor: number | undefined;
            meta: {
                totalRowCount: number;
            };
        };
    }>;
    outOfOfficeReasonList: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: {
            id: number;
            userId: number | null;
            enabled: boolean;
            reason: string;
            emoji: string;
        }[];
    }>;
}>;
