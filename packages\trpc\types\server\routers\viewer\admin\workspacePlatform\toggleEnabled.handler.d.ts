import type { z } from "zod";
import type { workspacePlatformToggleEnabledSchema } from "./schema";
export default function toggleEnabledHandler({ input, }: {
    input: z.infer<typeof workspacePlatformToggleEnabledSchema>;
}): Promise<Omit<{
    name: string;
    id: number;
    description: string;
    createdAt: Date;
    updatedAt: Date;
    slug: string;
    enabled: boolean;
    defaultServiceAccountKey: import(".prisma/client").Prisma.JsonValue;
}, "defaultServiceAccountKey"> & {
    defaultServiceAccountKey: undefined;
}>;
