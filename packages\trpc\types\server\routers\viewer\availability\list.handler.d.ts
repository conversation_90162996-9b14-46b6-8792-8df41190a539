import type { TrpcSessionUser } from "../../../types";
type ListOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
};
export declare const listHandler: ({ ctx }: ListOptions) => Promise<{
    schedules: {
        isDefault: boolean;
        name: string;
        id: number;
        availability: {
            id: number;
            date: Date | null;
            startTime: Date;
            endTime: Date;
            userId: number | null;
            eventTypeId: number | null;
            scheduleId: number | null;
            days: number[];
        }[];
        timeZone: string | null;
    }[];
}>;
export {};
