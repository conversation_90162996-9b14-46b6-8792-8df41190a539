import type { TrpcSessionUser } from "../../../types";
type OrganizationOnboarding = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
};
export declare function getOrganizationOnboardingHandler({ ctx }: OrganizationOnboarding): Promise<{
    name: string;
    id: string;
    error: string | null;
    createdAt: Date;
    updatedAt: Date;
    bio: string | null;
    organizationId: number | null;
    teams: import(".prisma/client").Prisma.JsonValue;
    slug: string;
    isPlatform: boolean;
    logo: string | null;
    createdById: number;
    orgOwnerEmail: string;
    billingPeriod: import(".prisma/client").$Enums.BillingPeriod;
    pricePerSeat: number;
    seats: number;
    isDomainConfigured: boolean;
    stripeCustomerId: string | null;
    stripeSubscriptionId: string | null;
    stripeSubscriptionItemId: string | null;
    invitedMembers: import(".prisma/client").Prisma.JsonValue;
    isComplete: boolean;
} | null>;
export default getOrganizationOnboardingHandler;
