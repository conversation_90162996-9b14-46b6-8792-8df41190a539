/**
 * Simplified version of legacyListMembers.handler.ts that returns basic member info.
 * Used for filtering people on /bookings.
 */
import type { PrismaClient } from "@calcom/prisma";
import type { TrpcSessionUser } from "@calcom/trpc/server/types";
type ListSimpleMembersOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
        prisma: PrismaClient;
    };
};
export declare const listSimpleMembers: ({ ctx }: ListSimpleMembersOptions) => Promise<{
    name: string | null;
    id: number;
    email: string;
    username: string | null;
    avatarUrl: string | null;
}[]>;
export default listSimpleMembers;
