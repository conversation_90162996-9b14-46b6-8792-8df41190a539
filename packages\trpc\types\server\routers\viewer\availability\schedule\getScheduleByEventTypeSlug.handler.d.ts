import type { PrismaClient } from "@calcom/prisma";
import type { TrpcSessionUser } from "../../../../types";
import type { TGetByEventSlugInputSchema } from "./getScheduleByEventTypeSlug.schema";
type GetOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
        prisma: PrismaClient;
    };
    input: TGetByEventSlugInputSchema;
};
export declare const getScheduleByEventSlugHandler: ({ ctx, input }: GetOptions) => Promise<{
    id: number;
    name: string;
    isManaged: boolean;
    workingHours: import("@calcom/types/schedule").WorkingHours[];
    schedule: {
        id: number;
        date: Date | null;
        startTime: Date;
        endTime: Date;
        userId: number | null;
        eventTypeId: number | null;
        scheduleId: number | null;
        days: number[];
    }[];
    availability: {
        end: Date;
        userId?: number | null;
        start: Date;
    }[][];
    timeZone: string;
    dateOverrides: {
        ranges: import("@calcom/types/schedule").TimeRange[];
    }[];
    isDefault: boolean;
    isLastSchedule: boolean;
    readOnly: boolean;
    userId: number;
} | {
    id: number;
    name: string;
    availability: never[][];
    dateOverrides: never[];
    timeZone: string;
    workingHours: never[];
    isDefault: boolean;
}>;
export {};
