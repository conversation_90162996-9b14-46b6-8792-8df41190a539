import { z } from "zod";
export declare const ZUpdateInputSchema: z.ZodObject<{
    id: z.ZodN<PERSON>ber;
    name: z.<PERSON>od<PERSON>tring;
    activeOn: z<PERSON><PERSON><z.ZodNumber, "many">;
    steps: z.<PERSON><PERSON><PERSON><z.ZodObject<{
        id: z.ZodNumber;
        stepNumber: z.Z<PERSON>N<PERSON><PERSON>;
        action: z.ZodE<PERSON><["EMAIL_HOST", "EMAIL_ATTENDEE", "EMAIL_ADDRESS", "SMS_ATTENDEE", "SMS_NUMBER", "WHATSAPP_ATTENDEE", "WHATSAPP_NUMBER", "CAL_AI_PHONE_CALL"]>;
        workflowId: z.<PERSON>od<PERSON><PERSON>;
        sendTo: z.Zod<PERSON>ullable<z.ZodString>;
        reminderBody: z.ZodNullable<z.ZodString>;
        emailSubject: z.ZodNullable<z.ZodString>;
        template: z.<PERSON><PERSON><PERSON><["CUSTOM", "REMINDER", "RATING", "CANCELLED", "COMPLETED", "RESCHEDULED"]>;
        numberRequired: z.ZodNullable<z.ZodBoolean>;
        sender: z.<PERSON>odNullable<z.ZodString>;
        senderName: z.ZodNullable<z.ZodString>;
        includeCalendarEvent: z.ZodBoolean;
    }, "strip", z.ZodTypeAny, {
        id: number;
        template: "CANCELLED" | "REMINDER" | "CUSTOM" | "RESCHEDULED" | "COMPLETED" | "RATING";
        action: "EMAIL_HOST" | "EMAIL_ATTENDEE" | "SMS_ATTENDEE" | "SMS_NUMBER" | "EMAIL_ADDRESS" | "WHATSAPP_ATTENDEE" | "WHATSAPP_NUMBER" | "CAL_AI_PHONE_CALL";
        stepNumber: number;
        workflowId: number;
        sendTo: string | null;
        reminderBody: string | null;
        emailSubject: string | null;
        numberRequired: boolean | null;
        sender: string | null;
        includeCalendarEvent: boolean;
        senderName: string | null;
    }, {
        id: number;
        template: "CANCELLED" | "REMINDER" | "CUSTOM" | "RESCHEDULED" | "COMPLETED" | "RATING";
        action: "EMAIL_HOST" | "EMAIL_ATTENDEE" | "SMS_ATTENDEE" | "SMS_NUMBER" | "EMAIL_ADDRESS" | "WHATSAPP_ATTENDEE" | "WHATSAPP_NUMBER" | "CAL_AI_PHONE_CALL";
        stepNumber: number;
        workflowId: number;
        sendTo: string | null;
        reminderBody: string | null;
        emailSubject: string | null;
        numberRequired: boolean | null;
        sender: string | null;
        includeCalendarEvent: boolean;
        senderName: string | null;
    }>, "many">;
    trigger: z.ZodEnum<["BEFORE_EVENT", "EVENT_CANCELLED", "NEW_EVENT", "AFTER_EVENT", "RESCHEDULE_EVENT", "AFTER_HOSTS_CAL_VIDEO_NO_SHOW", "AFTER_GUESTS_CAL_VIDEO_NO_SHOW"]>;
    time: z.ZodNullable<z.ZodNumber>;
    timeUnit: z.ZodNullable<z.ZodEnum<["DAY", "HOUR", "MINUTE"]>>;
    isActiveOnAll: z.ZodOptional<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    name: string;
    id: number;
    time: number | null;
    steps: {
        id: number;
        template: "CANCELLED" | "REMINDER" | "CUSTOM" | "RESCHEDULED" | "COMPLETED" | "RATING";
        action: "EMAIL_HOST" | "EMAIL_ATTENDEE" | "SMS_ATTENDEE" | "SMS_NUMBER" | "EMAIL_ADDRESS" | "WHATSAPP_ATTENDEE" | "WHATSAPP_NUMBER" | "CAL_AI_PHONE_CALL";
        stepNumber: number;
        workflowId: number;
        sendTo: string | null;
        reminderBody: string | null;
        emailSubject: string | null;
        numberRequired: boolean | null;
        sender: string | null;
        includeCalendarEvent: boolean;
        senderName: string | null;
    }[];
    trigger: "AFTER_HOSTS_CAL_VIDEO_NO_SHOW" | "AFTER_GUESTS_CAL_VIDEO_NO_SHOW" | "BEFORE_EVENT" | "EVENT_CANCELLED" | "NEW_EVENT" | "AFTER_EVENT" | "RESCHEDULE_EVENT";
    timeUnit: "DAY" | "HOUR" | "MINUTE" | null;
    activeOn: number[];
    isActiveOnAll?: boolean | undefined;
}, {
    name: string;
    id: number;
    time: number | null;
    steps: {
        id: number;
        template: "CANCELLED" | "REMINDER" | "CUSTOM" | "RESCHEDULED" | "COMPLETED" | "RATING";
        action: "EMAIL_HOST" | "EMAIL_ATTENDEE" | "SMS_ATTENDEE" | "SMS_NUMBER" | "EMAIL_ADDRESS" | "WHATSAPP_ATTENDEE" | "WHATSAPP_NUMBER" | "CAL_AI_PHONE_CALL";
        stepNumber: number;
        workflowId: number;
        sendTo: string | null;
        reminderBody: string | null;
        emailSubject: string | null;
        numberRequired: boolean | null;
        sender: string | null;
        includeCalendarEvent: boolean;
        senderName: string | null;
    }[];
    trigger: "AFTER_HOSTS_CAL_VIDEO_NO_SHOW" | "AFTER_GUESTS_CAL_VIDEO_NO_SHOW" | "BEFORE_EVENT" | "EVENT_CANCELLED" | "NEW_EVENT" | "AFTER_EVENT" | "RESCHEDULE_EVENT";
    timeUnit: "DAY" | "HOUR" | "MINUTE" | null;
    activeOn: number[];
    isActiveOnAll?: boolean | undefined;
}>;
export type TUpdateInputSchema = z.infer<typeof ZUpdateInputSchema>;
