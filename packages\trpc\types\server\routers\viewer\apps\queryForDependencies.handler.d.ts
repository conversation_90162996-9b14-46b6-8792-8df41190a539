import type { TrpcSessionUser } from "../../../types";
import type { TQueryForDependenciesInputSchema } from "./queryForDependencies.schema";
type QueryForDependenciesOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TQueryForDependenciesInputSchema;
};
export declare const queryForDependenciesHandler: ({ ctx, input }: QueryForDependenciesOptions) => Promise<{
    name: string;
    slug: string;
    installed: boolean;
}[] | undefined>;
export {};
