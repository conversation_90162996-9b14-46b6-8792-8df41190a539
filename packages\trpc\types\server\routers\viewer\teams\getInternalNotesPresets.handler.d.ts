import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TGetInternalNotesPresetsInputSchema } from "./getInternalNotesPresets.schema";
type UpdateMembershipOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TGetInternalNotesPresetsInputSchema;
};
export declare const getInternalNotesPresetsHandler: ({ ctx, input }: UpdateMembershipOptions) => Promise<{
    name: string;
    id: number;
    cancellationReason: string | null;
}[]>;
export default getInternalNotesPresetsHandler;
