import { z } from "zod";
export declare const ZDownloadExpenseLogSchema: z.ZodObject<{
    teamId: z.ZodOptional<z.ZodNumber>;
    startDate: z.ZodString;
    endDate: z.ZodString;
}, "strip", z.ZodTypeAny, {
    startDate: string;
    endDate: string;
    teamId?: number | undefined;
}, {
    startDate: string;
    endDate: string;
    teamId?: number | undefined;
}>;
export type TDownloadExpenseLogSchema = z.infer<typeof ZDownloadExpenseLogSchema>;
