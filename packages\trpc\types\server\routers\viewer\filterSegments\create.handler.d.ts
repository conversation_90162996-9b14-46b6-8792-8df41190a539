import type { TCreateFilterSegmentInputSchema } from "@calcom/lib/server/repository/filterSegment.type";
import type { TrpcSessionUser } from "@calcom/trpc/server/types";
export declare const createFilterSegmentHandler: ({ ctx, input, }: {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TCreateFilterSegmentInputSchema;
}) => Promise<import("@calcom/features/data-table/lib/types").FilterSegmentOutput>;
