import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TGetMeetingInformationInputSchema } from "./getMeetingInformation.schema";
type GetMeetingInformationOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TGetMeetingInformationInputSchema;
};
export declare const getMeetingInformationHandler: ({ ctx: _ctx, input }: GetMeetingInformationOptions) => Promise<any>;
export {};
