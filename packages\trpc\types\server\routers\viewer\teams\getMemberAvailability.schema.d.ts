import { z } from "zod";
export declare const ZGetMemberAvailabilityInputSchema: z.ZodObject<{
    teamId: z.ZodNumber;
    memberId: z.ZodNumber;
    timezone: z.ZodEffects<z.ZodString, string, string>;
    dateFrom: z.ZodString;
    dateTo: z.ZodString;
}, "strip", z.Z<PERSON>ype<PERSON>ny, {
    teamId: number;
    timezone: string;
    memberId: number;
    dateFrom: string;
    dateTo: string;
}, {
    teamId: number;
    timezone: string;
    memberId: number;
    dateFrom: string;
    dateTo: string;
}>;
export type TGetMemberAvailabilityInputSchema = z.infer<typeof ZGetMemberAvailabilityInputSchema>;
