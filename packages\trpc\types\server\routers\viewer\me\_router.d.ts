export declare const meRouter: import("@trpc/server/unstable-core-do-not-import").CreateRouterInner<import("@trpc/server/unstable-core-do-not-import").RootConfig<{
    ctx: import("../../../createContext").InnerContext;
    meta: object;
    errorShape: {
        message: string;
        code: number;
        data: {
            code: string;
            httpStatus: number;
            path?: string;
            [key: string]: unknown;
        };
    };
    transformer: {
        stringify: (object: any) => string;
        parse: <T = unknown>(string: string) => T;
        serialize: (object: any) => import("superjson/dist/types").SuperJSONResult;
        deserialize: <T_1 = unknown>(payload: import("superjson/dist/types").SuperJSONResult) => T_1;
        registerClass: (v: import("superjson/dist/types").Class, options?: string | import("superjson/dist/class-registry").RegisterOptions | undefined) => void;
        registerSymbol: (v: Symbol, identifier?: string | undefined) => void;
        registerCustom: <I, O extends import("superjson/dist/types").JSONValue>(transformer: Omit<import("superjson/dist/custom-transformer-registry").CustomTransfomer<I, O>, "name">, name: string) => void;
        allowErrorProps: (...props: string[]) => void;
    };
}>, {
    bookingUnconfirmedCount: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: number;
    }>;
    deleteMe: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            password: string;
            totpCode?: string | undefined;
        };
        output: void;
    }>;
    deleteMeWithoutPassword: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: void;
        output: void;
    }>;
    get: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            includePasswordAdded?: boolean | undefined;
        } | undefined;
        output: {
            isTeamAdminOrOwner: boolean;
            passwordAdded?: boolean | undefined;
            secondaryEmails: {
                id: number;
                email: string;
                emailVerified: Date | null;
            }[];
            isPremium: boolean | undefined;
            organizationId: null;
            organization: {
                id: number;
                isPlatform: boolean;
                slug: string;
                isOrgAdmin: boolean;
            };
            username: string | null;
            profile: import("@calcom/types/UserProfile").UserAsPersonalProfile;
            profiles: never[];
            organizationSettings?: undefined;
            id: number;
            name: string | null;
            email: string;
            emailMd5: string;
            emailVerified: Date | null;
            startTime: number;
            endTime: number;
            bufferTime: number;
            locale: string;
            timeFormat: number | null;
            timeZone: string;
            avatar: string;
            avatarUrl: string | null;
            createdDate: Date;
            trialEndsAt: Date | null;
            defaultScheduleId: number | null;
            completedOnboarding: boolean;
            twoFactorEnabled: boolean;
            disableImpersonation: boolean;
            identityProvider: import(".prisma/client").$Enums.IdentityProvider;
            identityProviderEmail: string;
            brandColor: string | null;
            darkBrandColor: string | null;
            bio: string | null;
            weekStart: string;
            theme: string | null;
            appTheme: string | null;
            hideBranding: boolean;
            metadata: import(".prisma/client").Prisma.JsonValue;
            defaultBookerLayouts: {
                enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
                defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
            } | null;
            allowDynamicBooking: boolean | null;
            allowSEOIndexing: boolean | null;
            receiveMonthlyDigestEmail: boolean | null;
        } | {
            isTeamAdminOrOwner: boolean;
            passwordAdded?: boolean | undefined;
            secondaryEmails: {
                id: number;
                email: string;
                emailVerified: Date | null;
            }[];
            isPremium: boolean | undefined;
            organizationId: number | null;
            organization: {
                id: number | null;
                isOrgAdmin: boolean;
                metadata: {
                    defaultConferencingApp?: {
                        appSlug?: string | undefined;
                        appLink?: string | undefined;
                    } | undefined;
                    requestedSlug?: string | null | undefined;
                    paymentId?: string | undefined;
                    subscriptionId?: string | null | undefined;
                    subscriptionItemId?: string | null | undefined;
                    orgSeats?: number | null | undefined;
                    orgPricePerSeat?: number | null | undefined;
                    migratedToOrgFrom?: {
                        teamSlug?: string | null | undefined;
                        lastMigrationTime?: string | undefined;
                        reverted?: boolean | undefined;
                        lastRevertTime?: string | undefined;
                    } | undefined;
                    billingPeriod?: import("@calcom/prisma/zod-utils").BillingPeriod | undefined;
                } | null;
                requestedSlug: string | null;
                name?: string | undefined;
                organizationSettings?: {
                    allowSEOIndexing: boolean;
                    lockEventTypeCreationForUsers: boolean;
                } | null | undefined;
                hideBranding?: boolean | undefined;
                slug?: string | null | undefined;
                logoUrl?: string | null | undefined;
                isPrivate?: boolean | undefined;
                bannerUrl?: string | null | undefined;
                isPlatform?: boolean | undefined;
            };
            username: string | null;
            profile: import("@calcom/types/UserProfile").UserAsPersonalProfile | {
                name: string | null;
                avatarUrl: string | null;
                startTime: number;
                endTime: number;
                bufferTime: number;
                username: string | null;
                upId: string;
                id: null;
                organizationId: null;
                organization: null;
            } | {
                name: string | null;
                avatarUrl: string | null;
                startTime: number;
                endTime: number;
                bufferTime: number;
                user: {
                    name: string | null;
                    id: number;
                    locale: string | null;
                    startTime: number;
                    endTime: number;
                    email: string;
                    username: string | null;
                    avatarUrl: string | null;
                    bufferTime: number;
                    defaultScheduleId: number | null;
                    isPlatformManaged: boolean;
                };
                organization: {
                    name: string;
                    id: number;
                    metadata: import(".prisma/client").Prisma.JsonValue;
                    organizationSettings: {
                        allowSEOIndexing: boolean;
                        lockEventTypeCreationForUsers: boolean;
                    } | null;
                    hideBranding: boolean;
                    slug: string | null;
                    logoUrl: string | null;
                    isPrivate: boolean;
                    bannerUrl: string | null;
                    isPlatform: boolean;
                    members: {
                        id: number;
                        role: import(".prisma/client").$Enums.MembershipRole;
                        userId: number;
                        disableImpersonation: boolean;
                        teamId: number;
                        accepted: boolean;
                    }[];
                } & Omit<Pick<{
                    id: number;
                    name: string;
                    slug: string | null;
                    logoUrl: string | null;
                    calVideoLogo: string | null;
                    appLogo: string | null;
                    appIconLogo: string | null;
                    bio: string | null;
                    hideBranding: boolean;
                    hideTeamProfileLink: boolean;
                    isPrivate: boolean;
                    hideBookATeamMember: boolean;
                    createdAt: Date;
                    metadata: import(".prisma/client").Prisma.JsonValue | null;
                    theme: string | null;
                    rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
                    rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
                    brandColor: string | null;
                    darkBrandColor: string | null;
                    bannerUrl: string | null;
                    parentId: number | null;
                    timeFormat: number | null;
                    timeZone: string;
                    weekStart: string;
                    isOrganization: boolean;
                    pendingPayment: boolean;
                    isPlatform: boolean;
                    createdByOAuthClientId: string | null;
                    smsLockState: import(".prisma/client").$Enums.SMSLockState;
                    smsLockReviewedByAdmin: boolean;
                    bookingLimits: import(".prisma/client").Prisma.JsonValue | null;
                    includeManagedEventsInLimits: boolean;
                }, "name" | "id" | "metadata" | "hideBranding" | "slug" | "logoUrl" | "bannerUrl" | "isPlatform">, "metadata"> & {
                    requestedSlug: string | null;
                    metadata: {
                        requestedSlug: string | null;
                        defaultConferencingApp?: {
                            appSlug?: string | undefined;
                            appLink?: string | undefined;
                        } | undefined;
                        paymentId?: string | undefined;
                        subscriptionId?: string | null | undefined;
                        subscriptionItemId?: string | null | undefined;
                        orgSeats?: number | null | undefined;
                        orgPricePerSeat?: number | null | undefined;
                        migratedToOrgFrom?: {
                            teamSlug?: string | null | undefined;
                            lastMigrationTime?: string | undefined;
                            reverted?: boolean | undefined;
                            lastRevertTime?: string | undefined;
                        } | undefined;
                        billingPeriod?: import("@calcom/prisma/zod-utils").BillingPeriod | undefined;
                    };
                };
                movedFromUser: {
                    id: number;
                } | null;
                id: number;
                userId: number;
                uid: string;
                createdAt: Date & string;
                updatedAt: Date & string;
                username: string;
                organizationId: number;
                upId: string;
            } | ({
                name: string | null;
                avatarUrl: string | null;
                startTime: number;
                endTime: number;
                bufferTime: number;
                username: string | null;
                upId: string;
                id: null;
                organizationId: null;
                organization: null;
            } & import("@calcom/types/UserProfile").UserAsPersonalProfile) | (import("@calcom/types/UserProfile").UserAsPersonalProfile & {
                name: string | null;
                avatarUrl: string | null;
                startTime: number;
                endTime: number;
                bufferTime: number;
                username: string | null;
                upId: string;
                id: null;
                organizationId: null;
                organization: null;
            });
            profiles: import("@calcom/types/UserProfile").UserProfile[];
            organizationSettings: {
                allowSEOIndexing: boolean;
                lockEventTypeCreationForUsers: boolean;
            } | null | undefined;
            id: number;
            name: string | null;
            email: string;
            emailMd5: string;
            emailVerified: Date | null;
            startTime: number;
            endTime: number;
            bufferTime: number;
            locale: string;
            timeFormat: number | null;
            timeZone: string;
            avatar: string;
            avatarUrl: string | null;
            createdDate: Date;
            trialEndsAt: Date | null;
            defaultScheduleId: number | null;
            completedOnboarding: boolean;
            twoFactorEnabled: boolean;
            disableImpersonation: boolean;
            identityProvider: import(".prisma/client").$Enums.IdentityProvider;
            identityProviderEmail: string;
            brandColor: string | null;
            darkBrandColor: string | null;
            bio: string | null;
            weekStart: string;
            theme: string | null;
            appTheme: string | null;
            hideBranding: boolean;
            metadata: import(".prisma/client").Prisma.JsonValue;
            defaultBookerLayouts: {
                enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
                defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
            } | null;
            allowDynamicBooking: boolean | null;
            allowSEOIndexing: boolean | null;
            receiveMonthlyDigestEmail: boolean | null;
        };
    }>;
    getUserTopBanners: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: {
            teamUpgradeBanner: ({
                team: {
                    children: {
                        name: string;
                        id: number;
                        metadata: import(".prisma/client").Prisma.JsonValue;
                        createdAt: Date;
                        bio: string | null;
                        timeZone: string;
                        weekStart: string;
                        hideBranding: boolean;
                        theme: string | null;
                        timeFormat: number | null;
                        brandColor: string | null;
                        darkBrandColor: string | null;
                        smsLockState: import(".prisma/client").$Enums.SMSLockState;
                        smsLockReviewedByAdmin: boolean;
                        slug: string | null;
                        logoUrl: string | null;
                        calVideoLogo: string | null;
                        appLogo: string | null;
                        appIconLogo: string | null;
                        hideTeamProfileLink: boolean;
                        isPrivate: boolean;
                        hideBookATeamMember: boolean;
                        rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
                        rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
                        bannerUrl: string | null;
                        parentId: number | null;
                        isOrganization: boolean;
                        pendingPayment: boolean;
                        isPlatform: boolean;
                        createdByOAuthClientId: string | null;
                        bookingLimits: import(".prisma/client").Prisma.JsonValue;
                        includeManagedEventsInLimits: boolean;
                    }[];
                } & {
                    name: string;
                    id: number;
                    metadata: import(".prisma/client").Prisma.JsonValue;
                    createdAt: Date;
                    bio: string | null;
                    timeZone: string;
                    weekStart: string;
                    hideBranding: boolean;
                    theme: string | null;
                    timeFormat: number | null;
                    brandColor: string | null;
                    darkBrandColor: string | null;
                    smsLockState: import(".prisma/client").$Enums.SMSLockState;
                    smsLockReviewedByAdmin: boolean;
                    slug: string | null;
                    logoUrl: string | null;
                    calVideoLogo: string | null;
                    appLogo: string | null;
                    appIconLogo: string | null;
                    hideTeamProfileLink: boolean;
                    isPrivate: boolean;
                    hideBookATeamMember: boolean;
                    rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
                    rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
                    bannerUrl: string | null;
                    parentId: number | null;
                    isOrganization: boolean;
                    pendingPayment: boolean;
                    isPlatform: boolean;
                    createdByOAuthClientId: string | null;
                    bookingLimits: import(".prisma/client").Prisma.JsonValue;
                    includeManagedEventsInLimits: boolean;
                };
            } & {
                id: number;
                role: import(".prisma/client").$Enums.MembershipRole;
                userId: number;
                createdAt: Date | null;
                updatedAt: Date | null;
                disableImpersonation: boolean;
                teamId: number;
                accepted: boolean;
                customRoleId: string | null;
            })[];
            orgUpgradeBanner: ({
                team: {
                    name: string;
                    id: number;
                    metadata: import(".prisma/client").Prisma.JsonValue;
                    createdAt: Date;
                    bio: string | null;
                    timeZone: string;
                    weekStart: string;
                    hideBranding: boolean;
                    theme: string | null;
                    timeFormat: number | null;
                    brandColor: string | null;
                    darkBrandColor: string | null;
                    smsLockState: import(".prisma/client").$Enums.SMSLockState;
                    smsLockReviewedByAdmin: boolean;
                    slug: string | null;
                    logoUrl: string | null;
                    calVideoLogo: string | null;
                    appLogo: string | null;
                    appIconLogo: string | null;
                    hideTeamProfileLink: boolean;
                    isPrivate: boolean;
                    hideBookATeamMember: boolean;
                    rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
                    rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
                    bannerUrl: string | null;
                    parentId: number | null;
                    isOrganization: boolean;
                    pendingPayment: boolean;
                    isPlatform: boolean;
                    createdByOAuthClientId: string | null;
                    bookingLimits: import(".prisma/client").Prisma.JsonValue;
                    includeManagedEventsInLimits: boolean;
                };
            } & {
                id: number;
                role: import(".prisma/client").$Enums.MembershipRole;
                userId: number;
                createdAt: Date | null;
                updatedAt: Date | null;
                disableImpersonation: boolean;
                teamId: number;
                accepted: boolean;
                customRoleId: string | null;
            })[];
            verifyEmailBanner: boolean;
            calendarCredentialBanner: boolean;
            invalidAppCredentialBanners: import("@calcom/features/users/components/InvalidAppCredentialsBanner").InvalidAppCredentialBannerProps[];
        };
    }>;
    myStats: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: {
            id: number;
            sumOfBookings: number | undefined;
            sumOfCalendars: number | undefined;
            sumOfTeams: number | undefined;
            sumOfEventTypes: number | undefined;
            sumOfTeamEventTypes: number | undefined;
        };
    }>;
    platformMe: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: {
            id: number;
            username: string | null;
            email: string;
            timeFormat: number | null;
            timeZone: string;
            defaultScheduleId: number | null;
            weekStart: string;
            organizationId: number | null;
            organization: {
                isPlatform: any;
                id: number | null;
            };
        };
    }>;
    shouldVerifyEmail: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: {
            id: number;
            email: string;
            isVerified: boolean;
        };
    }>;
    updateProfile: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            username?: string | undefined;
            name?: string | undefined;
            email?: string | undefined;
            bio?: string | undefined;
            avatarUrl?: string | null | undefined;
            timeZone?: string | undefined;
            weekStart?: string | undefined;
            hideBranding?: boolean | undefined;
            allowDynamicBooking?: boolean | undefined;
            allowSEOIndexing?: boolean | undefined;
            receiveMonthlyDigestEmail?: boolean | undefined;
            brandColor?: string | undefined;
            darkBrandColor?: string | undefined;
            theme?: string | null | undefined;
            appTheme?: string | null | undefined;
            completedOnboarding?: boolean | undefined;
            locale?: string | undefined;
            timeFormat?: number | undefined;
            disableImpersonation?: boolean | undefined;
            metadata?: {
                proPaidForByTeamId?: number | undefined;
                stripeCustomerId?: string | undefined;
                vitalSettings?: {
                    connected?: boolean | undefined;
                    selectedParam?: string | undefined;
                    sleepValue?: number | undefined;
                } | undefined;
                isPremium?: boolean | undefined;
                sessionTimeout?: number | undefined;
                defaultConferencingApp?: {
                    appSlug?: string | undefined;
                    appLink?: string | undefined;
                } | undefined;
                defaultBookerLayouts?: {
                    enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
                    defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
                } | null | undefined;
                emailChangeWaitingForVerification?: string | undefined;
                migratedToOrgFrom?: {
                    username?: string | null | undefined;
                    lastMigrationTime?: string | undefined;
                    reverted?: boolean | undefined;
                    revertTime?: string | undefined;
                } | undefined;
            } | null | undefined;
            travelSchedules?: {
                startDate: Date;
                timeZone: string;
                id?: number | undefined;
                endDate?: Date | undefined;
            }[] | undefined;
            secondaryEmails?: {
                id: number;
                email: string;
                isDeleted?: boolean | undefined;
            }[] | undefined;
        };
        output: {
            email: string | undefined;
            avatarUrl: string | null;
            hasEmailBeenChanged: boolean | "" | undefined;
            sendEmailVerification: boolean;
            username?: string | undefined;
            name?: string | undefined;
            bio?: string | undefined;
            timeZone?: string | undefined;
            weekStart?: string | undefined;
            hideBranding?: boolean | undefined;
            allowDynamicBooking?: boolean | undefined;
            allowSEOIndexing?: boolean | undefined;
            receiveMonthlyDigestEmail?: boolean | undefined;
            brandColor?: string | undefined;
            darkBrandColor?: string | undefined;
            theme?: string | null | undefined;
            appTheme?: string | null | undefined;
            completedOnboarding?: boolean | undefined;
            locale?: string | undefined;
            timeFormat?: number | undefined;
            disableImpersonation?: boolean | undefined;
            metadata?: {
                proPaidForByTeamId?: number | undefined;
                stripeCustomerId?: string | undefined;
                vitalSettings?: {
                    connected?: boolean | undefined;
                    selectedParam?: string | undefined;
                    sleepValue?: number | undefined;
                } | undefined;
                isPremium?: boolean | undefined;
                sessionTimeout?: number | undefined;
                defaultConferencingApp?: {
                    appSlug?: string | undefined;
                    appLink?: string | undefined;
                } | undefined;
                defaultBookerLayouts?: {
                    enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
                    defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
                } | null | undefined;
                emailChangeWaitingForVerification?: string | undefined;
                migratedToOrgFrom?: {
                    username?: string | null | undefined;
                    lastMigrationTime?: string | undefined;
                    reverted?: boolean | undefined;
                    revertTime?: string | undefined;
                } | undefined;
            } | null | undefined;
            travelSchedules?: {
                startDate: Date;
                timeZone: string;
                id?: number | undefined;
                endDate?: Date | undefined;
            }[] | undefined;
            secondaryEmails?: {
                id: number;
                email: string;
                isDeleted: boolean;
            }[] | undefined;
        };
    }>;
}>;
