import type { TSetFilterSegmentPreferenceInputSchema } from "@calcom/lib/server/repository/filterSegment.type";
import type { TrpcSessionUser } from "@calcom/trpc/server/types";
export declare const setFilterSegmentPreferenceHandler: ({ ctx, input, }: {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TSetFilterSegmentPreferenceInputSchema;
}) => Promise<{
    id: number;
    segmentId: number | null;
    tableIdentifier: string;
    userId: number;
    createdAt: Date;
    updatedAt: Date;
    systemSegmentId: string | null;
} | null>;
