import type { TrpcSessionUser } from "../../../types";
import type { TCheckIfMembershipExistsInputSchema } from "./checkIfMembershipExists.schema";
type CheckIfMembershipExistsOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TCheckIfMembershipExistsInputSchema;
};
declare const checkIfMembershipExistsHandler: ({ ctx, input }: CheckIfMembershipExistsOptions) => Promise<boolean>;
export default checkIfMembershipExistsHandler;
