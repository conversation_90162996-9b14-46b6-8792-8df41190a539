import { z } from "zod";
export declare const ZUpdateUserInputSchema: z.ZodObject<{
    userId: z.ZodNumber;
    username: z.<PERSON>odOptional<z.ZodString>;
    bio: z.ZodOptional<z.ZodString>;
    name: z.<PERSON>ption<PERSON><z.ZodString>;
    email: z.<PERSON>ptional<z.ZodString>;
    avatar: z.<PERSON>al<z.ZodString>;
    role: z.<PERSON><PERSON><PERSON><[z.ZodEnum<["MEMBER", "ADMIN", "OWNER"]>, z.ZodString]>;
    timeZone: z.ZodEffects<z.ZodString, string, string>;
    attributeOptions: z.ZodOptional<z.ZodObject<{
        userId: z.ZodNumber;
        attributes: z.ZodArray<z.ZodObject<{
            id: z.ZodString;
            options: z.ZodOptional<z.ZodArray<z.ZodObject<{
                label: z.ZodOptional<z.ZodString>;
                value: z.ZodString;
                weight: z.ZodOptional<z.ZodNumber>;
            }, "strip", z.ZodTypeAny, {
                value: string;
                label?: string | undefined;
                weight?: number | undefined;
            }, {
                value: string;
                label?: string | undefined;
                weight?: number | undefined;
            }>, "many">>;
            value: z.ZodOptional<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            id: string;
            options?: {
                value: string;
                label?: string | undefined;
                weight?: number | undefined;
            }[] | undefined;
            value?: string | undefined;
        }, {
            id: string;
            options?: {
                value: string;
                label?: string | undefined;
                weight?: number | undefined;
            }[] | undefined;
            value?: string | undefined;
        }>, "many">;
    }, "strip", z.ZodTypeAny, {
        userId: number;
        attributes: {
            id: string;
            options?: {
                value: string;
                label?: string | undefined;
                weight?: number | undefined;
            }[] | undefined;
            value?: string | undefined;
        }[];
    }, {
        userId: number;
        attributes: {
            id: string;
            options?: {
                value: string;
                label?: string | undefined;
                weight?: number | undefined;
            }[] | undefined;
            value?: string | undefined;
        }[];
    }>>;
}, "strip", z.ZodTypeAny, {
    role: string;
    userId: number;
    timeZone: string;
    username?: string | undefined;
    bio?: string | undefined;
    name?: string | undefined;
    email?: string | undefined;
    avatar?: string | undefined;
    attributeOptions?: {
        userId: number;
        attributes: {
            id: string;
            options?: {
                value: string;
                label?: string | undefined;
                weight?: number | undefined;
            }[] | undefined;
            value?: string | undefined;
        }[];
    } | undefined;
}, {
    role: string;
    userId: number;
    timeZone: string;
    username?: string | undefined;
    bio?: string | undefined;
    name?: string | undefined;
    email?: string | undefined;
    avatar?: string | undefined;
    attributeOptions?: {
        userId: number;
        attributes: {
            id: string;
            options?: {
                value: string;
                label?: string | undefined;
                weight?: number | undefined;
            }[] | undefined;
            value?: string | undefined;
        }[];
    } | undefined;
}>;
export type TUpdateUserInputSchema = z.infer<typeof ZUpdateUserInputSchema>;
