import { z } from "zod";
export declare const ZListMembersInputSchema: z.ZodObject<{
    limit: z.ZodNumber;
    offset: z.ZodNumber;
    searchTerm: z.ZodOptional<z.ZodString>;
    expand: z.<PERSON>odOptional<z.<PERSON>od<PERSON>rray<z.ZodEnum<["attributes"]>, "many">>;
    filters: z.ZodOptional<z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        value: z.ZodUnion<[z.ZodObject<{
            type: z.ZodLiteral<import("@calcom/features/data-table/lib/types").ColumnFilterType.SINGLE_SELECT>;
            data: z.ZodUnion<[z.ZodString, z.ZodNumber]>;
        }, "strip", z.ZodTypeAny, {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.SINGLE_SELECT;
            data: string | number;
        }, {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.SINGLE_SELECT;
            data: string | number;
        }>, z.ZodObject<{
            type: z.ZodLiteral<import("@calcom/features/data-table/lib/types").ColumnFilterType.MULTI_SELECT>;
            data: z.ZodArray<z.ZodUnion<[z.ZodString, z.ZodNumber]>, "many">;
        }, "strip", z.ZodTypeAny, {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.MULTI_SELECT;
            data: (string | number)[];
        }, {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.MULTI_SELECT;
            data: (string | number)[];
        }>, z.ZodObject<{
            type: z.ZodLiteral<import("@calcom/features/data-table/lib/types").ColumnFilterType.TEXT>;
            data: z.ZodObject<{
                operator: z.ZodEnum<["equals", "notEquals", "contains", "notContains", "startsWith", "endsWith", "isEmpty", "isNotEmpty"]>;
                operand: z.ZodString;
            }, "strip", z.ZodTypeAny, {
                operator: "endsWith" | "startsWith" | "equals" | "notEquals" | "contains" | "notContains" | "isEmpty" | "isNotEmpty";
                operand: string;
            }, {
                operator: "endsWith" | "startsWith" | "equals" | "notEquals" | "contains" | "notContains" | "isEmpty" | "isNotEmpty";
                operand: string;
            }>;
        }, "strip", z.ZodTypeAny, {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.TEXT;
            data: {
                operator: "endsWith" | "startsWith" | "equals" | "notEquals" | "contains" | "notContains" | "isEmpty" | "isNotEmpty";
                operand: string;
            };
        }, {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.TEXT;
            data: {
                operator: "endsWith" | "startsWith" | "equals" | "notEquals" | "contains" | "notContains" | "isEmpty" | "isNotEmpty";
                operand: string;
            };
        }>, z.ZodObject<{
            type: z.ZodLiteral<import("@calcom/features/data-table/lib/types").ColumnFilterType.NUMBER>;
            data: z.ZodObject<{
                operator: z.ZodEnum<["eq", "neq", "gt", "gte", "lt", "lte"]>;
                operand: z.ZodNumber;
            }, "strip", z.ZodTypeAny, {
                operator: "eq" | "neq" | "gt" | "gte" | "lt" | "lte";
                operand: number;
            }, {
                operator: "eq" | "neq" | "gt" | "gte" | "lt" | "lte";
                operand: number;
            }>;
        }, "strip", z.ZodTypeAny, {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.NUMBER;
            data: {
                operator: "eq" | "neq" | "gt" | "gte" | "lt" | "lte";
                operand: number;
            };
        }, {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.NUMBER;
            data: {
                operator: "eq" | "neq" | "gt" | "gte" | "lt" | "lte";
                operand: number;
            };
        }>, z.ZodObject<{
            type: z.ZodLiteral<import("@calcom/features/data-table/lib/types").ColumnFilterType.DATE_RANGE>;
            data: z.ZodObject<{
                startDate: z.ZodNullable<z.ZodString>;
                endDate: z.ZodNullable<z.ZodString>;
                preset: z.ZodString;
            }, "strip", z.ZodTypeAny, {
                startDate: string | null;
                endDate: string | null;
                preset: string;
            }, {
                startDate: string | null;
                endDate: string | null;
                preset: string;
            }>;
        }, "strip", z.ZodTypeAny, {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.DATE_RANGE;
            data: {
                startDate: string | null;
                endDate: string | null;
                preset: string;
            };
        }, {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.DATE_RANGE;
            data: {
                startDate: string | null;
                endDate: string | null;
                preset: string;
            };
        }>]>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        value: {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.SINGLE_SELECT;
            data: string | number;
        } | {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.MULTI_SELECT;
            data: (string | number)[];
        } | {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.TEXT;
            data: {
                operator: "endsWith" | "startsWith" | "equals" | "notEquals" | "contains" | "notContains" | "isEmpty" | "isNotEmpty";
                operand: string;
            };
        } | {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.NUMBER;
            data: {
                operator: "eq" | "neq" | "gt" | "gte" | "lt" | "lte";
                operand: number;
            };
        } | {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.DATE_RANGE;
            data: {
                startDate: string | null;
                endDate: string | null;
                preset: string;
            };
        };
    }, {
        id: string;
        value: {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.SINGLE_SELECT;
            data: string | number;
        } | {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.MULTI_SELECT;
            data: (string | number)[];
        } | {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.TEXT;
            data: {
                operator: "endsWith" | "startsWith" | "equals" | "notEquals" | "contains" | "notContains" | "isEmpty" | "isNotEmpty";
                operand: string;
            };
        } | {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.NUMBER;
            data: {
                operator: "eq" | "neq" | "gt" | "gte" | "lt" | "lte";
                operand: number;
            };
        } | {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.DATE_RANGE;
            data: {
                startDate: string | null;
                endDate: string | null;
                preset: string;
            };
        };
    }>, "many">>;
    oAuthClientId: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    offset: number;
    limit: number;
    searchTerm?: string | undefined;
    expand?: "attributes"[] | undefined;
    filters?: {
        id: string;
        value: {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.SINGLE_SELECT;
            data: string | number;
        } | {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.MULTI_SELECT;
            data: (string | number)[];
        } | {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.TEXT;
            data: {
                operator: "endsWith" | "startsWith" | "equals" | "notEquals" | "contains" | "notContains" | "isEmpty" | "isNotEmpty";
                operand: string;
            };
        } | {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.NUMBER;
            data: {
                operator: "eq" | "neq" | "gt" | "gte" | "lt" | "lte";
                operand: number;
            };
        } | {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.DATE_RANGE;
            data: {
                startDate: string | null;
                endDate: string | null;
                preset: string;
            };
        };
    }[] | undefined;
    oAuthClientId?: string | undefined;
}, {
    offset: number;
    limit: number;
    searchTerm?: string | undefined;
    expand?: "attributes"[] | undefined;
    filters?: {
        id: string;
        value: {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.SINGLE_SELECT;
            data: string | number;
        } | {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.MULTI_SELECT;
            data: (string | number)[];
        } | {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.TEXT;
            data: {
                operator: "endsWith" | "startsWith" | "equals" | "notEquals" | "contains" | "notContains" | "isEmpty" | "isNotEmpty";
                operand: string;
            };
        } | {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.NUMBER;
            data: {
                operator: "eq" | "neq" | "gt" | "gte" | "lt" | "lte";
                operand: number;
            };
        } | {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.DATE_RANGE;
            data: {
                startDate: string | null;
                endDate: string | null;
                preset: string;
            };
        };
    }[] | undefined;
    oAuthClientId?: string | undefined;
}>;
export type TListMembersSchema = z.infer<typeof ZListMembersInputSchema>;
