import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { THasActiveTeamPlanInputSchema } from "./hasActiveTeamPlan.schema";
type HasActiveTeamPlanOptions = {
    ctx: {
        user: Pick<NonNullable<TrpcSessionUser>, "id">;
    };
    input: THasActiveTeamPlanInputSchema;
};
export declare const hasActiveTeamPlanHandler: ({ ctx, input }: HasActiveTeamPlanOptions) => Promise<{
    isActive: boolean;
    isTrial: boolean;
}>;
export default hasActiveTeamPlanHandler;
