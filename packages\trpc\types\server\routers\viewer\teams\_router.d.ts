export declare const viewerTeamsRouter: import("@trpc/server/unstable-core-do-not-import").CreateRouterInner<import("@trpc/server/unstable-core-do-not-import").RootConfig<{
    ctx: import("../../../createContext").InnerContext;
    meta: object;
    errorShape: {
        message: string;
        code: number;
        data: {
            code: string;
            httpStatus: number;
            path?: string;
            [key: string]: unknown;
        };
    };
    transformer: {
        stringify: (object: any) => string;
        parse: <T = unknown>(string: string) => T;
        serialize: (object: any) => import("superjson/dist/types").SuperJSONResult;
        deserialize: <T_1 = unknown>(payload: import("superjson/dist/types").SuperJSONResult) => T_1;
        registerClass: (v: import("superjson/dist/types").Class, options?: string | import("superjson/dist/class-registry").RegisterOptions | undefined) => void;
        registerSymbol: (v: Symbol, identifier?: string | undefined) => void;
        registerCustom: <I, O extends import("superjson/dist/types").JSONValue>(transformer: Omit<import("superjson/dist/custom-transformer-registry").CustomTransfomer<I, O>, "name">, name: string) => void;
        allowErrorProps: (...props: string[]) => void;
    };
}>, {
    get: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            teamId: number;
            isOrg?: boolean | undefined;
        };
        output: {
            membership: {
                role: import(".prisma/client").$Enums.MembershipRole;
                accepted: boolean;
            };
            inviteToken: {
                identifier: string;
                expires: Date;
                token: string;
                expiresInDays: number | null;
            } | undefined;
            metadata: {
                defaultConferencingApp?: {
                    appSlug?: string | undefined;
                    appLink?: string | undefined;
                } | undefined;
                requestedSlug?: string | null | undefined;
                orgSeats?: number | null | undefined;
                orgPricePerSeat?: number | null | undefined;
                migratedToOrgFrom?: {
                    teamSlug?: string | null | undefined;
                    lastMigrationTime?: string | undefined;
                    reverted?: boolean | undefined;
                    lastRevertTime?: string | undefined;
                } | undefined;
                billingPeriod?: import("@calcom/prisma/zod-utils").BillingPeriod | undefined;
            };
            bookingLimits: Partial<Record<"PER_DAY" | "PER_MONTH" | "PER_WEEK" | "PER_YEAR", number | undefined>> | null;
            logo?: string | undefined;
            name: string;
            id: number;
            children: {
                name: string;
                slug: string | null;
            }[];
            bio: string | null;
            hideBranding: boolean;
            theme: string | null;
            brandColor: string | null;
            darkBrandColor: string | null;
            slug: string | null;
            logoUrl: string | null;
            hideTeamProfileLink: boolean;
            isPrivate: boolean;
            hideBookATeamMember: boolean;
            rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
            rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
            parentId: number | null;
            includeManagedEventsInLimits: boolean;
            parent: {
                name: string;
                id: number;
                metadata: import(".prisma/client").Prisma.JsonValue;
                slug: string | null;
                logoUrl: string | null;
                isPrivate: boolean;
                isOrganization: boolean;
            } | null;
            isOrganization: boolean;
        };
    }>;
    list: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            includeOrgs?: boolean | undefined;
        } | undefined;
        output: {
            metadata: {
                defaultConferencingApp?: {
                    appSlug?: string | undefined;
                    appLink?: string | undefined;
                } | undefined;
                requestedSlug?: string | null | undefined;
                paymentId?: string | undefined;
                subscriptionId?: string | null | undefined;
                subscriptionItemId?: string | null | undefined;
                orgSeats?: number | null | undefined;
                orgPricePerSeat?: number | null | undefined;
                migratedToOrgFrom?: {
                    teamSlug?: string | null | undefined;
                    lastMigrationTime?: string | undefined;
                    reverted?: boolean | undefined;
                    lastRevertTime?: string | undefined;
                } | undefined;
                billingPeriod?: import("@calcom/prisma/zod-utils").BillingPeriod | undefined;
            } | null;
            inviteToken: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                teamId: number | null;
                secondaryEmailId: number | null;
                identifier: string;
                expires: Date;
                token: string;
                expiresInDays: number | null;
            } | undefined;
            name: string;
            id: number;
            slug: string | null;
            logoUrl: string | null;
            parentId: number | null;
            isOrganization: boolean;
            parent: {
                name: string;
                id: number;
                metadata: import(".prisma/client").Prisma.JsonValue;
                createdAt: Date;
                bio: string | null;
                timeZone: string;
                weekStart: string;
                hideBranding: boolean;
                theme: string | null;
                timeFormat: number | null;
                brandColor: string | null;
                darkBrandColor: string | null;
                smsLockState: import(".prisma/client").$Enums.SMSLockState;
                smsLockReviewedByAdmin: boolean;
                slug: string | null;
                logoUrl: string | null;
                calVideoLogo: string | null;
                appLogo: string | null;
                appIconLogo: string | null;
                hideTeamProfileLink: boolean;
                isPrivate: boolean;
                hideBookATeamMember: boolean;
                rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
                rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
                bannerUrl: string | null;
                parentId: number | null;
                isOrganization: boolean;
                pendingPayment: boolean;
                isPlatform: boolean;
                createdByOAuthClientId: string | null;
                bookingLimits: import(".prisma/client").Prisma.JsonValue;
                includeManagedEventsInLimits: boolean;
            } | null;
            role: import(".prisma/client").$Enums.MembershipRole;
            accepted: boolean;
        }[];
    }>;
    listOwnedTeams: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: {
            metadata: {
                defaultConferencingApp?: {
                    appSlug?: string | undefined;
                    appLink?: string | undefined;
                } | undefined;
                requestedSlug?: string | null | undefined;
                paymentId?: string | undefined;
                subscriptionId?: string | null | undefined;
                subscriptionItemId?: string | null | undefined;
                orgSeats?: number | null | undefined;
                orgPricePerSeat?: number | null | undefined;
                migratedToOrgFrom?: {
                    teamSlug?: string | null | undefined;
                    lastMigrationTime?: string | undefined;
                    reverted?: boolean | undefined;
                    lastRevertTime?: string | undefined;
                } | undefined;
                billingPeriod?: import("@calcom/prisma/zod-utils").BillingPeriod | undefined;
            } | null;
            inviteToken: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                teamId: number | null;
                secondaryEmailId: number | null;
                identifier: string;
                expires: Date;
                token: string;
                expiresInDays: number | null;
            } | undefined;
            name: string;
            id: number;
            slug: string | null;
            logoUrl: string | null;
            parentId: number | null;
            isOrganization: boolean;
            parent: {
                name: string;
                id: number;
                metadata: import(".prisma/client").Prisma.JsonValue;
                createdAt: Date;
                bio: string | null;
                timeZone: string;
                weekStart: string;
                hideBranding: boolean;
                theme: string | null;
                timeFormat: number | null;
                brandColor: string | null;
                darkBrandColor: string | null;
                smsLockState: import(".prisma/client").$Enums.SMSLockState;
                smsLockReviewedByAdmin: boolean;
                slug: string | null;
                logoUrl: string | null;
                calVideoLogo: string | null;
                appLogo: string | null;
                appIconLogo: string | null;
                hideTeamProfileLink: boolean;
                isPrivate: boolean;
                hideBookATeamMember: boolean;
                rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
                rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
                bannerUrl: string | null;
                parentId: number | null;
                isOrganization: boolean;
                pendingPayment: boolean;
                isPlatform: boolean;
                createdByOAuthClientId: string | null;
                bookingLimits: import(".prisma/client").Prisma.JsonValue;
                includeManagedEventsInLimits: boolean;
            } | null;
            role: import(".prisma/client").$Enums.MembershipRole;
            accepted: boolean;
        }[];
    }>;
    create: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            name: string;
            slug: string;
            logo?: string | null | undefined;
        };
        output: {
            url: string;
            message: string;
            team: null;
        } | {
            url: string;
            message: string;
            team: {
                name: string;
                id: number;
                metadata: import(".prisma/client").Prisma.JsonValue;
                createdAt: Date;
                bio: string | null;
                timeZone: string;
                weekStart: string;
                hideBranding: boolean;
                theme: string | null;
                timeFormat: number | null;
                brandColor: string | null;
                darkBrandColor: string | null;
                smsLockState: import(".prisma/client").$Enums.SMSLockState;
                smsLockReviewedByAdmin: boolean;
                slug: string | null;
                logoUrl: string | null;
                calVideoLogo: string | null;
                appLogo: string | null;
                appIconLogo: string | null;
                hideTeamProfileLink: boolean;
                isPrivate: boolean;
                hideBookATeamMember: boolean;
                rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
                rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
                bannerUrl: string | null;
                parentId: number | null;
                isOrganization: boolean;
                pendingPayment: boolean;
                isPlatform: boolean;
                createdByOAuthClientId: string | null;
                bookingLimits: import(".prisma/client").Prisma.JsonValue;
                includeManagedEventsInLimits: boolean;
            };
        };
    }>;
    update: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: import("./update.schema").TUpdateInputSchema;
        output: {
            logoUrl: string | null;
            name: string;
            bio: string | null;
            slug: string | null;
            theme: string | null;
            brandColor: string | null;
            darkBrandColor: string | null;
            bookingLimits: import("@calcom/types/Calendar").IntervalLimit;
            includeManagedEventsInLimits: boolean;
            rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
            rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
        } | undefined;
    }>;
    delete: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            teamId: number;
        };
        output: {
            name: string;
            id: number;
            metadata: import(".prisma/client").Prisma.JsonValue;
            createdAt: Date;
            bio: string | null;
            timeZone: string;
            weekStart: string;
            hideBranding: boolean;
            theme: string | null;
            timeFormat: number | null;
            brandColor: string | null;
            darkBrandColor: string | null;
            smsLockState: import(".prisma/client").$Enums.SMSLockState;
            smsLockReviewedByAdmin: boolean;
            slug: string | null;
            logoUrl: string | null;
            calVideoLogo: string | null;
            appLogo: string | null;
            appIconLogo: string | null;
            hideTeamProfileLink: boolean;
            isPrivate: boolean;
            hideBookATeamMember: boolean;
            rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
            rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
            bannerUrl: string | null;
            parentId: number | null;
            isOrganization: boolean;
            pendingPayment: boolean;
            isPlatform: boolean;
            createdByOAuthClientId: string | null;
            bookingLimits: import(".prisma/client").Prisma.JsonValue;
            includeManagedEventsInLimits: boolean;
        };
    }>;
    removeMember: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            teamIds: number[];
            memberIds: number[];
            isOrg?: boolean | undefined;
        };
        output: void;
    }>;
    inviteMember: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            creationSource: "API_V1" | "API_V2" | "WEBAPP";
            teamId: number;
            language: string;
            usernameOrEmail: (string | (string | {
                role: "ADMIN" | "MEMBER" | "OWNER";
                email: string;
            })[]) & (string | (string | {
                role: "ADMIN" | "MEMBER" | "OWNER";
                email: string;
            })[] | undefined);
            role?: "ADMIN" | "MEMBER" | "OWNER" | undefined;
            isPlatform?: boolean | undefined;
        };
        output: {
            usernameOrEmail: string | string[];
            numUsersInvited: number;
        };
    }>;
    acceptOrLeave: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            teamId: number;
            accept: boolean;
        };
        output: void;
    }>;
    changeMemberRole: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            role: string;
            teamId: number;
            memberId: number;
        };
        output: ({
            user: {
                name: string | null;
                id: number;
                role: import(".prisma/client").$Enums.UserPermissionRole;
                metadata: import(".prisma/client").Prisma.JsonValue;
                locale: string | null;
                startTime: number;
                endTime: number;
                creationSource: import(".prisma/client").$Enums.CreationSource | null;
                email: string;
                movedToProfileId: number | null;
                username: string | null;
                emailVerified: Date | null;
                bio: string | null;
                avatarUrl: string | null;
                timeZone: string;
                weekStart: string;
                bufferTime: number;
                hideBranding: boolean;
                theme: string | null;
                appTheme: string | null;
                createdDate: Date;
                trialEndsAt: Date | null;
                lastActiveAt: Date | null;
                defaultScheduleId: number | null;
                completedOnboarding: boolean;
                timeFormat: number | null;
                twoFactorSecret: string | null;
                twoFactorEnabled: boolean;
                backupCodes: string | null;
                identityProvider: import(".prisma/client").$Enums.IdentityProvider;
                identityProviderId: string | null;
                invitedTo: number | null;
                brandColor: string | null;
                darkBrandColor: string | null;
                allowDynamicBooking: boolean | null;
                allowSEOIndexing: boolean | null;
                receiveMonthlyDigestEmail: boolean | null;
                verified: boolean | null;
                disableImpersonation: boolean;
                organizationId: number | null;
                locked: boolean;
                isPlatformManaged: boolean;
                smsLockState: import(".prisma/client").$Enums.SMSLockState;
                smsLockReviewedByAdmin: boolean;
                referralLinkId: string | null;
                whitelistWorkflows: boolean;
            };
            team: {
                name: string;
                id: number;
                metadata: import(".prisma/client").Prisma.JsonValue;
                createdAt: Date;
                bio: string | null;
                timeZone: string;
                weekStart: string;
                hideBranding: boolean;
                theme: string | null;
                timeFormat: number | null;
                brandColor: string | null;
                darkBrandColor: string | null;
                smsLockState: import(".prisma/client").$Enums.SMSLockState;
                smsLockReviewedByAdmin: boolean;
                slug: string | null;
                logoUrl: string | null;
                calVideoLogo: string | null;
                appLogo: string | null;
                appIconLogo: string | null;
                hideTeamProfileLink: boolean;
                isPrivate: boolean;
                hideBookATeamMember: boolean;
                rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
                rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
                bannerUrl: string | null;
                parentId: number | null;
                isOrganization: boolean;
                pendingPayment: boolean;
                isPlatform: boolean;
                createdByOAuthClientId: string | null;
                bookingLimits: import(".prisma/client").Prisma.JsonValue;
                includeManagedEventsInLimits: boolean;
            };
        } & {
            id: number;
            role: import(".prisma/client").$Enums.MembershipRole;
            userId: number;
            createdAt: Date | null;
            updatedAt: Date | null;
            disableImpersonation: boolean;
            teamId: number;
            accepted: boolean;
            customRoleId: string | null;
        }) | null;
    }>;
    getMemberAvailability: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            teamId: number;
            timezone: string;
            memberId: number;
            dateFrom: string;
            dateTo: string;
        };
        output: {
            busy: import("@calcom/types/Calendar").EventBusyDetails[];
            timeZone: string;
            dateRanges: {
                start: import("dayjs").Dayjs;
                end: import("dayjs").Dayjs;
            }[];
            oooExcludedDateRanges: {
                start: import("dayjs").Dayjs;
                end: import("dayjs").Dayjs;
            }[];
            workingHours: import("@calcom/types/schedule").WorkingHours[];
            dateOverrides: import("@calcom/types/schedule").TimeRange[];
            currentSeats: {
                uid: string;
                startTime: Date;
                _count: {
                    attendees: number;
                };
            }[] | null;
            datesOutOfOffice: import("@calcom/lib/getUserAvailability").IOutOfOfficeData;
        } | {
            busy: never[];
            timeZone: string;
            dateRanges: never[];
            oooExcludedDateRanges: never[];
            workingHours: never[];
            dateOverrides: never[];
            currentSeats: never[];
            datesOutOfOffice: undefined;
        };
    }>;
    getMembershipbyUser: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            teamId: number;
            memberId: number;
        };
        output: {
            id: number;
            role: import(".prisma/client").$Enums.MembershipRole;
            userId: number;
            createdAt: Date | null;
            updatedAt: Date | null;
            disableImpersonation: boolean;
            teamId: number;
            accepted: boolean;
            customRoleId: string | null;
        } | null;
    }>;
    updateMembership: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            disableImpersonation: boolean;
            teamId: number;
            memberId: number;
        };
        output: {
            id: number;
            role: import(".prisma/client").$Enums.MembershipRole;
            userId: number;
            createdAt: Date | null;
            updatedAt: Date | null;
            disableImpersonation: boolean;
            teamId: number;
            accepted: boolean;
            customRoleId: string | null;
        };
    }>;
    publish: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            teamId: number;
        };
        output: {
            url: string;
            status: "REQUIRES_PAYMENT" | "REQUIRES_UPGRADE" | "SUCCESS";
            message?: undefined;
        } | {
            url: string;
            status?: undefined;
            message?: undefined;
        } | {
            url: string;
            message: string;
            status?: undefined;
        };
    }>;
    /** This is a temporal endpoint so we can progressively upgrade teams to the new billing system. */
    getUpgradeable: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: ({
            team: {
                children: {
                    name: string;
                    id: number;
                    metadata: import(".prisma/client").Prisma.JsonValue;
                    createdAt: Date;
                    bio: string | null;
                    timeZone: string;
                    weekStart: string;
                    hideBranding: boolean;
                    theme: string | null;
                    timeFormat: number | null;
                    brandColor: string | null;
                    darkBrandColor: string | null;
                    smsLockState: import(".prisma/client").$Enums.SMSLockState;
                    smsLockReviewedByAdmin: boolean;
                    slug: string | null;
                    logoUrl: string | null;
                    calVideoLogo: string | null;
                    appLogo: string | null;
                    appIconLogo: string | null;
                    hideTeamProfileLink: boolean;
                    isPrivate: boolean;
                    hideBookATeamMember: boolean;
                    rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
                    rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
                    bannerUrl: string | null;
                    parentId: number | null;
                    isOrganization: boolean;
                    pendingPayment: boolean;
                    isPlatform: boolean;
                    createdByOAuthClientId: string | null;
                    bookingLimits: import(".prisma/client").Prisma.JsonValue;
                    includeManagedEventsInLimits: boolean;
                }[];
            } & {
                name: string;
                id: number;
                metadata: import(".prisma/client").Prisma.JsonValue;
                createdAt: Date;
                bio: string | null;
                timeZone: string;
                weekStart: string;
                hideBranding: boolean;
                theme: string | null;
                timeFormat: number | null;
                brandColor: string | null;
                darkBrandColor: string | null;
                smsLockState: import(".prisma/client").$Enums.SMSLockState;
                smsLockReviewedByAdmin: boolean;
                slug: string | null;
                logoUrl: string | null;
                calVideoLogo: string | null;
                appLogo: string | null;
                appIconLogo: string | null;
                hideTeamProfileLink: boolean;
                isPrivate: boolean;
                hideBookATeamMember: boolean;
                rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
                rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
                bannerUrl: string | null;
                parentId: number | null;
                isOrganization: boolean;
                pendingPayment: boolean;
                isPlatform: boolean;
                createdByOAuthClientId: string | null;
                bookingLimits: import(".prisma/client").Prisma.JsonValue;
                includeManagedEventsInLimits: boolean;
            };
        } & {
            id: number;
            role: import(".prisma/client").$Enums.MembershipRole;
            userId: number;
            createdAt: Date | null;
            updatedAt: Date | null;
            disableImpersonation: boolean;
            teamId: number;
            accepted: boolean;
            customRoleId: string | null;
        })[];
    }>;
    listMembers: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            teamId: number;
            limit?: number | undefined;
            searchTerm?: string | undefined;
            cursor?: number | null | undefined;
        };
        output: {
            members: {
                username: string | null;
                role: import(".prisma/client").$Enums.MembershipRole;
                customRoleId: string | null;
                customRole: {
                    id: string;
                    name: string;
                } | null;
                profile: import("@calcom/types/UserProfile").UserProfile;
                organizationId: number | null;
                organization: any;
                accepted: boolean;
                disableImpersonation: boolean;
                bookerUrl: string;
                teamId: number;
                lastActiveAt: string | null;
                name: string | null;
                id: number;
                email: string;
                bio: string | null;
                avatarUrl: string | null;
                nonProfileUsername: string | null;
            }[];
            nextCursor: number | undefined;
            meta: {
                totalRowCount: number;
            };
        };
    }>;
    listSimpleMembers: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: {
            name: string | null;
            id: number;
            email: string;
            username: string | null;
            avatarUrl: string | null;
        }[];
    }>;
    legacyListMembers: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            cursor?: number | null | undefined;
            teamIds?: number[] | undefined;
            limit?: number | null | undefined;
            searchText?: string | undefined;
            adminOrOwnedTeamsOnly?: boolean | undefined;
        };
        output: {
            members: ({
                accepted: boolean;
                membershipId: number;
                name: string | null;
                id: number;
                email: string;
                username: string | null;
                avatarUrl: string | null;
            } & {
                nonProfileUsername: string | null;
                profile: import("@calcom/types/UserProfile").UserProfile;
            })[];
            nextCursor: number | undefined;
        };
    }>;
    getUserConnectedApps: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            teamId: number;
            userIds: number[];
        };
        output: Record<number, {
            name: string | null;
            logo: string | null;
            externalId: string | null;
            app: {
                slug: string;
                categories: import("@calcom/prisma/enums").AppCategories[];
            } | null;
        }[]>;
    }>;
    hasTeamPlan: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: {
            hasTeamPlan: boolean;
        };
    }>;
    listInvites: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: {
            id: number;
            role: import(".prisma/client").$Enums.MembershipRole;
            userId: number;
            createdAt: Date | null;
            updatedAt: Date | null;
            disableImpersonation: boolean;
            teamId: number;
            accepted: boolean;
            customRoleId: string | null;
        }[];
    }>;
    createInvite: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            teamId: number;
            token?: string | undefined;
        };
        output: {
            token: string;
            inviteLink: string;
        };
    }>;
    setInviteExpiration: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            token: string;
            expiresInDays?: number | undefined;
        };
        output: void;
    }>;
    deleteInvite: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            token: string;
        };
        output: void;
    }>;
    inviteMemberByToken: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            creationSource: "API_V1" | "API_V2" | "WEBAPP";
            token: string;
        };
        output: string;
    }>;
    hasEditPermissionForUser: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            memberId: number;
        };
        output: boolean;
    }>;
    resendInvitation: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            email: string;
            teamId: number;
            language: string;
            isOrg?: boolean | undefined;
        };
        output: {
            email: string;
            teamId: number;
            language: string;
            isOrg: boolean;
        };
    }>;
    roundRobinReassign: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            teamId: number;
            bookingId: number;
        };
        output: {
            bookingId: number;
            reassignedTo: {
                id: number;
                name: string | null;
                email: string;
            };
        };
    }>;
    roundRobinManualReassign: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            bookingId: number;
            teamMemberId: number;
            reassignReason?: string | undefined;
        };
        output: {
            success: boolean;
        };
    }>;
    getRoundRobinHostsToReassign: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            bookingId: number;
            exclude: "fixedHosts";
            cursor?: number | undefined;
            limit?: number | undefined;
            searchTerm?: string | undefined;
        };
        output: {
            items: {
                id: number;
                name: string | null;
                email: string;
                status: string;
            }[];
            nextCursor: number | null;
            totalCount: number;
        };
    }>;
    checkIfMembershipExists: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            value: string;
            teamId: number;
        };
        output: boolean;
    }>;
    addMembersToEventTypes: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            teamId: number;
            userIds: number[];
            eventTypeIds: number[];
        };
        output: import("@prisma/client/runtime/library").GetBatchResult;
    }>;
    removeHostsFromEventTypes: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            teamId: number;
            userIds: number[];
            eventTypeIds: number[];
        };
        output: import("@prisma/client/runtime/library").GetBatchResult;
    }>;
    getInternalNotesPresets: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            teamId: number;
        };
        output: {
            name: string;
            id: number;
            cancellationReason: string | null;
        }[];
    }>;
    updateInternalNotesPresets: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            teamId: number;
            presets: {
                name: string;
                id: number;
                cancellationReason?: string | undefined;
            }[];
        };
        output: {
            name: string;
            id: number;
            createdAt: Date;
            cancellationReason: string | null;
            teamId: number;
        }[];
    }>;
    hasActiveTeamPlan: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            ownerOnly?: boolean | undefined;
        } | undefined;
        output: {
            isActive: boolean;
            isTrial: boolean;
        };
    }>;
    skipTeamTrials: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {};
        output: {
            success: boolean;
            error?: undefined;
        } | {
            success: boolean;
            error: string;
        };
    }>;
}>;
