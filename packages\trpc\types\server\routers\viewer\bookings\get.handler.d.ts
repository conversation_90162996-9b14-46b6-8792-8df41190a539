import type { Prisma } from "@prisma/client";
import type { Kysely } from "kysely";
import type { DB } from "@calcom/kysely";
import type { PrismaClient } from "@calcom/prisma";
import { SchedulingType } from "@calcom/prisma/enums";
import { BookingStatus } from "@calcom/prisma/enums";
import type { TrpcSessionUser } from "../../../types";
import type { TGetInputSchema } from "./get.schema";
type GetOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
        prisma: PrismaClient;
    };
    input: TGetInputSchema;
};
type InputByStatus = "upcoming" | "recurring" | "past" | "cancelled" | "unconfirmed";
export declare const getHandler: ({ ctx, input }: GetOptions) => Promise<{
    bookings: {
        rescheduler: string | null;
        eventType: {
            recurringEvent: import("@calcom/types/Calendar").RecurringEvent | null;
            eventTypeColor: {
                lightEventTypeColor: string;
                darkEventTypeColor: string;
            } | null;
            price: number;
            currency: string;
            metadata: {
                config?: {
                    useHostSchedulesForTeamEvent?: boolean | undefined;
                } | undefined;
                smartContractAddress?: string | undefined;
                blockchainId?: number | undefined;
                multipleDuration?: number[] | undefined;
                giphyThankYouPage?: string | undefined;
                additionalNotesRequired?: boolean | undefined;
                disableSuccessPage?: boolean | undefined;
                disableStandardEmails?: {
                    all?: {
                        host?: boolean | undefined;
                        attendee?: boolean | undefined;
                    } | undefined;
                    confirmation?: {
                        host?: boolean | undefined;
                        attendee?: boolean | undefined;
                    } | undefined;
                } | undefined;
                managedEventConfig?: {
                    unlockedFields?: {
                        users?: true | undefined;
                        children?: true | undefined;
                        length?: true | undefined;
                        title?: true | undefined;
                        metadata?: true | undefined;
                        description?: true | undefined;
                        userId?: true | undefined;
                        calVideoSettings?: true | undefined;
                        destinationCalendar?: true | undefined;
                        profile?: true | undefined;
                        team?: true | undefined;
                        schedule?: true | undefined;
                        availability?: true | undefined;
                        hashedLink?: true | undefined;
                        secondaryEmail?: true | undefined;
                        customInputs?: true | undefined;
                        timeZone?: true | undefined;
                        bookings?: true | undefined;
                        selectedCalendars?: true | undefined;
                        webhooks?: true | undefined;
                        workflows?: true | undefined;
                        hosts?: true | undefined;
                        slug?: true | undefined;
                        parentId?: true | undefined;
                        bookingLimits?: true | undefined;
                        parent?: true | undefined;
                        teamId?: true | undefined;
                        hidden?: true | undefined;
                        _count?: true | undefined;
                        interfaceLanguage?: true | undefined;
                        position?: true | undefined;
                        locations?: true | undefined;
                        offsetStart?: true | undefined;
                        profileId?: true | undefined;
                        useEventLevelSelectedCalendars?: true | undefined;
                        eventName?: true | undefined;
                        bookingFields?: true | undefined;
                        periodType?: true | undefined;
                        periodStartDate?: true | undefined;
                        periodEndDate?: true | undefined;
                        periodDays?: true | undefined;
                        periodCountCalendarDays?: true | undefined;
                        lockTimeZoneToggleOnBookingPage?: true | undefined;
                        lockedTimeZone?: true | undefined;
                        requiresConfirmation?: true | undefined;
                        requiresConfirmationWillBlockSlot?: true | undefined;
                        requiresConfirmationForFreeEmail?: true | undefined;
                        requiresBookerEmailVerification?: true | undefined;
                        canSendCalVideoTranscriptionEmails?: true | undefined;
                        autoTranslateDescriptionEnabled?: true | undefined;
                        recurringEvent?: true | undefined;
                        disableGuests?: true | undefined;
                        hideCalendarNotes?: true | undefined;
                        hideCalendarEventDetails?: true | undefined;
                        minimumBookingNotice?: true | undefined;
                        beforeEventBuffer?: true | undefined;
                        afterEventBuffer?: true | undefined;
                        seatsPerTimeSlot?: true | undefined;
                        onlyShowFirstAvailableSlot?: true | undefined;
                        disableCancelling?: true | undefined;
                        disableRescheduling?: true | undefined;
                        seatsShowAttendees?: true | undefined;
                        seatsShowAvailabilityCount?: true | undefined;
                        schedulingType?: true | undefined;
                        scheduleId?: true | undefined;
                        allowReschedulingCancelledBookings?: true | undefined;
                        price?: true | undefined;
                        currency?: true | undefined;
                        slotInterval?: true | undefined;
                        successRedirectUrl?: true | undefined;
                        forwardParamsSuccessRedirect?: true | undefined;
                        durationLimits?: true | undefined;
                        isInstantEvent?: true | undefined;
                        instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                        instantMeetingScheduleId?: true | undefined;
                        instantMeetingParameters?: true | undefined;
                        assignAllTeamMembers?: true | undefined;
                        assignRRMembersUsingSegment?: true | undefined;
                        rrSegmentQueryValue?: true | undefined;
                        useEventTypeDestinationCalendarEmail?: true | undefined;
                        isRRWeightsEnabled?: true | undefined;
                        maxLeadThreshold?: true | undefined;
                        includeNoShowInRRCalculation?: true | undefined;
                        allowReschedulingPastBookings?: true | undefined;
                        hideOrganizerEmail?: true | undefined;
                        maxActiveBookingsPerBooker?: true | undefined;
                        maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                        customReplyToEmail?: true | undefined;
                        eventTypeColor?: true | undefined;
                        rescheduleWithSameRoundRobinHost?: true | undefined;
                        secondaryEmailId?: true | undefined;
                        useBookerTimezone?: true | undefined;
                        restrictionScheduleId?: true | undefined;
                        bookingRequiresAuthentication?: true | undefined;
                        owner?: true | undefined;
                        instantMeetingSchedule?: true | undefined;
                        aiPhoneCallConfig?: true | undefined;
                        fieldTranslations?: true | undefined;
                        restrictionSchedule?: true | undefined;
                        hostGroups?: true | undefined;
                    } | undefined;
                } | undefined;
                requiresConfirmationThreshold?: {
                    time: number;
                    unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
                } | undefined;
                bookerLayouts?: {
                    enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
                    defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
                } | null | undefined;
                apps?: unknown;
            } | null;
            id?: number | undefined;
            length?: number | undefined;
            title?: string | undefined;
            slug?: string | undefined;
            eventName?: string | null | undefined;
            disableGuests?: boolean | undefined;
            disableCancelling?: boolean | null | undefined;
            disableRescheduling?: boolean | null | undefined;
            seatsShowAttendees?: boolean | null | undefined;
            seatsShowAvailabilityCount?: boolean | null | undefined;
            allowReschedulingPastBookings?: boolean | undefined;
            hideOrganizerEmail?: boolean | undefined;
            customReplyToEmail?: string | null | undefined;
            schedulingType?: SchedulingType | null | undefined;
            hosts?: {
                userId: number;
                user: {
                    id: number;
                    email: string;
                } | null;
            }[] | undefined;
            team?: {
                id: number;
                name: string;
                slug: string | null;
            } | null | undefined;
            hostGroups?: {
                id: string;
                name: string;
            }[] | undefined;
        };
        startTime: string;
        endTime: string;
        id: number;
        title: string;
        metadata: unknown;
        description: string | null;
        uid: string;
        userPrimaryEmail: string | null;
        customInputs: unknown;
        location: string | null;
        createdAt: Date;
        updatedAt: Date | null;
        paid: boolean;
        rescheduled: boolean | null;
        fromReschedule: string | null;
        recurringEventId: string | null;
        isRecorded: boolean;
        responses: Prisma.JsonValue;
        status: BookingStatus;
        routedFromRoutingFormReponse: {
            id: number;
        } | null;
        references: {
            id: number;
            type: string;
            title: string;
            metadata: unknown;
            status: "rejected" | "cancelled" | "awaiting_host" | "pending" | "accepted";
            description: string | null;
            startTime: Date;
            endTime: Date;
            userId: number | null;
            uid: string;
            idempotencyKey: string | null;
            userPrimaryEmail: string | null;
            eventTypeId: number | null;
            customInputs: unknown;
            responses: unknown;
            location: string | null;
            createdAt: Date;
            updatedAt: Date | null;
            paid: boolean;
            destinationCalendarId: number | null;
            cancellationReason: string | null;
            rejectionReason: string | null;
            reassignReason: string | null;
            reassignById: number | null;
            dynamicEventSlugRef: string | null;
            dynamicGroupSlugRef: string | null;
            rescheduled: boolean | null;
            fromReschedule: string | null;
            recurringEventId: string | null;
            smsReminderNumber: string | null;
            scheduledJobs: string[];
            isRecorded: boolean;
            iCalUID: string | null;
            iCalSequence: number;
            rating: number | null;
            ratingFeedback: string | null;
            noShowHost: boolean | null;
            oneTimePassword: string | null;
            cancelledBy: string | null;
            rescheduledBy: string | null;
            creationSource: import("@calcom/kysely/types").CreationSource | null;
            credentialId: number | null;
            delegationCredentialId: string | null;
            domainWideDelegationCredentialId: string | null;
            deleted: boolean | null;
            bookingId: number | null;
            thirdPartyRecurringEventId: string | null;
            meetingId: string | null;
            meetingPassword: string | null;
            meetingUrl: string | null;
            externalCalendarId: string | null;
        }[];
        payment: {
            paymentOption: "ON_BOOKING" | "HOLD" | null;
            currency: string;
            success: boolean;
            amount: number;
        }[];
        user: {
            id: number;
            name: string | null;
            email: string;
        } | null;
        attendees: {
            name: string;
            id: number;
            title: string;
            metadata: unknown;
            status: "rejected" | "cancelled" | "awaiting_host" | "pending" | "accepted";
            description: string | null;
            locale: string | null;
            startTime: Date;
            endTime: Date;
            userId: number | null;
            uid: string;
            idempotencyKey: string | null;
            userPrimaryEmail: string | null;
            eventTypeId: number | null;
            customInputs: unknown;
            responses: unknown;
            location: string | null;
            createdAt: Date;
            updatedAt: Date | null;
            paid: boolean;
            destinationCalendarId: number | null;
            cancellationReason: string | null;
            rejectionReason: string | null;
            reassignReason: string | null;
            reassignById: number | null;
            dynamicEventSlugRef: string | null;
            dynamicGroupSlugRef: string | null;
            rescheduled: boolean | null;
            fromReschedule: string | null;
            recurringEventId: string | null;
            smsReminderNumber: string | null;
            scheduledJobs: string[];
            isRecorded: boolean;
            iCalUID: string | null;
            iCalSequence: number;
            rating: number | null;
            ratingFeedback: string | null;
            noShowHost: boolean | null;
            oneTimePassword: string | null;
            cancelledBy: string | null;
            rescheduledBy: string | null;
            creationSource: import("@calcom/kysely/types").CreationSource | null;
            email: string;
            timeZone: string;
            bookingId: number | null;
            phoneNumber: string | null;
            noShow: boolean | null;
        }[];
        seatsReferences: {
            referenceUid: string;
            attendee: {
                email: string;
            } | null;
        }[];
        assignmentReason: {
            id: number;
            title: string;
            metadata: unknown;
            status: "rejected" | "cancelled" | "awaiting_host" | "pending" | "accepted";
            description: string | null;
            startTime: Date;
            endTime: Date;
            userId: number | null;
            uid: string;
            idempotencyKey: string | null;
            userPrimaryEmail: string | null;
            eventTypeId: number | null;
            customInputs: unknown;
            responses: unknown;
            location: string | null;
            createdAt: Date;
            updatedAt: Date | null;
            paid: boolean;
            destinationCalendarId: number | null;
            cancellationReason: string | null;
            rejectionReason: string | null;
            reassignReason: string | null;
            reassignById: number | null;
            dynamicEventSlugRef: string | null;
            dynamicGroupSlugRef: string | null;
            rescheduled: boolean | null;
            fromReschedule: string | null;
            recurringEventId: string | null;
            smsReminderNumber: string | null;
            scheduledJobs: string[];
            isRecorded: boolean;
            iCalUID: string | null;
            iCalSequence: number;
            rating: number | null;
            ratingFeedback: string | null;
            noShowHost: boolean | null;
            oneTimePassword: string | null;
            cancelledBy: string | null;
            rescheduledBy: string | null;
            creationSource: import("@calcom/kysely/types").CreationSource | null;
            bookingId: number;
            reasonEnum: import("@calcom/kysely/types").AssignmentReasonEnum;
            reasonString: string;
        }[];
    }[];
    recurringInfo: {
        recurringEventId: string | null;
        count: number;
        firstDate: Date | null;
        bookings: {
            [key: string]: Date[];
        };
    }[];
    totalCount: number;
}>;
export declare function getBookings({ user, prisma, kysely, bookingListingByStatus, sort, filters, take, skip, }: {
    user: {
        id: number;
        email: string;
        orgId?: number | null;
    };
    filters: TGetInputSchema["filters"];
    prisma: PrismaClient;
    kysely: Kysely<DB>;
    bookingListingByStatus: InputByStatus[];
    sort?: {
        sortStart?: "asc" | "desc";
        sortEnd?: "asc" | "desc";
        sortCreated?: "asc" | "desc";
        sortUpdated?: "asc" | "desc";
    };
    take: number;
    skip: number;
}): Promise<{
    bookings: {
        rescheduler: string | null;
        eventType: {
            recurringEvent: import("@calcom/types/Calendar").RecurringEvent | null;
            eventTypeColor: {
                lightEventTypeColor: string;
                darkEventTypeColor: string;
            } | null;
            price: number;
            currency: string;
            metadata: {
                config?: {
                    useHostSchedulesForTeamEvent?: boolean | undefined;
                } | undefined;
                smartContractAddress?: string | undefined;
                blockchainId?: number | undefined;
                multipleDuration?: number[] | undefined;
                giphyThankYouPage?: string | undefined;
                additionalNotesRequired?: boolean | undefined;
                disableSuccessPage?: boolean | undefined;
                disableStandardEmails?: {
                    all?: {
                        host?: boolean | undefined;
                        attendee?: boolean | undefined;
                    } | undefined;
                    confirmation?: {
                        host?: boolean | undefined;
                        attendee?: boolean | undefined;
                    } | undefined;
                } | undefined;
                managedEventConfig?: {
                    unlockedFields?: {
                        users?: true | undefined;
                        children?: true | undefined;
                        length?: true | undefined;
                        title?: true | undefined;
                        metadata?: true | undefined;
                        description?: true | undefined;
                        userId?: true | undefined;
                        calVideoSettings?: true | undefined;
                        destinationCalendar?: true | undefined;
                        profile?: true | undefined;
                        team?: true | undefined;
                        schedule?: true | undefined;
                        availability?: true | undefined;
                        hashedLink?: true | undefined;
                        secondaryEmail?: true | undefined;
                        customInputs?: true | undefined;
                        timeZone?: true | undefined;
                        bookings?: true | undefined;
                        selectedCalendars?: true | undefined;
                        webhooks?: true | undefined;
                        workflows?: true | undefined;
                        hosts?: true | undefined;
                        slug?: true | undefined;
                        parentId?: true | undefined;
                        bookingLimits?: true | undefined;
                        parent?: true | undefined;
                        teamId?: true | undefined;
                        hidden?: true | undefined;
                        _count?: true | undefined;
                        interfaceLanguage?: true | undefined;
                        position?: true | undefined;
                        locations?: true | undefined;
                        offsetStart?: true | undefined;
                        profileId?: true | undefined;
                        useEventLevelSelectedCalendars?: true | undefined;
                        eventName?: true | undefined;
                        bookingFields?: true | undefined;
                        periodType?: true | undefined;
                        periodStartDate?: true | undefined;
                        periodEndDate?: true | undefined;
                        periodDays?: true | undefined;
                        periodCountCalendarDays?: true | undefined;
                        lockTimeZoneToggleOnBookingPage?: true | undefined;
                        lockedTimeZone?: true | undefined;
                        requiresConfirmation?: true | undefined;
                        requiresConfirmationWillBlockSlot?: true | undefined;
                        requiresConfirmationForFreeEmail?: true | undefined;
                        requiresBookerEmailVerification?: true | undefined;
                        canSendCalVideoTranscriptionEmails?: true | undefined;
                        autoTranslateDescriptionEnabled?: true | undefined;
                        recurringEvent?: true | undefined;
                        disableGuests?: true | undefined;
                        hideCalendarNotes?: true | undefined;
                        hideCalendarEventDetails?: true | undefined;
                        minimumBookingNotice?: true | undefined;
                        beforeEventBuffer?: true | undefined;
                        afterEventBuffer?: true | undefined;
                        seatsPerTimeSlot?: true | undefined;
                        onlyShowFirstAvailableSlot?: true | undefined;
                        disableCancelling?: true | undefined;
                        disableRescheduling?: true | undefined;
                        seatsShowAttendees?: true | undefined;
                        seatsShowAvailabilityCount?: true | undefined;
                        schedulingType?: true | undefined;
                        scheduleId?: true | undefined;
                        allowReschedulingCancelledBookings?: true | undefined;
                        price?: true | undefined;
                        currency?: true | undefined;
                        slotInterval?: true | undefined;
                        successRedirectUrl?: true | undefined;
                        forwardParamsSuccessRedirect?: true | undefined;
                        durationLimits?: true | undefined;
                        isInstantEvent?: true | undefined;
                        instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                        instantMeetingScheduleId?: true | undefined;
                        instantMeetingParameters?: true | undefined;
                        assignAllTeamMembers?: true | undefined;
                        assignRRMembersUsingSegment?: true | undefined;
                        rrSegmentQueryValue?: true | undefined;
                        useEventTypeDestinationCalendarEmail?: true | undefined;
                        isRRWeightsEnabled?: true | undefined;
                        maxLeadThreshold?: true | undefined;
                        includeNoShowInRRCalculation?: true | undefined;
                        allowReschedulingPastBookings?: true | undefined;
                        hideOrganizerEmail?: true | undefined;
                        maxActiveBookingsPerBooker?: true | undefined;
                        maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                        customReplyToEmail?: true | undefined;
                        eventTypeColor?: true | undefined;
                        rescheduleWithSameRoundRobinHost?: true | undefined;
                        secondaryEmailId?: true | undefined;
                        useBookerTimezone?: true | undefined;
                        restrictionScheduleId?: true | undefined;
                        bookingRequiresAuthentication?: true | undefined;
                        owner?: true | undefined;
                        instantMeetingSchedule?: true | undefined;
                        aiPhoneCallConfig?: true | undefined;
                        fieldTranslations?: true | undefined;
                        restrictionSchedule?: true | undefined;
                        hostGroups?: true | undefined;
                    } | undefined;
                } | undefined;
                requiresConfirmationThreshold?: {
                    time: number;
                    unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
                } | undefined;
                bookerLayouts?: {
                    enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
                    defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
                } | null | undefined;
                apps?: unknown;
            } | null;
            id?: number | undefined;
            length?: number | undefined;
            title?: string | undefined;
            slug?: string | undefined;
            eventName?: string | null | undefined;
            disableGuests?: boolean | undefined;
            disableCancelling?: boolean | null | undefined;
            disableRescheduling?: boolean | null | undefined;
            seatsShowAttendees?: boolean | null | undefined;
            seatsShowAvailabilityCount?: boolean | null | undefined;
            allowReschedulingPastBookings?: boolean | undefined;
            hideOrganizerEmail?: boolean | undefined;
            customReplyToEmail?: string | null | undefined;
            schedulingType?: SchedulingType | null | undefined;
            hosts?: {
                userId: number;
                user: {
                    id: number;
                    email: string;
                } | null;
            }[] | undefined;
            team?: {
                id: number;
                name: string;
                slug: string | null;
            } | null | undefined;
            hostGroups?: {
                id: string;
                name: string;
            }[] | undefined;
        };
        startTime: string;
        endTime: string;
        id: number;
        title: string;
        metadata: unknown;
        description: string | null;
        uid: string;
        userPrimaryEmail: string | null;
        customInputs: unknown;
        location: string | null;
        createdAt: Date;
        updatedAt: Date | null;
        paid: boolean;
        rescheduled: boolean | null;
        fromReschedule: string | null;
        recurringEventId: string | null;
        isRecorded: boolean;
        responses: Prisma.JsonValue;
        status: BookingStatus;
        routedFromRoutingFormReponse: {
            id: number;
        } | null;
        references: {
            id: number;
            type: string;
            title: string;
            metadata: unknown;
            status: "rejected" | "cancelled" | "awaiting_host" | "pending" | "accepted";
            description: string | null;
            startTime: Date;
            endTime: Date;
            userId: number | null;
            uid: string;
            idempotencyKey: string | null;
            userPrimaryEmail: string | null;
            eventTypeId: number | null;
            customInputs: unknown;
            responses: unknown;
            location: string | null;
            createdAt: Date;
            updatedAt: Date | null;
            paid: boolean;
            destinationCalendarId: number | null;
            cancellationReason: string | null;
            rejectionReason: string | null;
            reassignReason: string | null;
            reassignById: number | null;
            dynamicEventSlugRef: string | null;
            dynamicGroupSlugRef: string | null;
            rescheduled: boolean | null;
            fromReschedule: string | null;
            recurringEventId: string | null;
            smsReminderNumber: string | null;
            scheduledJobs: string[];
            isRecorded: boolean;
            iCalUID: string | null;
            iCalSequence: number;
            rating: number | null;
            ratingFeedback: string | null;
            noShowHost: boolean | null;
            oneTimePassword: string | null;
            cancelledBy: string | null;
            rescheduledBy: string | null;
            creationSource: import("@calcom/kysely/types").CreationSource | null;
            credentialId: number | null;
            delegationCredentialId: string | null;
            domainWideDelegationCredentialId: string | null;
            deleted: boolean | null;
            bookingId: number | null;
            thirdPartyRecurringEventId: string | null;
            meetingId: string | null;
            meetingPassword: string | null;
            meetingUrl: string | null;
            externalCalendarId: string | null;
        }[];
        payment: {
            paymentOption: "ON_BOOKING" | "HOLD" | null;
            currency: string;
            success: boolean;
            amount: number;
        }[];
        user: {
            id: number;
            name: string | null;
            email: string;
        } | null;
        attendees: {
            name: string;
            id: number;
            title: string;
            metadata: unknown;
            status: "rejected" | "cancelled" | "awaiting_host" | "pending" | "accepted";
            description: string | null;
            locale: string | null;
            startTime: Date;
            endTime: Date;
            userId: number | null;
            uid: string;
            idempotencyKey: string | null;
            userPrimaryEmail: string | null;
            eventTypeId: number | null;
            customInputs: unknown;
            responses: unknown;
            location: string | null;
            createdAt: Date;
            updatedAt: Date | null;
            paid: boolean;
            destinationCalendarId: number | null;
            cancellationReason: string | null;
            rejectionReason: string | null;
            reassignReason: string | null;
            reassignById: number | null;
            dynamicEventSlugRef: string | null;
            dynamicGroupSlugRef: string | null;
            rescheduled: boolean | null;
            fromReschedule: string | null;
            recurringEventId: string | null;
            smsReminderNumber: string | null;
            scheduledJobs: string[];
            isRecorded: boolean;
            iCalUID: string | null;
            iCalSequence: number;
            rating: number | null;
            ratingFeedback: string | null;
            noShowHost: boolean | null;
            oneTimePassword: string | null;
            cancelledBy: string | null;
            rescheduledBy: string | null;
            creationSource: import("@calcom/kysely/types").CreationSource | null;
            email: string;
            timeZone: string;
            bookingId: number | null;
            phoneNumber: string | null;
            noShow: boolean | null;
        }[];
        seatsReferences: {
            referenceUid: string;
            attendee: {
                email: string;
            } | null;
        }[];
        assignmentReason: {
            id: number;
            title: string;
            metadata: unknown;
            status: "rejected" | "cancelled" | "awaiting_host" | "pending" | "accepted";
            description: string | null;
            startTime: Date;
            endTime: Date;
            userId: number | null;
            uid: string;
            idempotencyKey: string | null;
            userPrimaryEmail: string | null;
            eventTypeId: number | null;
            customInputs: unknown;
            responses: unknown;
            location: string | null;
            createdAt: Date;
            updatedAt: Date | null;
            paid: boolean;
            destinationCalendarId: number | null;
            cancellationReason: string | null;
            rejectionReason: string | null;
            reassignReason: string | null;
            reassignById: number | null;
            dynamicEventSlugRef: string | null;
            dynamicGroupSlugRef: string | null;
            rescheduled: boolean | null;
            fromReschedule: string | null;
            recurringEventId: string | null;
            smsReminderNumber: string | null;
            scheduledJobs: string[];
            isRecorded: boolean;
            iCalUID: string | null;
            iCalSequence: number;
            rating: number | null;
            ratingFeedback: string | null;
            noShowHost: boolean | null;
            oneTimePassword: string | null;
            cancelledBy: string | null;
            rescheduledBy: string | null;
            creationSource: import("@calcom/kysely/types").CreationSource | null;
            bookingId: number;
            reasonEnum: import("@calcom/kysely/types").AssignmentReasonEnum;
            reasonString: string;
        }[];
    }[];
    recurringInfo: {
        recurringEventId: string | null;
        count: number;
        firstDate: Date | null;
        bookings: {
            [key: string]: Date[];
        };
    }[];
    totalCount: number;
}>;
export {};
