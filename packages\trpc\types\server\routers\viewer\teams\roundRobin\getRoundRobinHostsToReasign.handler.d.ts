import type { PrismaClient } from "@calcom/prisma";
import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TGetRoundRobinHostsToReassignInputSchema } from "./getRoundRobinHostsToReasign.schema";
type GetRoundRobinHostsToReassignOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
        prisma: PrismaClient;
    };
    input: TGetRoundRobinHostsToReassignInputSchema;
};
export declare const getRoundRobinHostsToReassign: ({ ctx, input }: GetRoundRobinHostsToReassignOptions) => Promise<{
    items: {
        id: number;
        name: string | null;
        email: string;
        status: string;
    }[];
    nextCursor: number | null;
    totalCount: number;
}>;
export default getRoundRobinHostsToReassign;
