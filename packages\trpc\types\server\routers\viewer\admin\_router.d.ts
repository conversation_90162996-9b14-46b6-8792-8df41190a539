export declare const adminRouter: import("@trpc/server/unstable-core-do-not-import").CreateRouterInner<import("@trpc/server/unstable-core-do-not-import").RootConfig<{
    ctx: import("../../../createContext").InnerContext;
    meta: object;
    errorShape: {
        message: string;
        code: number;
        data: {
            code: string;
            httpStatus: number;
            path?: string;
            [key: string]: unknown;
        };
    };
    transformer: {
        stringify: (object: any) => string;
        parse: <T = unknown>(string: string) => T;
        serialize: (object: any) => import("superjson/dist/types").SuperJSONResult;
        deserialize: <T_1 = unknown>(payload: import("superjson/dist/types").SuperJSONResult) => T_1;
        registerClass: (v: import("superjson/dist/types").Class, options?: string | import("superjson/dist/class-registry").RegisterOptions | undefined) => void;
        registerSymbol: (v: Symbol, identifier?: string | undefined) => void;
        registerCustom: <I, O extends import("superjson/dist/types").JSONValue>(transformer: Omit<import("superjson/dist/custom-transformer-registry").CustomTransfomer<I, O>, "name">, name: string) => void;
        allowErrorProps: (...props: string[]) => void;
    };
}>, {
    listPaginated: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            limit: number;
            cursor?: number | null | undefined;
            searchTerm?: string | null | undefined;
        };
        output: {
            rows: {
                name: string | null;
                id: number;
                role: import(".prisma/client").$Enums.UserPermissionRole;
                email: string;
                username: string | null;
                timeZone: string;
                locked: boolean;
                whitelistWorkflows: boolean;
                profiles: {
                    username: string;
                }[];
            }[];
            nextCursor: number | undefined;
            meta: {
                totalRowCount: number;
            };
        };
    }>;
    sendPasswordReset: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            userId: number;
        };
        output: {
            success: boolean;
        };
    }>;
    lockUserAccount: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            userId: number;
            locked: boolean;
        };
        output: {
            success: boolean;
            userId: number;
            locked: boolean;
        };
    }>;
    toggleFeatureFlag: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            slug: string;
            enabled: boolean;
        };
        output: {
            type: import(".prisma/client").$Enums.FeatureType | null;
            description: string | null;
            createdAt: Date | null;
            updatedAt: Date | null;
            slug: string;
            enabled: boolean;
            lastUsedAt: Date | null;
            updatedBy: number | null;
            stale: boolean | null;
        };
    }>;
    removeTwoFactor: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            userId: number;
        };
        output: {
            success: boolean;
            userId: number;
        };
    }>;
    getSMSLockStateTeamsUsers: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: {
            users: {
                locked: {
                    name: string | null;
                    id: number;
                    email: string;
                    username: string | null;
                    avatarUrl: string | null;
                    smsLockState: import(".prisma/client").$Enums.SMSLockState;
                }[];
                reviewNeeded: {
                    name: string | null;
                    id: number;
                    email: string;
                    username: string | null;
                    avatarUrl: string | null;
                    smsLockState: import(".prisma/client").$Enums.SMSLockState;
                }[];
            };
            teams: {
                locked: {
                    name: string;
                    id: number;
                    smsLockState: import(".prisma/client").$Enums.SMSLockState;
                    slug: string | null;
                    logoUrl: string | null;
                }[];
                reviewNeeded: {
                    name: string;
                    id: number;
                    smsLockState: import(".prisma/client").$Enums.SMSLockState;
                    slug: string | null;
                    logoUrl: string | null;
                }[];
            };
        };
    }>;
    setSMSLockState: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            userId?: number | undefined;
            username?: string | undefined;
            teamId?: number | undefined;
            teamSlug?: string | undefined;
            lock?: boolean | undefined;
        };
        output: {
            name: string | null;
            locked: boolean | undefined;
        };
    }>;
    createSelfHostedLicense: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            billingPeriod: "MONTHLY" | "ANNUALLY";
            billingType: "PER_BOOKING" | "PER_USER";
            entityCount: number;
            entityPrice: number;
            overages: number;
            billingEmail: string;
        };
        output: {
            stripeCheckoutUrl: string;
        };
    }>;
    verifyWorkflows: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            userId: number;
        };
        output: void;
    }>;
    whitelistUserWorkflows: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            userId: number;
            whitelistWorkflows: boolean;
        };
        output: {
            whitelistWorkflows: boolean;
        };
    }>;
    workspacePlatform: import("@trpc/server/unstable-core-do-not-import").CreateRouterInner<import("@trpc/server/unstable-core-do-not-import").RootConfig<{
        ctx: import("../../../createContext").InnerContext;
        meta: object;
        errorShape: {
            message: string;
            code: number;
            data: {
                code: string;
                httpStatus: number;
                path?: string;
                [key: string]: unknown;
            };
        };
        transformer: {
            stringify: (object: any) => string;
            parse: <T = unknown>(string: string) => T;
            serialize: (object: any) => import("superjson/dist/types").SuperJSONResult;
            deserialize: <T_1 = unknown>(payload: import("superjson/dist/types").SuperJSONResult) => T_1;
            registerClass: (v: import("superjson/dist/types").Class, options?: string | import("superjson/dist/class-registry").RegisterOptions | undefined) => void;
            registerSymbol: (v: Symbol, identifier?: string | undefined) => void;
            registerCustom: <I, O extends import("superjson/dist/types").JSONValue>(transformer: Omit<import("superjson/dist/custom-transformer-registry").CustomTransfomer<I, O>, "name">, name: string) => void;
            allowErrorProps: (...props: string[]) => void;
        };
    }>, {
        list: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
            input: void;
            output: (Omit<{
                name: string;
                id: number;
                description: string;
                slug: string;
                enabled: boolean;
            }, "defaultServiceAccountKey"> & {
                defaultServiceAccountKey: undefined;
            })[];
        }>;
        add: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
            input: {
                name: string;
                description: string;
                slug: string;
                defaultServiceAccountKey?: import("zod").objectInputType<{
                    private_key: import("zod").ZodString;
                    client_email: import("zod").ZodOptional<import("zod").ZodString>;
                    client_id: import("zod").ZodString;
                    tenant_id: import("zod").ZodOptional<import("zod").ZodString>;
                }, import("zod").ZodTypeAny, "passthrough"> | undefined;
                enabled?: boolean | undefined;
            };
            output: Omit<{
                name: string;
                id: number;
                description: string;
                createdAt: Date;
                updatedAt: Date;
                slug: string;
                enabled: boolean;
                defaultServiceAccountKey: import(".prisma/client").Prisma.JsonValue;
            }, "defaultServiceAccountKey"> & {
                defaultServiceAccountKey: undefined;
            };
        }>;
        update: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
            input: {
                name: string;
                id: number;
                description: string;
            };
            output: Omit<{
                name: string;
                id: number;
                description: string;
                createdAt: Date;
                updatedAt: Date;
                slug: string;
                enabled: boolean;
                defaultServiceAccountKey: import(".prisma/client").Prisma.JsonValue;
            }, "defaultServiceAccountKey"> & {
                defaultServiceAccountKey: undefined;
            };
        }>;
        updateServiceAccount: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
            input: {
                id: number;
                defaultServiceAccountKey: {
                    client_id: string;
                    private_key: string;
                    client_email?: string | undefined;
                    tenant_id?: string | undefined;
                } & {
                    [k: string]: unknown;
                };
            };
            output: Omit<{
                name: string;
                id: number;
                description: string;
                createdAt: Date;
                updatedAt: Date;
                slug: string;
                enabled: boolean;
                defaultServiceAccountKey: import(".prisma/client").Prisma.JsonValue;
            }, "defaultServiceAccountKey"> & {
                defaultServiceAccountKey: undefined;
            };
        }>;
        toggleEnabled: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
            input: {
                id: number;
                enabled: boolean;
            };
            output: Omit<{
                name: string;
                id: number;
                description: string;
                createdAt: Date;
                updatedAt: Date;
                slug: string;
                enabled: boolean;
                defaultServiceAccountKey: import(".prisma/client").Prisma.JsonValue;
            }, "defaultServiceAccountKey"> & {
                defaultServiceAccountKey: undefined;
            };
        }>;
    }>;
}>;
