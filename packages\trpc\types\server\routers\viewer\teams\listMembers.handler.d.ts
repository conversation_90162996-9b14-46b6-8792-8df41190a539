import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TListMembersInputSchema } from "./listMembers.schema";
type ListMembersHandlerOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TListMembersInputSchema;
};
export declare const listMembersHandler: ({ ctx, input }: ListMembersHandlerOptions) => Promise<{
    members: {
        username: string | null;
        role: import(".prisma/client").$Enums.MembershipRole;
        customRoleId: string | null;
        customRole: {
            id: string;
            name: string;
        } | null;
        profile: import("@calcom/types/UserProfile").UserProfile;
        organizationId: number | null;
        organization: any;
        accepted: boolean;
        disableImpersonation: boolean;
        bookerUrl: string;
        teamId: number;
        lastActiveAt: string | null;
        name: string | null;
        id: number;
        email: string;
        bio: string | null;
        avatarUrl: string | null;
        nonProfileUsername: string | null;
    }[];
    nextCursor: number | undefined;
    meta: {
        totalRowCount: number;
    };
}>;
export default listMembersHandler;
