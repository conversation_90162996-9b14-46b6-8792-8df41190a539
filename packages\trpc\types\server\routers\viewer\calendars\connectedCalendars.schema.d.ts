import { z } from "zod";
export declare const ZConnectedCalendarsInputSchema: z.Zod<PERSON>ptional<z.ZodObject<{
    onboarding: z.ZodOptional<z.ZodBoolean>;
    eventTypeId: z.<PERSON><z.ZodNumber>;
}, "strip", z.<PERSON>ny, {
    eventTypeId: number | null;
    onboarding?: boolean | undefined;
}, {
    eventTypeId: number | null;
    onboarding?: boolean | undefined;
}>>;
export type TConnectedCalendarsInputSchema = z.infer<typeof ZConnectedCalendarsInputSchema>;
