import type { CacheService } from "@calcom/features/calendar-cache/lib/getShouldServeCache";
import type { FeaturesRepository } from "@calcom/features/flags/features.repository";
import type { IRedisService } from "@calcom/features/redis/IRedisService";
import type { QualifiedHostsService } from "@calcom/lib/bookings/findQualifiedHostsWithDelegationCredentials";
import type { BusyTimesService } from "@calcom/lib/getBusyTimes";
import type { UserAvailabilityService, IFromUser, IToUser } from "@calcom/lib/getUserAvailability";
import type { CheckBookingLimitsService } from "@calcom/lib/intervalLimits/server/checkBookingLimits";
import type { ISelectedSlotRepository } from "@calcom/lib/server/repository/ISelectedSlotRepository";
import type { BookingRepository } from "@calcom/lib/server/repository/booking";
import type { EventTypeRepository } from "@calcom/lib/server/repository/eventTypeRepository";
import type { RoutingFormResponseRepository } from "@calcom/lib/server/repository/formResponse";
import type { PrismaOOORepository } from "@calcom/lib/server/repository/ooo";
import type { ScheduleRepository } from "@calcom/lib/server/repository/schedule";
import type { TeamRepository } from "@calcom/lib/server/repository/team";
import type { UserRepository } from "@calcom/lib/server/repository/user";
import type { NoSlotsNotificationService } from "./handleNotificationWhenNoSlots";
import type { GetScheduleOptions } from "./types";
export interface IGetAvailableSlots {
    slots: Record<string, {
        time: string;
        attendees?: number | undefined;
        bookingUid?: string | undefined;
        away?: boolean | undefined;
        fromUser?: IFromUser | undefined;
        toUser?: IToUser | undefined;
        reason?: string | undefined;
        emoji?: string | undefined;
    }[]>;
    troubleshooter?: any;
}
export type GetAvailableSlotsResponse = Awaited<ReturnType<(typeof AvailableSlotsService)["prototype"]["_getAvailableSlots"]>>;
export interface IAvailableSlotsService {
    oooRepo: PrismaOOORepository;
    scheduleRepo: ScheduleRepository;
    selectedSlotRepo: ISelectedSlotRepository;
    teamRepo: TeamRepository;
    userRepo: UserRepository;
    bookingRepo: BookingRepository;
    eventTypeRepo: EventTypeRepository;
    routingFormResponseRepo: RoutingFormResponseRepository;
    cacheService: CacheService;
    checkBookingLimitsService: CheckBookingLimitsService;
    userAvailabilityService: UserAvailabilityService;
    busyTimesService: BusyTimesService;
    redisClient: IRedisService;
    featuresRepo: FeaturesRepository;
    qualifiedHostsService: QualifiedHostsService;
    noSlotsNotificationService: NoSlotsNotificationService;
}
export declare class AvailableSlotsService {
    readonly dependencies: IAvailableSlotsService;
    constructor(dependencies: IAvailableSlotsService);
    private _getReservedSlotsAndCleanupExpired;
    private _getDynamicEventType;
    private getDynamicEventType;
    private applyOccupiedSeatsToCurrentSeats;
    private _getEventType;
    private getEventType;
    private doesRangeStartFromToday;
    private _getAllDatesWithBookabilityStatus;
    private getAllDatesWithBookabilityStatus;
    private getUserIdFromUsername;
    private getEventTypeId;
    private getTeamIdFromSlug;
    private _getBusyTimesFromLimitsForUsers;
    private getBusyTimesFromLimitsForUsers;
    private _getBusyTimesFromTeamLimitsForUsers;
    private getBusyTimesFromTeamLimitsForUsers;
    private _getOOODates;
    private getOOODates;
    private _getUsersWithCredentials;
    private getUsersWithCredentials;
    private getStartTime;
    private calculateHostsAndAvailabilities;
    private checkRestrictionScheduleEnabled;
    private _getRegularOrDynamicEventType;
    private getRegularOrDynamicEventType;
    getAvailableSlots: (args: GetScheduleOptions) => Promise<IGetAvailableSlots>;
    _getAvailableSlots({ input, ctx }: GetScheduleOptions): Promise<IGetAvailableSlots>;
}
