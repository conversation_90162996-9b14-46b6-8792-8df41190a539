import { z } from "zod";
export declare const ZGetInputSchema: z.ZodObject<{
    id: z.ZodOptional<z.ZodString>;
    eventTypeId: z.ZodOptional<z.ZodNumber>;
    teamId: z.<PERSON>odOptional<z.ZodNumber>;
    webhookId: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodType<PERSON>ny, {
    id?: string | undefined;
    eventTypeId?: number | undefined;
    teamId?: number | undefined;
    webhookId?: string | undefined;
}, {
    id?: string | undefined;
    eventTypeId?: number | undefined;
    teamId?: number | undefined;
    webhookId?: string | undefined;
}>;
export type TGetInputSchema = z.infer<typeof ZGetInputSchema>;
