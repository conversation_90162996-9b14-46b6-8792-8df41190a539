import type { Prisma } from "@prisma/client";
import type { TrpcSessionUser } from "../../../types";
import type { TAdminUpdate } from "./adminUpdate.schema";
type AdminUpdateOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TAdminUpdate;
};
export declare const adminUpdateHandler: ({ input }: AdminUpdateOptions) => Promise<{
    name: string;
    id: number;
    metadata: Prisma.JsonValue;
    createdAt: Date;
    bio: string | null;
    timeZone: string;
    weekStart: string;
    hideBranding: boolean;
    theme: string | null;
    timeFormat: number | null;
    brandColor: string | null;
    darkBrandColor: string | null;
    smsLockState: import(".prisma/client").$Enums.SMSLockState;
    smsLockReviewedByAdmin: boolean;
    slug: string | null;
    logoUrl: string | null;
    calVideoLogo: string | null;
    appLogo: string | null;
    appIconLogo: string | null;
    hideTeamProfileLink: boolean;
    isPrivate: boolean;
    hideBookATeamMember: boolean;
    rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
    rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
    bannerUrl: string | null;
    parentId: number | null;
    isOrganization: boolean;
    pendingPayment: boolean;
    isPlatform: boolean;
    createdByOAuthClientId: string | null;
    bookingLimits: Prisma.JsonValue;
    includeManagedEventsInLimits: boolean;
}>;
export default adminUpdateHandler;
