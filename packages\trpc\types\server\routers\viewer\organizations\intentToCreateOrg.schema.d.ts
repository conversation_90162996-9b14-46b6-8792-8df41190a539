import { z } from "zod";
export declare enum BillingPeriod {
    MONTHLY = "MONTHLY",
    ANNUALLY = "ANNUALLY"
}
export declare const ZIntentToCreateOrgInputSchema: z.ZodObject<{
    name: z.ZodString;
    slug: z.ZodEffects<z.ZodString, string, string>;
    orgOwnerEmail: z.ZodString;
    language: z.ZodOptional<z.ZodString>;
    seats: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
    pricePerSeat: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
    isPlatform: z.<PERSON>od<PERSON>efault<z.ZodBoolean>;
    billingPeriod: z.<PERSON>ef<PERSON><z.ZodNativeEnum<typeof BillingPeriod>>;
    creationSource: z.ZodNativeEnum<{
        readonly API_V1: "API_V1";
        readonly API_V2: "API_V2";
        readonly WEBAPP: "WEBAPP";
    }>;
}, "strip", z.<PERSON>odTypeAny, {
    name: string;
    creationSource: "API_V1" | "API_V2" | "WEBAPP";
    slug: string;
    isPlatform: boolean;
    orgOwnerEmail: string;
    billingPeriod: BillingPeriod;
    language?: string | undefined;
    seats?: number | null | undefined;
    pricePerSeat?: number | null | undefined;
}, {
    name: string;
    creationSource: "API_V1" | "API_V2" | "WEBAPP";
    slug: string;
    orgOwnerEmail: string;
    language?: string | undefined;
    seats?: number | null | undefined;
    pricePerSeat?: number | null | undefined;
    isPlatform?: boolean | undefined;
    billingPeriod?: BillingPeriod | undefined;
}>;
export type TIntentToCreateOrgInputSchema = z.infer<typeof ZIntentToCreateOrgInputSchema>;
