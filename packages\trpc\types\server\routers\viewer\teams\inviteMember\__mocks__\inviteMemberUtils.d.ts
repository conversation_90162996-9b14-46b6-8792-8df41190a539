import type { MembershipRole } from "@calcom/prisma/enums";
import type * as inviteMemberUtils from "../utils";
declare const inviteMemberUtilsMock: import("vitest-mock-extended").DeepMockProxy<typeof inviteMemberUtils>;
export declare const inviteMemberutilsScenarios: {
    checkPermissions: {
        fakePassed: () => any;
    };
    getTeamOrThrow: {
        fakeReturnTeam: (team: {
            id: number;
        } & Record<string, any>, forInput: {
            teamId: number;
        }) => {
            id: number;
            organizationSettings: null;
            parent: null;
            parentId: null;
        };
    };
    getOrgState: {
        /**
         * `getOrgState` completely generates the return value from input without using any outside variable like DB, etc.
         * So, it makes sense to let it use the actual implementation instead of mocking the output based on input
         */
        useActual: () => Promise<typeof inviteMemberUtils.getOrgState & import("vitest-mock-extended").CalledWithMock<{
            isInOrgScope: boolean;
            orgVerified: boolean;
            orgConfigured: boolean;
            autoAcceptEmailDomain: string;
            orgPublished: boolean;
        } | {
            isInOrgScope: boolean;
            orgVerified: null;
            autoAcceptEmailDomain: null;
            orgConfigured: null;
            orgPublished: null;
        }, [isOrg: boolean, team: {
            id: number;
            name: string;
            slug: string | null;
            logoUrl: string | null;
            calVideoLogo: string | null;
            appLogo: string | null;
            appIconLogo: string | null;
            bio: string | null;
            hideBranding: boolean;
            hideTeamProfileLink: boolean;
            isPrivate: boolean;
            hideBookATeamMember: boolean;
            createdAt: Date;
            metadata: import(".prisma/client").Prisma.JsonValue | null;
            theme: string | null;
            rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
            rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
            brandColor: string | null;
            darkBrandColor: string | null;
            bannerUrl: string | null;
            parentId: number | null;
            timeFormat: number | null;
            timeZone: string;
            weekStart: string;
            isOrganization: boolean;
            pendingPayment: boolean;
            isPlatform: boolean;
            createdByOAuthClientId: string | null;
            smsLockState: import(".prisma/client").$Enums.SMSLockState;
            smsLockReviewedByAdmin: boolean;
            bookingLimits: import(".prisma/client").Prisma.JsonValue | null;
            includeManagedEventsInLimits: boolean;
        } & {
            organizationSettings?: import(".prisma/client").OrganizationSettings | null;
        } & {
            parent: ({
                id: number;
                name: string;
                slug: string | null;
                logoUrl: string | null;
                calVideoLogo: string | null;
                appLogo: string | null;
                appIconLogo: string | null;
                bio: string | null;
                hideBranding: boolean;
                hideTeamProfileLink: boolean;
                isPrivate: boolean;
                hideBookATeamMember: boolean;
                createdAt: Date;
                metadata: import(".prisma/client").Prisma.JsonValue | null;
                theme: string | null;
                rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
                rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
                brandColor: string | null;
                darkBrandColor: string | null;
                bannerUrl: string | null;
                parentId: number | null;
                timeFormat: number | null;
                timeZone: string;
                weekStart: string;
                isOrganization: boolean;
                pendingPayment: boolean;
                isPlatform: boolean;
                createdByOAuthClientId: string | null;
                smsLockState: import(".prisma/client").$Enums.SMSLockState;
                smsLockReviewedByAdmin: boolean;
                bookingLimits: import(".prisma/client").Prisma.JsonValue | null;
                includeManagedEventsInLimits: boolean;
            } & {
                organizationSettings?: import(".prisma/client").OrganizationSettings | null;
            }) | null;
        }]>>;
    };
    getUniqueInvitationsOrThrowIfEmpty: {
        useActual: () => Promise<typeof inviteMemberUtils.getUniqueInvitationsOrThrowIfEmpty & import("vitest-mock-extended").CalledWithMock<Promise<inviteMemberUtils.Invitation[]>, [invitations: inviteMemberUtils.Invitation[]]>>;
    };
    findUsersWithInviteStatus: {
        useAdvancedMock: (returnVal: Awaited<ReturnType<typeof inviteMemberUtilsMock.findUsersWithInviteStatus>>, forInput: {
            team: any;
            invitations: {
                usernameOrEmail: string;
                newRole?: MembershipRole;
            }[];
        }) => Promise<{
            newRole: MembershipRole;
            canBeInvited: inviteMemberUtils.INVITE_STATUS;
            id: number;
            email: string;
            username: string | null;
            completedOnboarding: boolean;
            identityProvider: import(".prisma/client").$Enums.IdentityProvider;
            teams?: Pick<import(".prisma/client").Membership, "userId" | "teamId" | "accepted" | "role">[];
            profiles: import(".prisma/client").Profile[];
            password: import(".prisma/client").UserPassword | null;
        }[]>;
    };
    getOrgConnectionInfo: {
        useActual: () => Promise<typeof inviteMemberUtils.getOrgConnectionInfo & import("vitest-mock-extended").CalledWithMock<{
            orgId: number | undefined;
            autoAccept: boolean;
        }, [{
            orgAutoAcceptDomain?: string | null;
            orgVerified: boolean | null;
            email: string;
            team: Pick<import("../types").TeamWithParent, "parentId" | "id">;
            isOrg: boolean;
        }]>>;
    };
};
export declare const expects: {
    expectSignupEmailsToBeSent: ({ emails, team, inviterName, isOrg, teamId, }: {
        emails: string[];
        team: any[];
        inviterName: string;
        teamId: number;
        isOrg: boolean;
    }) => void;
};
export default inviteMemberUtilsMock;
