import { z } from "zod";
export declare const ZAdminUpdate: z.ZodObject<{
    id: z.Z<PERSON><PERSON><PERSON>;
    name: z.<PERSON>od<PERSON>ptional<z.ZodString>;
    slug: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    organizationSettings: z.<PERSON>al<z.ZodObject<{
        isOrganizationVerified: z.<PERSON>ptional<z.ZodBoolean>;
        isOrganizationConfigured: z.Z<PERSON>ptional<z.ZodBoolean>;
        isAdminReviewed: z.<PERSON>odOptional<z.ZodBoolean>;
        orgAutoAcceptEmail: z.ZodOptional<z.ZodString>;
        isAdminAPIEnabled: z.<PERSON><z.ZodBoolean>;
    }, "strip", z.Zod<PERSON>ype<PERSON>ny, {
        isOrganizationVerified?: boolean | undefined;
        isOrganizationConfigured?: boolean | undefined;
        isAdminReviewed?: boolean | undefined;
        orgAutoAcceptEmail?: string | undefined;
        isAdminAPIEnabled?: boolean | undefined;
    }, {
        isOrganizationVerified?: boolean | undefined;
        isOrganizationConfigured?: boolean | undefined;
        isAdminReviewed?: boolean | undefined;
        orgAutoAcceptEmail?: string | undefined;
        isAdminAPIEnabled?: boolean | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    id: number;
    name?: string | undefined;
    slug?: string | null | undefined;
    organizationSettings?: {
        isOrganizationVerified?: boolean | undefined;
        isOrganizationConfigured?: boolean | undefined;
        isAdminReviewed?: boolean | undefined;
        orgAutoAcceptEmail?: string | undefined;
        isAdminAPIEnabled?: boolean | undefined;
    } | undefined;
}, {
    id: number;
    name?: string | undefined;
    slug?: string | null | undefined;
    organizationSettings?: {
        isOrganizationVerified?: boolean | undefined;
        isOrganizationConfigured?: boolean | undefined;
        isAdminReviewed?: boolean | undefined;
        orgAutoAcceptEmail?: string | undefined;
        isAdminAPIEnabled?: boolean | undefined;
    } | undefined;
}>;
export type TAdminUpdate = z.infer<typeof ZAdminUpdate>;
