import type { TrpcSessionUser } from "../../../types";
type AdminGetAllOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
};
export declare const adminGetUnverifiedHandler: ({}: AdminGetAllOptions) => Promise<{
    metadata: {
        defaultConferencingApp?: {
            appSlug?: string | undefined;
            appLink?: string | undefined;
        } | undefined;
        requestedSlug?: string | null | undefined;
        paymentId?: string | undefined;
        subscriptionId?: string | null | undefined;
        subscriptionItemId?: string | null | undefined;
        orgSeats?: number | null | undefined;
        orgPricePerSeat?: number | null | undefined;
        migratedToOrgFrom?: {
            teamSlug?: string | null | undefined;
            lastMigrationTime?: string | undefined;
            reverted?: boolean | undefined;
            lastRevertTime?: string | undefined;
        } | undefined;
        billingPeriod?: import("@calcom/prisma/zod-utils").BillingPeriod | undefined;
    } | null;
    name: string;
    id: number;
    organizationSettings: {
        id: number;
        allowSEOIndexing: boolean;
        organizationId: number;
        isOrganizationConfigured: boolean;
        isOrganizationVerified: boolean;
        orgAutoAcceptEmail: string;
        lockEventTypeCreationForUsers: boolean;
        adminGetsNoSlotsNotification: boolean;
        isAdminReviewed: boolean;
        isAdminAPIEnabled: boolean;
        orgProfileRedirectsToVerifiedDomain: boolean;
        disablePhoneOnlySMSNotifications: boolean;
    } | null;
    slug: string | null;
    members: {
        user: {
            name: string | null;
            id: number;
            email: string;
        };
    }[];
}[]>;
export default adminGetUnverifiedHandler;
