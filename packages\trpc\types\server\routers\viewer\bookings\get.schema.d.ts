import { z } from "zod";
export declare const ZGetInputSchema: z.ZodObject<{
    filters: z.ZodObject<{
        teamIds: z.<PERSON>od<PERSON>ptional<z.<PERSON><PERSON>rray<z.ZodNumber, "many">>;
        userIds: z.<PERSON>od<PERSON>ptional<z.<PERSON><z.ZodNumber, "many">>;
        status: z.<PERSON>od<PERSON>al<z.ZodEnum<["upcoming", "recurring", "past", "cancelled", "unconfirmed"]>>;
        eventTypeIds: z.<PERSON>od<PERSON>ptional<z.<PERSON>od<PERSON>rray<z.ZodNumber, "many">>;
        attendeeEmail: z.ZodOptional<z.<PERSON>odU<PERSON>n<[z.ZodString, z.ZodObject<{
            type: z.ZodLiteral<import("@calcom/features/data-table/lib/types").ColumnFilterType.TEXT>;
            data: z.ZodObject<{
                operator: z.<PERSON>od<PERSON>num<["equals", "notEquals", "contains", "notContains", "startsWith", "endsWith", "isEmpty", "isNotEmpty"]>;
                operand: z.<PERSON>;
            }, "strip", z.<PERSON>odType<PERSON>ny, {
                operator: "endsWith" | "startsWith" | "equals" | "notEquals" | "contains" | "notContains" | "isEmpty" | "isNotEmpty";
                operand: string;
            }, {
                operator: "endsWith" | "startsWith" | "equals" | "notEquals" | "contains" | "notContains" | "isEmpty" | "isNotEmpty";
                operand: string;
            }>;
        }, "strip", z.ZodTypeAny, {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.TEXT;
            data: {
                operator: "endsWith" | "startsWith" | "equals" | "notEquals" | "contains" | "notContains" | "isEmpty" | "isNotEmpty";
                operand: string;
            };
        }, {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.TEXT;
            data: {
                operator: "endsWith" | "startsWith" | "equals" | "notEquals" | "contains" | "notContains" | "isEmpty" | "isNotEmpty";
                operand: string;
            };
        }>]>>;
        attendeeName: z.ZodOptional<z.ZodUnion<[z.ZodString, z.ZodObject<{
            type: z.ZodLiteral<import("@calcom/features/data-table/lib/types").ColumnFilterType.TEXT>;
            data: z.ZodObject<{
                operator: z.ZodEnum<["equals", "notEquals", "contains", "notContains", "startsWith", "endsWith", "isEmpty", "isNotEmpty"]>;
                operand: z.ZodString;
            }, "strip", z.ZodTypeAny, {
                operator: "endsWith" | "startsWith" | "equals" | "notEquals" | "contains" | "notContains" | "isEmpty" | "isNotEmpty";
                operand: string;
            }, {
                operator: "endsWith" | "startsWith" | "equals" | "notEquals" | "contains" | "notContains" | "isEmpty" | "isNotEmpty";
                operand: string;
            }>;
        }, "strip", z.ZodTypeAny, {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.TEXT;
            data: {
                operator: "endsWith" | "startsWith" | "equals" | "notEquals" | "contains" | "notContains" | "isEmpty" | "isNotEmpty";
                operand: string;
            };
        }, {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.TEXT;
            data: {
                operator: "endsWith" | "startsWith" | "equals" | "notEquals" | "contains" | "notContains" | "isEmpty" | "isNotEmpty";
                operand: string;
            };
        }>]>>;
        bookingUid: z.ZodOptional<z.ZodString>;
        afterStartDate: z.ZodOptional<z.ZodString>;
        beforeEndDate: z.ZodOptional<z.ZodString>;
        afterUpdatedDate: z.ZodOptional<z.ZodString>;
        beforeUpdatedDate: z.ZodOptional<z.ZodString>;
        afterCreatedDate: z.ZodOptional<z.ZodString>;
        beforeCreatedDate: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        teamIds?: number[] | undefined;
        userIds?: number[] | undefined;
        status?: "past" | "upcoming" | "recurring" | "unconfirmed" | "cancelled" | undefined;
        eventTypeIds?: number[] | undefined;
        attendeeEmail?: string | {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.TEXT;
            data: {
                operator: "endsWith" | "startsWith" | "equals" | "notEquals" | "contains" | "notContains" | "isEmpty" | "isNotEmpty";
                operand: string;
            };
        } | undefined;
        attendeeName?: string | {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.TEXT;
            data: {
                operator: "endsWith" | "startsWith" | "equals" | "notEquals" | "contains" | "notContains" | "isEmpty" | "isNotEmpty";
                operand: string;
            };
        } | undefined;
        bookingUid?: string | undefined;
        afterStartDate?: string | undefined;
        beforeEndDate?: string | undefined;
        afterUpdatedDate?: string | undefined;
        beforeUpdatedDate?: string | undefined;
        afterCreatedDate?: string | undefined;
        beforeCreatedDate?: string | undefined;
    }, {
        teamIds?: number[] | undefined;
        userIds?: number[] | undefined;
        status?: "past" | "upcoming" | "recurring" | "unconfirmed" | "cancelled" | undefined;
        eventTypeIds?: number[] | undefined;
        attendeeEmail?: string | {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.TEXT;
            data: {
                operator: "endsWith" | "startsWith" | "equals" | "notEquals" | "contains" | "notContains" | "isEmpty" | "isNotEmpty";
                operand: string;
            };
        } | undefined;
        attendeeName?: string | {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.TEXT;
            data: {
                operator: "endsWith" | "startsWith" | "equals" | "notEquals" | "contains" | "notContains" | "isEmpty" | "isNotEmpty";
                operand: string;
            };
        } | undefined;
        bookingUid?: string | undefined;
        afterStartDate?: string | undefined;
        beforeEndDate?: string | undefined;
        afterUpdatedDate?: string | undefined;
        beforeUpdatedDate?: string | undefined;
        afterCreatedDate?: string | undefined;
        beforeCreatedDate?: string | undefined;
    }>;
    limit: z.ZodNumber;
    offset: z.ZodDefault<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    offset: number;
    filters: {
        teamIds?: number[] | undefined;
        userIds?: number[] | undefined;
        status?: "past" | "upcoming" | "recurring" | "unconfirmed" | "cancelled" | undefined;
        eventTypeIds?: number[] | undefined;
        attendeeEmail?: string | {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.TEXT;
            data: {
                operator: "endsWith" | "startsWith" | "equals" | "notEquals" | "contains" | "notContains" | "isEmpty" | "isNotEmpty";
                operand: string;
            };
        } | undefined;
        attendeeName?: string | {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.TEXT;
            data: {
                operator: "endsWith" | "startsWith" | "equals" | "notEquals" | "contains" | "notContains" | "isEmpty" | "isNotEmpty";
                operand: string;
            };
        } | undefined;
        bookingUid?: string | undefined;
        afterStartDate?: string | undefined;
        beforeEndDate?: string | undefined;
        afterUpdatedDate?: string | undefined;
        beforeUpdatedDate?: string | undefined;
        afterCreatedDate?: string | undefined;
        beforeCreatedDate?: string | undefined;
    };
    limit: number;
}, {
    filters: {
        teamIds?: number[] | undefined;
        userIds?: number[] | undefined;
        status?: "past" | "upcoming" | "recurring" | "unconfirmed" | "cancelled" | undefined;
        eventTypeIds?: number[] | undefined;
        attendeeEmail?: string | {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.TEXT;
            data: {
                operator: "endsWith" | "startsWith" | "equals" | "notEquals" | "contains" | "notContains" | "isEmpty" | "isNotEmpty";
                operand: string;
            };
        } | undefined;
        attendeeName?: string | {
            type: import("@calcom/features/data-table/lib/types").ColumnFilterType.TEXT;
            data: {
                operator: "endsWith" | "startsWith" | "equals" | "notEquals" | "contains" | "notContains" | "isEmpty" | "isNotEmpty";
                operand: string;
            };
        } | undefined;
        bookingUid?: string | undefined;
        afterStartDate?: string | undefined;
        beforeEndDate?: string | undefined;
        afterUpdatedDate?: string | undefined;
        beforeUpdatedDate?: string | undefined;
        afterCreatedDate?: string | undefined;
        beforeCreatedDate?: string | undefined;
    };
    limit: number;
    offset?: number | undefined;
}>;
export type TGetInputSchema = z.infer<typeof ZGetInputSchema>;
