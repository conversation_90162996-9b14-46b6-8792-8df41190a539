import type { PrismaClient } from "@calcom/prisma";
import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TUpdateInputSchema } from "./update.schema";
type UpdateOptions = {
    ctx: {
        user: Pick<NonNullable<TrpcSessionUser>, "id" | "metadata" | "locale" | "timeFormat">;
        prisma: PrismaClient;
    };
    input: TUpdateInputSchema;
};
export declare const updateHandler: ({ ctx, input }: UpdateOptions) => Promise<{
    workflow: ({
        team: {
            name: string;
            id: number;
            slug: string | null;
            isOrganization: boolean;
            members: {
                id: number;
                role: import(".prisma/client").$Enums.MembershipRole;
                userId: number;
                createdAt: Date | null;
                updatedAt: Date | null;
                disableImpersonation: boolean;
                teamId: number;
                accepted: boolean;
                customRoleId: string | null;
            }[];
        } | null;
        steps: {
            id: number;
            template: import(".prisma/client").$Enums.WorkflowTemplates;
            action: import(".prisma/client").$Enums.WorkflowActions;
            stepNumber: number;
            workflowId: number;
            sendTo: string | null;
            reminderBody: string | null;
            emailSubject: string | null;
            numberRequired: boolean | null;
            sender: string | null;
            numberVerificationPending: boolean;
            includeCalendarEvent: boolean;
            verifiedAt: Date | null;
            agentId: string | null;
        }[];
        activeOn: {
            eventType: {
                id: number;
                length: number;
                title: string;
                metadata: import(".prisma/client").Prisma.JsonValue;
                description: string | null;
                userId: number | null;
                timeZone: string | null;
                slug: string;
                parentId: number | null;
                bookingLimits: import(".prisma/client").Prisma.JsonValue;
                teamId: number | null;
                hidden: boolean;
                interfaceLanguage: string | null;
                position: number;
                locations: import(".prisma/client").Prisma.JsonValue;
                offsetStart: number;
                profileId: number | null;
                useEventLevelSelectedCalendars: boolean;
                eventName: string | null;
                bookingFields: import(".prisma/client").Prisma.JsonValue;
                periodType: import(".prisma/client").$Enums.PeriodType;
                periodStartDate: Date | null;
                periodEndDate: Date | null;
                periodDays: number | null;
                periodCountCalendarDays: boolean | null;
                lockTimeZoneToggleOnBookingPage: boolean;
                lockedTimeZone: string | null;
                requiresConfirmation: boolean;
                requiresConfirmationWillBlockSlot: boolean;
                requiresConfirmationForFreeEmail: boolean;
                requiresBookerEmailVerification: boolean;
                canSendCalVideoTranscriptionEmails: boolean;
                autoTranslateDescriptionEnabled: boolean;
                recurringEvent: import(".prisma/client").Prisma.JsonValue;
                disableGuests: boolean;
                hideCalendarNotes: boolean;
                hideCalendarEventDetails: boolean;
                minimumBookingNotice: number;
                beforeEventBuffer: number;
                afterEventBuffer: number;
                seatsPerTimeSlot: number | null;
                onlyShowFirstAvailableSlot: boolean;
                disableCancelling: boolean | null;
                disableRescheduling: boolean | null;
                seatsShowAttendees: boolean | null;
                seatsShowAvailabilityCount: boolean | null;
                schedulingType: import(".prisma/client").$Enums.SchedulingType | null;
                scheduleId: number | null;
                allowReschedulingCancelledBookings: boolean | null;
                price: number;
                currency: string;
                slotInterval: number | null;
                successRedirectUrl: string | null;
                forwardParamsSuccessRedirect: boolean | null;
                durationLimits: import(".prisma/client").Prisma.JsonValue;
                isInstantEvent: boolean;
                instantMeetingExpiryTimeOffsetInSeconds: number;
                instantMeetingScheduleId: number | null;
                instantMeetingParameters: string[];
                assignAllTeamMembers: boolean;
                assignRRMembersUsingSegment: boolean;
                rrSegmentQueryValue: import(".prisma/client").Prisma.JsonValue;
                useEventTypeDestinationCalendarEmail: boolean;
                isRRWeightsEnabled: boolean;
                maxLeadThreshold: number | null;
                includeNoShowInRRCalculation: boolean;
                allowReschedulingPastBookings: boolean;
                hideOrganizerEmail: boolean;
                maxActiveBookingsPerBooker: number | null;
                maxActiveBookingPerBookerOfferReschedule: boolean;
                customReplyToEmail: string | null;
                eventTypeColor: import(".prisma/client").Prisma.JsonValue;
                rescheduleWithSameRoundRobinHost: boolean;
                secondaryEmailId: number | null;
                useBookerTimezone: boolean;
                restrictionScheduleId: number | null;
                bookingRequiresAuthentication: boolean;
            };
        }[];
        activeOnTeams: {
            team: {
                name: string;
                id: number;
                metadata: import(".prisma/client").Prisma.JsonValue;
                createdAt: Date;
                bio: string | null;
                timeZone: string;
                weekStart: string;
                hideBranding: boolean;
                theme: string | null;
                timeFormat: number | null;
                brandColor: string | null;
                darkBrandColor: string | null;
                smsLockState: import(".prisma/client").$Enums.SMSLockState;
                smsLockReviewedByAdmin: boolean;
                slug: string | null;
                logoUrl: string | null;
                calVideoLogo: string | null;
                appLogo: string | null;
                appIconLogo: string | null;
                hideTeamProfileLink: boolean;
                isPrivate: boolean;
                hideBookATeamMember: boolean;
                rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
                rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
                bannerUrl: string | null;
                parentId: number | null;
                isOrganization: boolean;
                pendingPayment: boolean;
                isPlatform: boolean;
                createdByOAuthClientId: string | null;
                bookingLimits: import(".prisma/client").Prisma.JsonValue;
                includeManagedEventsInLimits: boolean;
            };
        }[];
    } & {
        name: string;
        id: number;
        time: number | null;
        userId: number | null;
        teamId: number | null;
        position: number;
        trigger: import(".prisma/client").$Enums.WorkflowTriggerEvents;
        timeUnit: import(".prisma/client").$Enums.TimeUnit | null;
        isActiveOnAll: boolean;
    }) | null;
}>;
export {};
