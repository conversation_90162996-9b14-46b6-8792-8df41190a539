import type { TrpcSessionUser } from "../../../types";
import type { ZGetAttributeSchema } from "./get.schema";
type GetOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: ZGetAttributeSchema;
};
declare const getAttributeHandler: ({ input, ctx }: GetOptions) => Promise<{
    name: string;
    id: string;
    type: "TEXT" | "NUMBER" | "SINGLE_SELECT" | "MULTI_SELECT";
    options: {
        value: string;
        id?: string | undefined;
        isGroup?: boolean | undefined;
        assignedUsers?: number | undefined;
        contains?: string[] | undefined;
    }[];
    isLocked?: boolean | undefined;
    isWeightsEnabled?: boolean | undefined;
}>;
export default getAttributeHandler;
