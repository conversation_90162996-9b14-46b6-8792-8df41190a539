import { z } from "zod";
export declare const ZBulkUpdateToDefaultAvailabilityInputSchema: z.ZodObject<{
    eventTypeIds: z.<PERSON><z.ZodNumber, "many">;
    selectedDefaultScheduleId: z.<PERSON>od<PERSON><z.ZodNullable<z.ZodNumber>>;
}, "strip", z.<PERSON>, {
    eventTypeIds: number[];
    selectedDefaultScheduleId?: number | null | undefined;
}, {
    eventTypeIds: number[];
    selectedDefaultScheduleId?: number | null | undefined;
}>;
export type TBulkUpdateToDefaultAvailabilityInputSchema = z.infer<typeof ZBulkUpdateToDefaultAvailabilityInputSchema>;
