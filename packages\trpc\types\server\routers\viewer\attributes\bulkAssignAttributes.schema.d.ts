import { z } from "zod";
export declare const bulkAssignAttributesSchema: z.ZodObject<{
    userIds: z.<PERSON><z.<PERSON>od<PERSON><PERSON><PERSON>, "many">;
    attributes: z.<PERSON>od<PERSON>rray<z.ZodObject<{
        id: z.ZodString;
        options: z.<PERSON>ptional<z.Zod<PERSON>rray<z.ZodObject<{
            label: z.ZodOptional<z.ZodString>;
            value: z.ZodString;
        }, "strip", z.ZodType<PERSON>ny, {
            value: string;
            label?: string | undefined;
        }, {
            value: string;
            label?: string | undefined;
        }>, "many">>;
        value: z.ZodOptional<z.ZodString>;
    }, "strip", z.<PERSON>odType<PERSON>ny, {
        id: string;
        options?: {
            value: string;
            label?: string | undefined;
        }[] | undefined;
        value?: string | undefined;
    }, {
        id: string;
        options?: {
            value: string;
            label?: string | undefined;
        }[] | undefined;
        value?: string | undefined;
    }>, "many">;
}, "strip", z.<PERSON><PERSON><PERSON><PERSON>ny, {
    attributes: {
        id: string;
        options?: {
            value: string;
            label?: string | undefined;
        }[] | undefined;
        value?: string | undefined;
    }[];
    userIds: number[];
}, {
    attributes: {
        id: string;
        options?: {
            value: string;
            label?: string | undefined;
        }[] | undefined;
        value?: string | undefined;
    }[];
    userIds: number[];
}>;
export type ZBulkAssignAttributes = z.infer<typeof bulkAssignAttributesSchema>;
