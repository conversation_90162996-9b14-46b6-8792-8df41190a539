import { z } from "zod";
export declare const ZOutOfOfficeEntriesListSchema: z.ZodObject<{
    limit: z.ZodNumber;
    cursor: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
    fetchTeamMembersEntries: z.<PERSON>ault<z.ZodOptional<z.ZodBoolean>>;
    searchTerm: z.ZodOptional<z.ZodString>;
    endDateFilterStartRange: z.ZodOptional<z.ZodString>;
    endDateFilterEndRange: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    limit: number;
    fetchTeamMembersEntries: boolean;
    cursor?: number | null | undefined;
    searchTerm?: string | undefined;
    endDateFilterStartRange?: string | undefined;
    endDateFilterEndRange?: string | undefined;
}, {
    limit: number;
    cursor?: number | null | undefined;
    fetchTeamMembersEntries?: boolean | undefined;
    searchTerm?: string | undefined;
    endDateFilterStartRange?: string | undefined;
    endDateFilterEndRange?: string | undefined;
}>;
export type TOutOfOfficeEntriesListSchema = z.infer<typeof ZOutOfOfficeEntriesListSchema>;
