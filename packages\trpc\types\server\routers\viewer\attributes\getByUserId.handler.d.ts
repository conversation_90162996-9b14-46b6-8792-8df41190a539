import type { AttributeType } from "@calcom/prisma/enums";
import type { TrpcSessionUser } from "../../../types";
import type { ZGetByUserIdSchema } from "./getByUserId.schema";
type GetOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: ZGetByUserIdSchema;
};
export type GroupedAttribute = {
    id: string;
    name: string;
    type: AttributeType;
    options: {
        id: string;
        slug: string;
        value: string;
        weight: number | null;
        createdByDSyncId: string | null;
    }[];
};
declare const getByUserIdHandler: ({ input, ctx }: GetOptions) => Promise<GroupedAttribute[]>;
declare function getMembershipAttributes(membershipId: number): Promise<{
    attributeOption: {
        id: string;
        value: string;
        attribute: {
            name: string;
            id: string;
            type: import(".prisma/client").$Enums.AttributeType;
        };
        slug: string;
    };
    weight: number | null;
    createdByDSyncId: string | null;
}[]>;
type MembershipAttributes = Awaited<ReturnType<typeof getMembershipAttributes>>;
export declare function groupMembershipAttributes(membershipAttributes: MembershipAttributes): GroupedAttribute[];
export default getByUserIdHandler;
