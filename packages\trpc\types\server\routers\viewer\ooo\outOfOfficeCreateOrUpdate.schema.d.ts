import { z } from "zod";
export declare const ZOutOfOfficeInputSchema: z.ZodObject<{
    uuid: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    forUserId: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
    dateRange: z.ZodObject<{
        startDate: z.ZodDate;
        endDate: z.ZodDate;
    }, "strip", z.ZodTypeAny, {
        startDate: Date;
        endDate: Date;
    }, {
        startDate: Date;
        endDate: Date;
    }>;
    startDateOffset: z.ZodNumber;
    endDateOffset: z.ZodNumber;
    toTeamUserId: z.ZodNullable<z.ZodNumber>;
    reasonId: z.ZodNumber;
    notes: z.ZodOptional<z.ZodNullable<z.ZodString>>;
}, "strip", z.ZodTypeAny, {
    reasonId: number;
    dateRange: {
        startDate: Date;
        endDate: Date;
    };
    startDateOffset: number;
    endDateOffset: number;
    toTeamUserId: number | null;
    uuid?: string | null | undefined;
    forUserId?: number | null | undefined;
    notes?: string | null | undefined;
}, {
    reasonId: number;
    dateRange: {
        startDate: Date;
        endDate: Date;
    };
    startDateOffset: number;
    endDateOffset: number;
    toTeamUserId: number | null;
    uuid?: string | null | undefined;
    forUserId?: number | null | undefined;
    notes?: string | null | undefined;
}>;
export type TOutOfOfficeInputSchema = z.infer<typeof ZOutOfOfficeInputSchema>;
