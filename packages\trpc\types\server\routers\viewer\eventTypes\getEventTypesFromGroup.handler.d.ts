import type { PrismaClient } from "@calcom/prisma";
import type { TrpcSessionUser } from "../../../types";
import type { TGetEventTypesFromGroupSchema } from "./getByViewer.schema";
import { mapEventType } from "./util";
type GetByViewerOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
        prisma: PrismaClient;
    };
    input: TGetEventTypesFromGroupSchema;
};
type MappedEventType = Awaited<ReturnType<typeof mapEventType>>;
export declare const getEventTypesFromGroup: ({ ctx, input, }: GetByViewerOptions) => Promise<{
    eventTypes: MappedEventType[];
    nextCursor: number | null | undefined;
}>;
export {};
