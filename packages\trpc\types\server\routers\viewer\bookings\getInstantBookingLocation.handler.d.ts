import type { PrismaClient } from "@calcom/prisma";
import type { TInstantBookingInputSchema } from "./getInstantBookingLocation.schema";
type GetOptions = {
    ctx: {
        prisma: PrismaClient;
    };
    input: TInstantBookingInputSchema;
};
export declare const getHandler: ({ ctx, input }: GetOptions) => Promise<{
    booking: {
        id: number;
        metadata: import(".prisma/client").Prisma.JsonValue;
        status: import(".prisma/client").$Enums.BookingStatus;
        description: string | null;
        startTime: Date;
        endTime: Date;
        uid: string;
        eventTypeId: number | null;
        location: string | null;
    } | null;
}>;
export {};
