import type { ServerResponse } from "http";
import type { NextApiResponse } from "next";
import type { PrismaClient } from "@calcom/prisma";
import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TFindTeamMembersMatchingAttributeLogicOfRouteInputSchema } from "./findTeamMembersMatchingAttributeLogicOfRoute.schema";
interface FindTeamMembersMatchingAttributeLogicOfRouteHandlerOptions {
    ctx: {
        prisma: PrismaClient;
        user: NonNullable<TrpcSessionUser>;
        res: ServerResponse | NextApiResponse | undefined;
    };
    input: TFindTeamMembersMatchingAttributeLogicOfRouteInputSchema;
}
export declare const findTeamMembersMatchingAttributeLogicOfRouteHandler: ({ ctx, input, }: FindTeamMembersMatchingAttributeLogicOfRouteHandlerOptions) => Promise<{
    troubleshooter: null;
    result: null;
    contactOwnerEmail: null;
    checkedFallback: boolean;
    mainWarnings: never[];
    fallbackWarnings: never[];
    eventTypeRedirectUrl: null;
    isUsingAttributeWeights: boolean;
} | {
    contactOwnerEmail: string | null;
    troubleshooter: {
        type: import("@calcom/lib/raqb/findTeamMembersMatchingAttributeLogic").TroubleshooterCase;
        data: Record<string, any>;
    } | undefined;
    checkedFallback: boolean;
    mainWarnings: string[] | null;
    fallbackWarnings: string[] | null;
    eventTypeRedirectUrl: string;
    isUsingAttributeWeights: boolean;
    result: null;
} | {
    troubleshooter: {
        type: import("@calcom/lib/raqb/findTeamMembersMatchingAttributeLogic").TroubleshooterCase;
        data: Record<string, any>;
    } | undefined;
    contactOwnerEmail: string | null;
    checkedFallback: boolean;
    mainWarnings: string[] | null;
    fallbackWarnings: string[] | null;
    result: {
        users: {
            id: number;
            name: string | null;
            email: string;
        }[];
        perUserData: {
            bookingsCount: Record<number, number>;
            bookingShortfalls: Record<number, number> | null;
            calibrations: Record<number, number> | null;
            weights: Record<number, number> | null;
        } | null;
    };
    isUsingAttributeWeights: boolean;
    eventTypeRedirectUrl: string;
}>;
export default findTeamMembersMatchingAttributeLogicOfRouteHandler;
