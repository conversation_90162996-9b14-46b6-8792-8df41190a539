import type { z } from "zod";
import type { workspacePlatformUpdateServiceAccountSchema } from "./schema";
export default function updateServiceAccountHandler({ input, }: {
    input: z.infer<typeof workspacePlatformUpdateServiceAccountSchema>;
}): Promise<Omit<{
    name: string;
    id: number;
    description: string;
    createdAt: Date;
    updatedAt: Date;
    slug: string;
    enabled: boolean;
    defaultServiceAccountKey: import(".prisma/client").Prisma.JsonValue;
}, "defaultServiceAccountKey"> & {
    defaultServiceAccountKey: undefined;
}>;
