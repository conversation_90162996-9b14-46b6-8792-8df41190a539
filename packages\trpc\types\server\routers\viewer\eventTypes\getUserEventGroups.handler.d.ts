import type { PrismaClient } from "@calcom/prisma";
import { MembershipRole } from "@calcom/prisma/enums";
import type { TrpcSessionUser } from "../../../types";
import type { TEventTypeInputSchema } from "./getByViewer.schema";
type GetByViewerOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
        prisma: PrismaClient;
    };
    input: TEventTypeInputSchema;
};
export declare const getUserEventGroups: ({ ctx, input }: GetByViewerOptions) => Promise<{
    eventTypeGroups: {
        teamId?: number | null;
        parentId?: number | null;
        bookerUrl: string;
        membershipRole?: MembershipRole | null;
        profile: {
            slug: (string | null) | null;
            name: string | null;
            image: string;
            eventTypesLockedByOrg?: boolean;
        };
        metadata: {
            membershipCount: number;
            readOnly: boolean;
        };
    }[];
    profiles: {
        teamId: number | null | undefined;
        membershipRole: MembershipRole | null | undefined;
        membershipCount: number;
        readOnly: boolean;
        slug: (string | null) | null;
        name: string | null;
        image: string;
        eventTypesLockedByOrg?: boolean;
    }[];
}>;
export declare function compareMembership(mship1: MembershipRole, mship2: MembershipRole): boolean;
export {};
