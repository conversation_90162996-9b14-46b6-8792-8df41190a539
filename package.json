{"name": "calcom-monorepo", "version": "0.0.0", "private": true, "workspaces": ["apps/*", "apps/api/*", "packages/*", "packages/embeds/*", "packages/features/*", "packages/app-store/*", "packages/app-store/ee/*", "packages/platform/*", "packages/platform/examples/base", "example-apps/*"], "scripts": {"app-store-cli": "yarn workspace @calcom/app-store-cli", "app-store:build": "yarn app-store-cli build", "app-store:watch": "yarn app-store-cli watch", "app-store": "yarn app-store-cli cli", "create-app": "yarn app-store create", "edit-app": "yarn app-store edit", "withEmbedPublishEnv": "NEXT_PUBLIC_EMBED_LIB_URL='https://app.cal.com/embed/embed.js' NEXT_PUBLIC_WEBAPP_URL='https://app.cal.com' yarn", "publish-embed": "yarn withEmbedPublishEnv workspace @calcom/embed-core build && yarn withEmbedPublishEnv workspace @calcom/embed-snippet build && yarn workspaces foreach --from=\"@calcom/embed*\" npm publish --access=public", "delete-app": "yarn app-store delete", "create-app-template": "yarn app-store create-template", "edit-app-template": "yarn app-store edit-template", "delete-app-template": "yarn app-store delete-template", "build": "turbo run build --filter=@calcom/web...", "build:ai": "turbo run build --filter=\"@calcom/ai\"", "clean": "find . -name node_modules -o -name .next -o -name .turbo -o -name dist -type d -prune | xargs rm -rf", "db-deploy": "turbo run db-deploy", "db-seed": "turbo run db-seed", "db-studio": "yarn prisma studio", "deploy": "turbo run deploy", "dev:all": "turbo run dev --filter=\"@calcom/web\" --filter=\"@calcom/website\" --filter=\"@calcom/console\"", "dev:ai": "turbo run dev --filter=\"@calcom/web\" --filter=\"@calcom/api-proxy\" --filter=\"@calcom/api\" --filter=\"@calcom/ai\"", "dev:api": "turbo run dev --filter=\"@calcom/web\" --filter=\"@calcom/api-proxy\" --filter=\"@calcom/api\"", "dev:api:console": "turbo run dev --filter=\"@calcom/web\" --filter=\"@calcom/api-proxy\" --filter=\"@calcom/api\" --filter=\"@calcom/console\"", "dev:console": "turbo run dev --filter=\"@calcom/web\" --filter=\"@calcom/console\"", "dev:swagger": "turbo run dev --filter=\"@calcom/api-proxy\" --filter=\"@calcom/api\" --filter=\"@calcom/swagger\"", "dev:website": "turbo run dev --filter=\"@calcom/web\" --filter=\"@calcom/website\"", "dev": "turbo run dev --filter=\"@calcom/web\"", "build-storybook": "turbo run build --filter=\"@calcom/storybook\"", "dx": "turbo run dx", "i-dev": "infisical run -- turbo run dev --filter=\"@calcom/web\"", "i-dx": "infisical run -- turbo run dx", "i-gen-web-example-env": "infisical secrets generate-example-env --tags=web > .env.example", "i-gen-app-store-example-env": "infisical secrets generate-example-env --tags=appstore > .env.appStore.example", "embed-tests-quick": "turbo run embed-tests-quick", "embed-tests": "turbo run embed-tests", "env-check:app-store": "dotenv-checker --schema .env.appStore.example --env .env.appStore", "env-check:common": "dotenv-checker --schema .env.example --env .env", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "heroku-postbuild": "turbo run @calcom/web#build", "lint:fix": "turbo run lint:fix", "lint:report": "turbo run lint:report", "lint": "turbo run lint", "postinstall": "husky install && turbo run post-install", "pre-commit": "lint-staged", "predev": "echo 'Checking env files'", "prisma": "yarn workspace @calcom/prisma prisma", "start": "turbo run start --filter=\"@calcom/web\"", "tdd": "vitest watch", "e2e": "cross-env NEXT_PUBLIC_IS_E2E=1 yarn playwright test --project=@calcom/web", "e2e:app-store": "NEXT_PUBLIC_IS_E2E=1 QUICK=true yarn playwright test --project=@calcom/app-store", "e2e:embed": "NEXT_PUBLIC_IS_E2E=1 yarn playwright test --project=@calcom/embed-core", "e2e:embed-react": "QUICK=true yarn playwright test --project=@calcom/embed-react", "test-e2e": "yarn db-seed && yarn e2e", "test-e2e:app-store": "yarn db-seed && yarn e2e:app-store", "test-e2e:embed": "yarn db-seed && yarn e2e:embed", "test-e2e:embed-react": "yarn db-seed && yarn e2e:embed-react", "test-playwright": "yarn playwright test --config=playwright.config.ts", "test": "vitest run", "test:ui": "vitest --ui", "type-check": "turbo run type-check", "type-check:ci": "turbo run type-check:ci --log-prefix=none", "web": "yarn workspace @calcom/web", "docker-build-api": "docker build -t cal-api -f ./infra/docker/api/Dockerfile .", "docker-run-api": "docker run -p 80:80 cal-api", "docker-stop-api": "docker ps --filter 'ancestor=cal-api' -q | xargs docker stop", "changesets-add": "yarn changeset add", "changesets-version": "yarn changeset version", "changesets-release": "NODE_OPTIONS='--max_old_space_size=12288' turbo run build --filter=@calcom/atoms && yarn changeset publish"}, "devDependencies": {"@changesets/changelog-github": "^0.5.1", "@changesets/cli": "2.29.4", "@jetstreamapp/soql-parser-js": "^6.1.0", "@playwright/test": "^1.45.3", "@snaplet/copycat": "^4.1.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^16.0.1", "@vitest/ui": "^2.1.1", "c8": "^7.13.0", "checkly": "latest", "dotenv-checker": "^1.1.5", "husky": "^8.0.0", "i18n-unused": "^0.13.0", "jest-diff": "^29.5.0", "jest-summarizing-reporter": "^1.1.4", "lint-staged": "^12.5.0", "mailhog": "^4.16.0", "next-router-mock": "^0.9.12", "node-gyp": "^10.2.0", "node-ical": "^0.16.1", "prettier": "^2.8.6", "prismock": "^1.33.4", "resize-observer-polyfill": "^1.5.1", "tsc-absolute": "^1.0.0", "typescript": "5.9.0-beta", "vitest": "^2.1.1", "vitest-fetch-mock": "^0.3.0", "vitest-mock-extended": "^2.0.2"}, "dependencies": {"@daily-co/daily-js": "^0.83.1", "@evyweb/ioctopus": "^1.2.0", "@next/third-parties": "^14.2.5", "@vercel/functions": "^1.4.0", "city-timezones": "^1.2.1", "date-fns-tz": "^3.2.0", "eslint": "^8.34.0", "p-limit": "^6.2.0", "turbo": "^2.5.5"}, "resolutions": {"types-ramda": "0.29.4", "@apidevtools/json-schema-ref-parser": "9.0.9", "@types/react": "18.0.26", "@types/react-dom": "^18.0.9", "@types/node": "^20.17.23", "next-i18next@^13.2.2": "patch:next-i18next@npm%3A13.3.0#./.yarn/patches/next-i18next-npm-13.3.0-bf25b0943c.patch", "libphonenumber-js": "patch:libphonenumber-js@1.11.18#./.yarn/patches/libphonenumber-js*****.18.patch", "dayjs@1.11.2": "patch:dayjs@npm%3A1.11.4#./.yarn/patches/dayjs-npm-1.11.4-97921cd375.patch", "dayjs@^1": "patch:dayjs@npm%3A1.11.4#./.yarn/patches/dayjs-npm-1.11.4-97921cd375.patch", "dayjs@^1.8.29": "patch:dayjs@npm%3A1.11.4#./.yarn/patches/dayjs-npm-1.11.4-97921cd375.patch", "@prisma/client@^5.4.2": "patch:@prisma/client@npm%3A5.4.2#./.yarn/patches/@prisma-client-npm-5.4.2-fca489b2dc.patch", "@prisma/client@5.4.2": "patch:@prisma/client@npm%3A5.4.2#./.yarn/patches/@prisma-client-npm-5.4.2-fca489b2dc.patch"}, "lint-staged": {"(apps|packages)/**/*.{js,ts,jsx,tsx}": ["prettier --write", "eslint --fix"], "*.json": ["prettier --write"], "packages/prisma/schema.prisma": ["prisma format"]}, "engines": {"npm": ">=7.0.0", "yarn": "3.4.1"}, "prisma": {"schema": "packages/prisma/schema.prisma", "seed": "ts-node --transpile-only ./packages/prisma/seed.ts"}, "packageManager": "yarn@3.4.1", "syncpack": {"filter": "^(?!@calcom).*", "semverRange": ""}}