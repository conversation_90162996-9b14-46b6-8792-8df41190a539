export declare const get: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
    input: {
        includePasswordAdded?: boolean | undefined;
    } | undefined;
    output: {
        isTeamAdminOrOwner: boolean;
        passwordAdded?: boolean | undefined;
        secondaryEmails: {
            id: number;
            email: string;
            emailVerified: Date | null;
        }[];
        isPremium: boolean | undefined;
        organizationId: null;
        organization: {
            id: number;
            isPlatform: boolean;
            slug: string;
            isOrgAdmin: boolean;
        };
        username: string | null;
        profile: import("@calcom/types/UserProfile").UserAsPersonalProfile;
        profiles: never[];
        organizationSettings?: undefined;
        id: number;
        name: string | null;
        email: string;
        emailMd5: string;
        emailVerified: Date | null;
        startTime: number;
        endTime: number;
        bufferTime: number;
        locale: string;
        timeFormat: number | null;
        timeZone: string;
        avatar: string;
        avatarUrl: string | null;
        createdDate: Date;
        trialEndsAt: Date | null;
        defaultScheduleId: number | null;
        completedOnboarding: boolean;
        twoFactorEnabled: boolean;
        disableImpersonation: boolean;
        identityProvider: import(".prisma/client").$Enums.IdentityProvider;
        identityProviderEmail: string;
        brandColor: string | null;
        darkBrandColor: string | null;
        bio: string | null;
        weekStart: string;
        theme: string | null;
        appTheme: string | null;
        hideBranding: boolean;
        metadata: import(".prisma/client").Prisma.JsonValue;
        defaultBookerLayouts: {
            enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
            defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
        } | null;
        allowDynamicBooking: boolean | null;
        allowSEOIndexing: boolean | null;
        receiveMonthlyDigestEmail: boolean | null;
    } | {
        isTeamAdminOrOwner: boolean;
        passwordAdded?: boolean | undefined;
        secondaryEmails: {
            id: number;
            email: string;
            emailVerified: Date | null;
        }[];
        isPremium: boolean | undefined;
        organizationId: number | null;
        organization: {
            id: number | null;
            isOrgAdmin: boolean;
            metadata: {
                defaultConferencingApp?: {
                    appSlug?: string | undefined;
                    appLink?: string | undefined;
                } | undefined;
                requestedSlug?: string | null | undefined;
                paymentId?: string | undefined;
                subscriptionId?: string | null | undefined;
                subscriptionItemId?: string | null | undefined;
                orgSeats?: number | null | undefined;
                orgPricePerSeat?: number | null | undefined;
                migratedToOrgFrom?: {
                    teamSlug?: string | null | undefined;
                    lastMigrationTime?: string | undefined;
                    reverted?: boolean | undefined;
                    lastRevertTime?: string | undefined;
                } | undefined;
                billingPeriod?: import("@calcom/prisma/zod-utils").BillingPeriod | undefined;
            } | null;
            requestedSlug: string | null;
            name?: string | undefined;
            organizationSettings?: {
                allowSEOIndexing: boolean;
                lockEventTypeCreationForUsers: boolean;
            } | null | undefined;
            hideBranding?: boolean | undefined;
            slug?: string | null | undefined;
            logoUrl?: string | null | undefined;
            isPrivate?: boolean | undefined;
            bannerUrl?: string | null | undefined;
            isPlatform?: boolean | undefined;
        };
        username: string | null;
        profile: import("@calcom/types/UserProfile").UserAsPersonalProfile | {
            name: string | null;
            avatarUrl: string | null;
            startTime: number;
            endTime: number;
            bufferTime: number;
            username: string | null;
            upId: string;
            id: null;
            organizationId: null;
            organization: null;
        } | {
            name: string | null;
            avatarUrl: string | null;
            startTime: number;
            endTime: number;
            bufferTime: number;
            user: {
                name: string | null;
                id: number;
                locale: string | null;
                startTime: number;
                endTime: number;
                email: string;
                username: string | null;
                avatarUrl: string | null;
                bufferTime: number;
                defaultScheduleId: number | null;
                isPlatformManaged: boolean;
            };
            organization: {
                name: string;
                id: number;
                metadata: import(".prisma/client").Prisma.JsonValue;
                organizationSettings: {
                    allowSEOIndexing: boolean;
                    lockEventTypeCreationForUsers: boolean;
                } | null;
                hideBranding: boolean;
                slug: string | null;
                logoUrl: string | null;
                isPrivate: boolean;
                bannerUrl: string | null;
                isPlatform: boolean;
                members: {
                    id: number;
                    role: import(".prisma/client").$Enums.MembershipRole;
                    userId: number;
                    disableImpersonation: boolean;
                    teamId: number;
                    accepted: boolean;
                }[];
            } & Omit<Pick<{
                id: number;
                name: string;
                slug: string | null;
                logoUrl: string | null;
                calVideoLogo: string | null;
                appLogo: string | null;
                appIconLogo: string | null;
                bio: string | null;
                hideBranding: boolean;
                hideTeamProfileLink: boolean;
                isPrivate: boolean;
                hideBookATeamMember: boolean;
                createdAt: Date;
                metadata: import(".prisma/client").Prisma.JsonValue | null;
                theme: string | null;
                rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
                rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
                brandColor: string | null;
                darkBrandColor: string | null;
                bannerUrl: string | null;
                parentId: number | null;
                timeFormat: number | null;
                timeZone: string;
                weekStart: string;
                isOrganization: boolean;
                pendingPayment: boolean;
                isPlatform: boolean;
                createdByOAuthClientId: string | null;
                smsLockState: import(".prisma/client").$Enums.SMSLockState;
                smsLockReviewedByAdmin: boolean;
                bookingLimits: import(".prisma/client").Prisma.JsonValue | null;
                includeManagedEventsInLimits: boolean;
            }, "name" | "id" | "metadata" | "hideBranding" | "slug" | "logoUrl" | "bannerUrl" | "isPlatform">, "metadata"> & {
                requestedSlug: string | null;
                metadata: {
                    requestedSlug: string | null;
                    defaultConferencingApp?: {
                        appSlug?: string | undefined;
                        appLink?: string | undefined;
                    } | undefined;
                    paymentId?: string | undefined;
                    subscriptionId?: string | null | undefined;
                    subscriptionItemId?: string | null | undefined;
                    orgSeats?: number | null | undefined;
                    orgPricePerSeat?: number | null | undefined;
                    migratedToOrgFrom?: {
                        teamSlug?: string | null | undefined;
                        lastMigrationTime?: string | undefined;
                        reverted?: boolean | undefined;
                        lastRevertTime?: string | undefined;
                    } | undefined;
                    billingPeriod?: import("@calcom/prisma/zod-utils").BillingPeriod | undefined;
                };
            };
            movedFromUser: {
                id: number;
            } | null;
            id: number;
            userId: number;
            uid: string;
            createdAt: Date & string;
            updatedAt: Date & string;
            username: string;
            organizationId: number;
            upId: string;
        } | ({
            name: string | null;
            avatarUrl: string | null;
            startTime: number;
            endTime: number;
            bufferTime: number;
            username: string | null;
            upId: string;
            id: null;
            organizationId: null;
            organization: null;
        } & import("@calcom/types/UserProfile").UserAsPersonalProfile) | (import("@calcom/types/UserProfile").UserAsPersonalProfile & {
            name: string | null;
            avatarUrl: string | null;
            startTime: number;
            endTime: number;
            bufferTime: number;
            username: string | null;
            upId: string;
            id: null;
            organizationId: null;
            organization: null;
        });
        profiles: import("@calcom/types/UserProfile").UserProfile[];
        organizationSettings: {
            allowSEOIndexing: boolean;
            lockEventTypeCreationForUsers: boolean;
        } | null | undefined;
        id: number;
        name: string | null;
        email: string;
        emailMd5: string;
        emailVerified: Date | null;
        startTime: number;
        endTime: number;
        bufferTime: number;
        locale: string;
        timeFormat: number | null;
        timeZone: string;
        avatar: string;
        avatarUrl: string | null;
        createdDate: Date;
        trialEndsAt: Date | null;
        defaultScheduleId: number | null;
        completedOnboarding: boolean;
        twoFactorEnabled: boolean;
        disableImpersonation: boolean;
        identityProvider: import(".prisma/client").$Enums.IdentityProvider;
        identityProviderEmail: string;
        brandColor: string | null;
        darkBrandColor: string | null;
        bio: string | null;
        weekStart: string;
        theme: string | null;
        appTheme: string | null;
        hideBranding: boolean;
        metadata: import(".prisma/client").Prisma.JsonValue;
        defaultBookerLayouts: {
            enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
            defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
        } | null;
        allowDynamicBooking: boolean | null;
        allowSEOIndexing: boolean | null;
        receiveMonthlyDigestEmail: boolean | null;
    };
}>;
