import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TGetMembershipbyUserInputSchema } from "./getMembershipbyUser.schema";
type GetMembershipbyUserOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TGetMembershipbyUserInputSchema;
};
export declare const getMembershipbyUserHandler: ({ ctx, input }: GetMembershipbyUserOptions) => Promise<{
    id: number;
    role: import(".prisma/client").$Enums.MembershipRole;
    userId: number;
    createdAt: Date | null;
    updatedAt: Date | null;
    disableImpersonation: boolean;
    teamId: number;
    accepted: boolean;
    customRoleId: string | null;
} | null>;
export default getMembershipbyUserHandler;
