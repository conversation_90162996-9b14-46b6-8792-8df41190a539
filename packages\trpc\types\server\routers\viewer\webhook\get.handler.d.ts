import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TGetInputSchema } from "./get.schema";
type GetOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TGetInputSchema;
};
export declare const getHandler: ({ ctx: _ctx, input }: GetOptions) => Promise<{
    id: string;
    time: number | null;
    userId: number | null;
    teamId: number | null;
    secret: string | null;
    active: boolean;
    platform: boolean;
    subscriberUrl: string;
    payloadTemplate: string | null;
    eventTriggers: import(".prisma/client").$Enums.WebhookTriggerEvents[];
    timeUnit: import(".prisma/client").$Enums.TimeUnit | null;
}>;
export {};
