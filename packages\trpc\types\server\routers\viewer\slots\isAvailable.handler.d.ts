import type { NextApiRequest } from "next";
import type { PrismaClient } from "@calcom/prisma";
import type { TIsAvailableInputSchema, TIsAvailableOutputSchema } from "./isAvailable.schema";
interface IsAvailableOptions {
    ctx: {
        prisma: PrismaClient;
        req?: NextApiRequest | undefined;
    };
    input: TIsAvailableInputSchema;
}
/**
 * It does a super quick check whether that slot is bookable or not.
 * It doesn't consider slow things like querying the bookings, checking the calendars.
 *
 * getSchedule call is the only(but very slow) way to know if a slot is bookable
 */
export declare const isAvailableHandler: ({ ctx, input, }: IsAvailableOptions) => Promise<TIsAvailableOutputSchema>;
export {};
