import { z } from "zod";
export declare const editAttributeSchema: z.ZodObject<{
    attributeId: z.ZodString;
    name: z.ZodString;
    type: z.ZodEnum<["TEXT", "NUMBER", "SINGLE_SELECT", "MULTI_SELECT"]>;
    isLocked: z.<PERSON><z.ZodBoolean>;
    isWeightsEnabled: z.Zod<PERSON>ptional<z.ZodBoolean>;
    options: z.<PERSON><z.ZodObject<{
        value: z.ZodString;
        id: z.ZodOptional<z.ZodString>;
        isGroup: z.ZodOptional<z.ZodBoolean>;
        contains: z.ZodOptional<z.<PERSON><PERSON>rray<z.ZodString, "many">>;
    }, "strip", z.ZodTypeAny, {
        value: string;
        id?: string | undefined;
        isGroup?: boolean | undefined;
        contains?: string[] | undefined;
    }, {
        value: string;
        id?: string | undefined;
        isGroup?: boolean | undefined;
        contains?: string[] | undefined;
    }>, "many">;
}, "strip", z.Z<PERSON>ypeAny, {
    name: string;
    type: "TEXT" | "NUMBER" | "SINGLE_SELECT" | "MULTI_SELECT";
    options: {
        value: string;
        id?: string | undefined;
        isGroup?: boolean | undefined;
        contains?: string[] | undefined;
    }[];
    attributeId: string;
    isLocked?: boolean | undefined;
    isWeightsEnabled?: boolean | undefined;
}, {
    name: string;
    type: "TEXT" | "NUMBER" | "SINGLE_SELECT" | "MULTI_SELECT";
    options: {
        value: string;
        id?: string | undefined;
        isGroup?: boolean | undefined;
        contains?: string[] | undefined;
    }[];
    attributeId: string;
    isLocked?: boolean | undefined;
    isWeightsEnabled?: boolean | undefined;
}>;
export type ZEditAttributeSchema = z.infer<typeof editAttributeSchema>;
