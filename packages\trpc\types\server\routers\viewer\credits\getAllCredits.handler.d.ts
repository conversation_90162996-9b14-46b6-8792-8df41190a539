import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TGetAllCreditsSchema } from "./getAllCredits.schema";
type GetAllCreditsOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TGetAllCreditsSchema;
};
export declare const getAllCreditsHandler: ({ ctx, input }: GetAllCreditsOptions) => Promise<{
    credits: {
        totalMonthlyCredits: number;
        totalRemainingMonthlyCredits: number;
        additionalCredits: number;
    };
} | null>;
export {};
