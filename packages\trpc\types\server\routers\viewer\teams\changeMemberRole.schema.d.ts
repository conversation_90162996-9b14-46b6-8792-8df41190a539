import { z } from "zod";
export declare const ZChangeMemberRoleInputSchema: z.ZodObject<{
    teamId: z.ZodNumber;
    memberId: z.<PERSON>odNumber;
    role: z.<PERSON>odU<PERSON><[z.ZodNativeEnum<{
        readonly MEMBER: "MEMBER";
        readonly ADMIN: "ADMIN";
        readonly OWNER: "OWNER";
    }>, z.ZodString]>;
}, "strip", z.<PERSON>od<PERSON>ype<PERSON>ny, {
    role: string;
    teamId: number;
    memberId: number;
}, {
    role: string;
    teamId: number;
    memberId: number;
}>;
export type TChangeMemberRoleInputSchema = z.infer<typeof ZChangeMemberRoleInputSchema>;
