import type { z } from "zod";
import { ZFindTeamMembersMatchingAttributeLogicInputSchema } from "./findTeamMembersMatchingAttributeLogic.schema";
export type TFindTeamMembersMatchingAttributeLogicInputSchema = z.infer<typeof ZFindTeamMembersMatchingAttributeLogicInputSchema>;
export declare const attributesRouter: import("@trpc/server/unstable-core-do-not-import").CreateRouterInner<import("@trpc/server/unstable-core-do-not-import").RootConfig<{
    ctx: import("../../../createContext").InnerContext;
    meta: object;
    errorShape: {
        message: string;
        code: number;
        data: {
            code: string;
            httpStatus: number;
            path?: string;
            [key: string]: unknown;
        };
    };
    transformer: {
        stringify: (object: any) => string;
        parse: <T = unknown>(string: string) => T;
        serialize: (object: any) => import("superjson/dist/types").SuperJSONResult;
        deserialize: <T_1 = unknown>(payload: import("superjson/dist/types").SuperJSONResult) => T_1;
        registerClass: (v: import("superjson/dist/types").Class, options?: string | import("superjson/dist/class-registry").RegisterOptions | undefined) => void;
        registerSymbol: (v: Symbol, identifier?: string | undefined) => void;
        registerCustom: <I, O extends import("superjson/dist/types").JSONValue>(transformer: Omit<import("superjson/dist/custom-transformer-registry").CustomTransfomer<I, O>, "name">, name: string) => void;
        allowErrorProps: (...props: string[]) => void;
    };
}>, {
    list: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: ({
            options: {
                id: string;
                contains: string[];
                value: string;
                slug: string;
                attributeId: string;
                isGroup: boolean;
            }[];
        } & {
            name: string;
            id: string;
            type: import(".prisma/client").$Enums.AttributeType;
            createdAt: Date;
            updatedAt: Date;
            slug: string;
            teamId: number;
            enabled: boolean;
            usersCanEditRelation: boolean;
            isWeightsEnabled: boolean;
            isLocked: boolean;
        })[];
    }>;
    get: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            id: string;
        };
        output: {
            name: string;
            id: string;
            type: "TEXT" | "NUMBER" | "SINGLE_SELECT" | "MULTI_SELECT";
            options: {
                value: string;
                id?: string | undefined;
                isGroup?: boolean | undefined;
                assignedUsers?: number | undefined;
                contains?: string[] | undefined;
            }[];
            isLocked?: boolean | undefined;
            isWeightsEnabled?: boolean | undefined;
        };
    }>;
    getByUserId: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            userId: number;
        };
        output: import("./getByUserId.handler").GroupedAttribute[];
    }>;
    create: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            name: string;
            type: "TEXT" | "NUMBER" | "SINGLE_SELECT" | "MULTI_SELECT";
            options: {
                value: string;
                isGroup?: boolean | undefined;
            }[];
            isLocked?: boolean | undefined;
        };
        output: {
            id: string;
            teamId: number;
            type: import(".prisma/client").$Enums.AttributeType;
            name: string;
            slug: string;
            enabled: boolean;
            usersCanEditRelation: boolean;
            createdAt: Date;
            updatedAt: Date;
            isWeightsEnabled: boolean;
            isLocked: boolean;
        };
    }>;
    edit: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            name: string;
            type: "TEXT" | "NUMBER" | "SINGLE_SELECT" | "MULTI_SELECT";
            options: {
                value: string;
                id?: string | undefined;
                isGroup?: boolean | undefined;
                contains?: string[] | undefined;
            }[];
            attributeId: string;
            isLocked?: boolean | undefined;
            isWeightsEnabled?: boolean | undefined;
        };
        output: {
            id: string;
        };
    }>;
    delete: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            id: string;
        };
        output: {
            name: string;
            id: string;
            type: import(".prisma/client").$Enums.AttributeType;
            createdAt: Date;
            updatedAt: Date;
            slug: string;
            teamId: number;
            enabled: boolean;
            usersCanEditRelation: boolean;
            isWeightsEnabled: boolean;
            isLocked: boolean;
        };
    }>;
    toggleActive: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            attributeId: string;
        };
        output: {
            enabled: boolean;
            id: string;
        };
    }>;
    assignUserToAttribute: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            userId: number;
            attributes: {
                id: string;
                options?: {
                    value: string;
                    label?: string | undefined;
                    weight?: number | undefined;
                }[] | undefined;
                value?: string | undefined;
            }[];
        };
        output: {
            success: boolean;
            message: string;
        };
    }>;
    bulkAssignAttributes: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            attributes: {
                id: string;
                options?: {
                    value: string;
                    label?: string | undefined;
                }[] | undefined;
                value?: string | undefined;
            }[];
            userIds: number[];
        };
        output: {
            success: boolean;
            message: string;
            results: {
                userId: number;
                success: boolean;
                message?: string;
            }[];
        };
    }>;
    findTeamMembersMatchingAttributeLogic: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            teamId: number;
            attributesQueryValue: {
                type: "group";
                id?: string | undefined;
                children1?: Record<string, {
                    type?: string | undefined;
                    properties?: {
                        field?: any;
                        operator?: any;
                        value?: any;
                        valueSrc?: any;
                        valueError?: (string | null)[] | undefined;
                        valueType?: any;
                    } | undefined;
                }> | undefined;
                properties?: any;
            } | {
                type: "switch_group";
                id?: string | undefined;
                children1?: Record<string, {
                    type?: string | undefined;
                    properties?: {
                        field?: any;
                        operator?: any;
                        value?: any;
                        valueSrc?: any;
                        valueError?: (string | null)[] | undefined;
                        valueType?: any;
                    } | undefined;
                }> | undefined;
                properties?: any;
            } | null;
            isPreview?: boolean | undefined;
            _enablePerf?: boolean | undefined;
            _concurrency?: number | undefined;
        };
        output: {
            troubleshooter: {
                type: import("@calcom/lib/raqb/findTeamMembersMatchingAttributeLogic").TroubleshooterCase;
                data: Record<string, any>;
            } | undefined;
            mainWarnings: string[] | null;
            fallbackWarnings: string[] | null;
            result: null;
        } | {
            mainWarnings: string[] | null;
            fallbackWarnings: string[] | null;
            troubleshooter: {
                type: import("@calcom/lib/raqb/findTeamMembersMatchingAttributeLogic").TroubleshooterCase;
                data: Record<string, any>;
            } | undefined;
            result: {
                id: number;
                name: string | null;
                email: string;
            }[];
        };
    }>;
}>;
