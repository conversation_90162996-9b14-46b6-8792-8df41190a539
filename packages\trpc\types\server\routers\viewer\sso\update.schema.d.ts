import { z } from "zod";
export declare const ZUpdateInputSchema: z.ZodObject<{
    encodedRawMetadata: z.ZodString;
    teamId: z.<PERSON><[z.<PERSON><PERSON>, z.ZodN<PERSON>]>;
}, "strip", z.<PERSON><PERSON>, {
    teamId: number | null;
    encodedRawMetadata: string;
}, {
    teamId: number | null;
    encodedRawMetadata: string;
}>;
export type TUpdateInputSchema = z.infer<typeof ZUpdateInputSchema>;
