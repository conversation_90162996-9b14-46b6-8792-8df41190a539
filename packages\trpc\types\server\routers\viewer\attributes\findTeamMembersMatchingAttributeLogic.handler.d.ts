import type { ServerResponse } from "http";
import type { NextApiResponse } from "next";
import type { PrismaClient } from "@calcom/prisma";
import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TFindTeamMembersMatchingAttributeLogicInputSchema } from "./findTeamMembersMatchingAttributeLogic.schema";
interface FindTeamMembersMatchingAttributeLogicHandlerOptions {
    ctx: {
        prisma: PrismaClient;
        user: NonNullable<TrpcSessionUser>;
        res: ServerResponse | NextApiResponse | undefined;
    };
    input: TFindTeamMembersMatchingAttributeLogicInputSchema;
}
export declare const findTeamMembersMatchingAttributeLogicHandler: ({ ctx, input, }: FindTeamMembersMatchingAttributeLogicHandlerOptions) => Promise<{
    troubleshooter: {
        type: import("@calcom/lib/raqb/findTeamMembersMatchingAttributeLogic").TroubleshooterCase;
        data: Record<string, any>;
    } | undefined;
    mainWarnings: string[] | null;
    fallbackWarnings: string[] | null;
    result: null;
} | {
    mainWarnings: string[] | null;
    fallbackWarnings: string[] | null;
    troubleshooter: {
        type: import("@calcom/lib/raqb/findTeamMembersMatchingAttributeLogic").TroubleshooterCase;
        data: Record<string, any>;
    } | undefined;
    result: {
        id: number;
        name: string | null;
        email: string;
    }[];
}>;
export default findTeamMembersMatchingAttributeLogicHandler;
