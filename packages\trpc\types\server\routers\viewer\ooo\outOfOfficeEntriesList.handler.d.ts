import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import { type TOutOfOfficeEntriesListSchema } from "./outOfOfficeEntriesList.schema";
type GetOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TOutOfOfficeEntriesListSchema;
};
export declare const outOfOfficeEntriesList: ({ ctx, input }: GetOptions) => Promise<{
    rows: {
        canEditAndDelete: boolean;
        user: {
            name: string | null;
            id: number;
            role: import(".prisma/client").$Enums.UserPermissionRole;
            metadata: import(".prisma/client").Prisma.JsonValue;
            locale: string | null;
            startTime: number;
            endTime: number;
            creationSource: import(".prisma/client").$Enums.CreationSource | null;
            email: string;
            movedToProfileId: number | null;
            username: string | null;
            emailVerified: Date | null;
            bio: string | null;
            avatarUrl: string | null;
            timeZone: string;
            weekStart: string;
            bufferTime: number;
            hideBranding: boolean;
            theme: string | null;
            appTheme: string | null;
            createdDate: Date;
            trialEndsAt: Date | null;
            lastActiveAt: Date | null;
            defaultScheduleId: number | null;
            completedOnboarding: boolean;
            timeFormat: number | null;
            twoFactorSecret: string | null;
            twoFactorEnabled: boolean;
            backupCodes: string | null;
            identityProvider: import(".prisma/client").$Enums.IdentityProvider;
            identityProviderId: string | null;
            invitedTo: number | null;
            brandColor: string | null;
            darkBrandColor: string | null;
            allowDynamicBooking: boolean | null;
            allowSEOIndexing: boolean | null;
            receiveMonthlyDigestEmail: boolean | null;
            verified: boolean | null;
            disableImpersonation: boolean;
            organizationId: number | null;
            locked: boolean;
            isPlatformManaged: boolean;
            smsLockState: import(".prisma/client").$Enums.SMSLockState;
            smsLockReviewedByAdmin: boolean;
            referralLinkId: string | null;
            whitelistWorkflows: boolean;
        };
        id: number;
        end: Date;
        notes: string | null;
        reason: {
            id: number;
            userId: number | null;
            reason: string;
            emoji: string;
        } | null;
        uuid: string;
        start: Date;
        toUser: {
            name: string | null;
            email: string;
            username: string | null;
        } | null;
        toUserId: number | null;
    }[];
    nextCursor: number | undefined;
    meta: {
        totalRowCount: number;
    };
}>;
export {};
