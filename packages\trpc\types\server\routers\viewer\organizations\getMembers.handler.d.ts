import type { TrpcSessionUser } from "../../../types";
import type { TGetMembersInputSchema } from "./getMembers.schema";
type CreateOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TGetMembersInputSchema;
};
export declare const getMembersHandler: ({ input, ctx }: CreateOptions) => Promise<{
    user: {
        name: string | null;
        id: number;
        email: string;
        username: string | null;
        avatarUrl: string | null;
        completedOnboarding: boolean;
    };
    id: number;
    role: import(".prisma/client").$Enums.MembershipRole;
    userId: number;
    disableImpersonation: boolean;
    teamId: number;
    accepted: boolean;
}[]>;
export default getMembersHandler;
