import type { ZVerifyCodeInputSchema } from "@calcom/prisma/zod-utils";
import type { TrpcSessionUser } from "@calcom/trpc/server/types";
type VerifyCodeOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: ZVerifyCodeInputSchema;
};
export declare const verifyCodeHandler: ({ ctx, input }: VerifyCodeOptions) => Promise<true>;
export declare const verifyCode: ({ user, email, code, }: {
    user?: Pick<NonNullable<TrpcSessionUser>, "role">;
    email?: string;
    code?: string;
}) => Promise<true>;
export default verifyCodeHandler;
