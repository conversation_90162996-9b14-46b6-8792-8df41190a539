import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TUpdateInputSchema } from "./update.schema";
type User = NonNullable<TrpcSessionUser>;
type UpdateOptions = {
    ctx: {
        user: {
            id: User["id"];
            defaultScheduleId: User["defaultScheduleId"];
            timeZone: User["timeZone"];
        };
    };
    input: TUpdateInputSchema;
};
export declare const updateHandler: ({ input, ctx }: UpdateOptions) => Promise<{
    schedule: {
        name: string;
        id: number;
        userId: number;
    };
    isDefault: boolean;
    availability?: undefined;
    timeZone?: undefined;
    prevDefaultId?: undefined;
    currentDefaultId?: undefined;
} | {
    schedule: {
        name: string;
        id: number;
        userId: number;
        eventType: {
            id: number;
            eventName: string | null;
        }[];
        availability: {
            id: number;
            date: Date | null;
            startTime: Date;
            endTime: Date;
            userId: number | null;
            eventTypeId: number | null;
            scheduleId: number | null;
            days: number[];
        }[];
        timeZone: string | null;
    };
    availability: import("@calcom/types/schedule").Schedule;
    timeZone: string;
    isDefault: boolean;
    prevDefaultId: number | null;
    currentDefaultId: number | null;
}>;
export {};
