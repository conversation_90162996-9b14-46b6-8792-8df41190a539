import { z } from "zod";
export declare const ZUpdateMembershipInputSchema: z.ZodObject<{
    teamId: z.ZodNumber;
    memberId: z.ZodNumber;
    disableImpersonation: z.ZodBoolean;
}, "strip", z.<PERSON><PERSON><PERSON><PERSON>ny, {
    disableImpersonation: boolean;
    teamId: number;
    memberId: number;
}, {
    disableImpersonation: boolean;
    teamId: number;
    memberId: number;
}>;
export type TUpdateMembershipInputSchema = z.infer<typeof ZUpdateMembershipInputSchema>;
