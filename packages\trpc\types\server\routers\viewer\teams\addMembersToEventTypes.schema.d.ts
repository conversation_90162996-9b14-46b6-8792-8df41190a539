import { z } from "zod";
export declare const ZAddMembersToEventTypes: z.ZodObject<{
    userIds: z.<PERSON><z.ZodN<PERSON><PERSON>, "many">;
    teamId: z.ZodN<PERSON>ber;
    eventTypeIds: z.<PERSON><z.<PERSON>odN<PERSON>ber, "many">;
}, "strip", z.Z<PERSON>, {
    teamId: number;
    userIds: number[];
    eventTypeIds: number[];
}, {
    teamId: number;
    userIds: number[];
    eventTypeIds: number[];
}>;
export type TAddMembersToEventTypes = z.infer<typeof ZAddMembersToEventTypes>;
