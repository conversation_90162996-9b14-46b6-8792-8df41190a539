import { MembershipRole } from "@calcom/prisma/enums";
import type { CreationSource } from "@calcom/prisma/enums";
import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TInviteMemberInputSchema } from "./inviteMember.schema";
import type { TeamWithParent } from "./types";
type InviteMemberOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TInviteMemberInputSchema;
};
type TargetTeam = {
    teamId: number;
} | {
    team: TeamWithParent;
};
export declare const inviteMembersWithNoInviterPermissionCheck: (data: {
    language: string;
    inviterName: string | null;
    orgSlug: string | null;
    invitations: {
        usernameOrEmail: string;
        role: MembershipRole;
    }[];
    creationSource: CreationSource;
    /**
     * Whether invitation is a direct user action or not i.e. we need to show them User based errors like inviting existing users or not.
     */
    isDirectUserAction?: boolean;
} & TargetTeam) => Promise<{
    usernameOrEmail: string | string[];
    numUsersInvited: number;
}>;
export default function inviteMemberHandler({ ctx, input }: InviteMemberOptions): Promise<{
    usernameOrEmail: string | string[];
    numUsersInvited: number;
}>;
export {};
