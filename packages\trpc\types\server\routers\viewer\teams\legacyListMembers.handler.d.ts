import type { PrismaClient } from "@calcom/prisma";
import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TLegacyListMembersInputSchema } from "./legacyListMembers.schema";
type ListMembersOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
        prisma: PrismaClient;
    };
    input: TLegacyListMembersInputSchema;
};
export declare const legacyListMembers: ({ ctx, input }: ListMembersOptions) => Promise<{
    members: ({
        accepted: boolean;
        membershipId: number;
        name: string | null;
        id: number;
        email: string;
        username: string | null;
        avatarUrl: string | null;
    } & {
        nonProfileUsername: string | null;
        profile: import("@calcom/types/UserProfile").UserProfile;
    })[];
    nextCursor: number | undefined;
}>;
export default legacyListMembers;
