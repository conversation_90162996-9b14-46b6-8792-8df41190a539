import { z } from "zod";
import type { IntervalLimit } from "@calcom/lib/intervalLimits/intervalLimitSchema";
export type TUpdateInputSchema = {
    id: number;
    bio?: string;
    name?: string;
    logo?: string | null;
    slug?: string;
    hideBranding?: boolean;
    hideBookATeamMember?: boolean;
    hideTeamProfileLink?: boolean;
    isPrivate?: boolean;
    brandColor?: string;
    darkBrandColor?: string;
    theme?: string | null;
    bookingLimits?: IntervalLimit | null;
    includeManagedEventsInLimits?: boolean;
    rrResetInterval?: "DAY" | "MONTH";
    rrTimestampBasis?: "CREATED_AT" | "START_TIME";
};
export declare const ZUpdateInputSchema: z.Schema<TUpdateInputSchema>;
