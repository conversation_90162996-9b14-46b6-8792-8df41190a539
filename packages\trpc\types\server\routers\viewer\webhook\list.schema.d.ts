import { z } from "zod";
export declare const ZListInputSchema: z.<PERSON>odOptional<z.ZodObject<{
    id: z.ZodOptional<z.ZodString>;
    appId: z.ZodOptional<z.ZodString>;
    teamId: z.<PERSON>od<PERSON>ptional<z.<PERSON>odN<PERSON>ber>;
    eventTypeId: z.<PERSON>od<PERSON>ptional<z.ZodNumber>;
    eventTriggers: z.<PERSON>al<z.<PERSON><PERSON><PERSON><PERSON><z.Zod<PERSON>num<["BOOKING_CANCELLED", "BOOKING_CREATED", "BOOKING_RESCHEDULED", "BOOKING_PAID", "BOOKING_PAYMENT_INITIATED", "MEETING_ENDED", "MEETING_STARTED", "BOOKING_REQUESTED", "BOOKING_REJECTED", "RECORDING_READY", "INSTANT_MEETING", "RECORDING_TRANSCRIPTION_GENERATED", "BOOKING_NO_SHOW_UPDATED", "OOO_CREATED", "AFTER_HOSTS_CAL_VIDEO_NO_SHOW", "AFTER_GUESTS_CAL_VIDEO_NO_SHOW", "FORM_SUBMITTED", "FORM_SUBMITTED_NO_EVENT"]>, "many">>;
}, "strip", z.ZodTypeAny, {
    id?: string | undefined;
    appId?: string | undefined;
    teamId?: number | undefined;
    eventTypeId?: number | undefined;
    eventTriggers?: ("BOOKING_CREATED" | "BOOKING_PAYMENT_INITIATED" | "BOOKING_PAID" | "BOOKING_RESCHEDULED" | "BOOKING_REQUESTED" | "BOOKING_CANCELLED" | "BOOKING_REJECTED" | "BOOKING_NO_SHOW_UPDATED" | "FORM_SUBMITTED" | "MEETING_ENDED" | "MEETING_STARTED" | "RECORDING_READY" | "INSTANT_MEETING" | "RECORDING_TRANSCRIPTION_GENERATED" | "OOO_CREATED" | "AFTER_HOSTS_CAL_VIDEO_NO_SHOW" | "AFTER_GUESTS_CAL_VIDEO_NO_SHOW" | "FORM_SUBMITTED_NO_EVENT")[] | undefined;
}, {
    id?: string | undefined;
    appId?: string | undefined;
    teamId?: number | undefined;
    eventTypeId?: number | undefined;
    eventTriggers?: ("BOOKING_CREATED" | "BOOKING_PAYMENT_INITIATED" | "BOOKING_PAID" | "BOOKING_RESCHEDULED" | "BOOKING_REQUESTED" | "BOOKING_CANCELLED" | "BOOKING_REJECTED" | "BOOKING_NO_SHOW_UPDATED" | "FORM_SUBMITTED" | "MEETING_ENDED" | "MEETING_STARTED" | "RECORDING_READY" | "INSTANT_MEETING" | "RECORDING_TRANSCRIPTION_GENERATED" | "OOO_CREATED" | "AFTER_HOSTS_CAL_VIDEO_NO_SHOW" | "AFTER_GUESTS_CAL_VIDEO_NO_SHOW" | "FORM_SUBMITTED_NO_EVENT")[] | undefined;
}>>;
export type TListInputSchema = z.infer<typeof ZListInputSchema>;
