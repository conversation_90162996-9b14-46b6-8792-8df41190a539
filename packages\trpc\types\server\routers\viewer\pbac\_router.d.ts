export declare const permissionsRouter: import("@trpc/server/unstable-core-do-not-import").CreateRouterInner<import("@trpc/server/unstable-core-do-not-import").RootConfig<{
    ctx: import("../../../createContext").InnerContext;
    meta: object;
    errorShape: {
        message: string;
        code: number;
        data: {
            code: string;
            httpStatus: number;
            path?: string;
            [key: string]: unknown;
        };
    };
    transformer: {
        stringify: (object: any) => string;
        parse: <T = unknown>(string: string) => T;
        serialize: (object: any) => import("superjson/dist/types").SuperJSONResult;
        deserialize: <T_1 = unknown>(payload: import("superjson/dist/types").SuperJSONResult) => T_1;
        registerClass: (v: import("superjson/dist/types").Class, options?: string | import("superjson/dist/class-registry").RegisterOptions | undefined) => void;
        registerSymbol: (v: Symbol, identifier?: string | undefined) => void;
        registerCustom: <I, O extends import("superjson/dist/types").JSONValue>(transformer: Omit<import("superjson/dist/custom-transformer-registry").CustomTransfomer<I, O>, "name">, name: string) => void;
        allowErrorProps: (...props: string[]) => void;
    };
}>, {
    getUserPermissions: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: {};
    }>;
    checkPermission: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            teamId: number;
            permission: "*.*" | "*.create" | "*.read" | "*.update" | "*.delete" | "*.invite" | "*.remove" | "*.changeMemberRole" | "*.listMembers" | "*.manageBilling" | "*.readTeamBookings" | "*.readOrgBookings" | "*.readRecordings" | "eventType.*" | "eventType.create" | "eventType.read" | "eventType.update" | "eventType.delete" | "eventType.invite" | "eventType.remove" | "eventType.changeMemberRole" | "eventType.listMembers" | "eventType.manageBilling" | "eventType.readTeamBookings" | "eventType.readOrgBookings" | "eventType.readRecordings" | "team.*" | "team.create" | "team.read" | "team.update" | "team.delete" | "team.invite" | "team.remove" | "team.changeMemberRole" | "team.listMembers" | "team.manageBilling" | "team.readTeamBookings" | "team.readOrgBookings" | "team.readRecordings" | "organization.*" | "organization.create" | "organization.read" | "organization.update" | "organization.delete" | "organization.invite" | "organization.remove" | "organization.changeMemberRole" | "organization.listMembers" | "organization.manageBilling" | "organization.readTeamBookings" | "organization.readOrgBookings" | "organization.readRecordings" | "organization.attributes.*" | "organization.attributes.create" | "organization.attributes.read" | "organization.attributes.update" | "organization.attributes.delete" | "organization.attributes.invite" | "organization.attributes.remove" | "organization.attributes.changeMemberRole" | "organization.attributes.listMembers" | "organization.attributes.manageBilling" | "organization.attributes.readTeamBookings" | "organization.attributes.readOrgBookings" | "organization.attributes.readRecordings" | "booking.*" | "booking.create" | "booking.read" | "booking.update" | "booking.delete" | "booking.invite" | "booking.remove" | "booking.changeMemberRole" | "booking.listMembers" | "booking.manageBilling" | "booking.readTeamBookings" | "booking.readOrgBookings" | "booking.readRecordings" | "insights.*" | "insights.create" | "insights.read" | "insights.update" | "insights.delete" | "insights.invite" | "insights.remove" | "insights.changeMemberRole" | "insights.listMembers" | "insights.manageBilling" | "insights.readTeamBookings" | "insights.readOrgBookings" | "insights.readRecordings" | "role.*" | "role.create" | "role.read" | "role.update" | "role.delete" | "role.invite" | "role.remove" | "role.changeMemberRole" | "role.listMembers" | "role.manageBilling" | "role.readTeamBookings" | "role.readOrgBookings" | "role.readRecordings" | "routingForm.*" | "routingForm.create" | "routingForm.read" | "routingForm.update" | "routingForm.delete" | "routingForm.invite" | "routingForm.remove" | "routingForm.changeMemberRole" | "routingForm.listMembers" | "routingForm.manageBilling" | "routingForm.readTeamBookings" | "routingForm.readOrgBookings" | "routingForm.readRecordings" | "workflow.*" | "workflow.create" | "workflow.read" | "workflow.update" | "workflow.delete" | "workflow.invite" | "workflow.remove" | "workflow.changeMemberRole" | "workflow.listMembers" | "workflow.manageBilling" | "workflow.readTeamBookings" | "workflow.readOrgBookings" | "workflow.readRecordings";
        };
        output: boolean;
    }>;
    checkPermissions: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            teamId: number;
            permissions: ("*.*" | "*.create" | "*.read" | "*.update" | "*.delete" | "*.invite" | "*.remove" | "*.changeMemberRole" | "*.listMembers" | "*.manageBilling" | "*.readTeamBookings" | "*.readOrgBookings" | "*.readRecordings" | "eventType.*" | "eventType.create" | "eventType.read" | "eventType.update" | "eventType.delete" | "eventType.invite" | "eventType.remove" | "eventType.changeMemberRole" | "eventType.listMembers" | "eventType.manageBilling" | "eventType.readTeamBookings" | "eventType.readOrgBookings" | "eventType.readRecordings" | "team.*" | "team.create" | "team.read" | "team.update" | "team.delete" | "team.invite" | "team.remove" | "team.changeMemberRole" | "team.listMembers" | "team.manageBilling" | "team.readTeamBookings" | "team.readOrgBookings" | "team.readRecordings" | "organization.*" | "organization.create" | "organization.read" | "organization.update" | "organization.delete" | "organization.invite" | "organization.remove" | "organization.changeMemberRole" | "organization.listMembers" | "organization.manageBilling" | "organization.readTeamBookings" | "organization.readOrgBookings" | "organization.readRecordings" | "organization.attributes.*" | "organization.attributes.create" | "organization.attributes.read" | "organization.attributes.update" | "organization.attributes.delete" | "organization.attributes.invite" | "organization.attributes.remove" | "organization.attributes.changeMemberRole" | "organization.attributes.listMembers" | "organization.attributes.manageBilling" | "organization.attributes.readTeamBookings" | "organization.attributes.readOrgBookings" | "organization.attributes.readRecordings" | "booking.*" | "booking.create" | "booking.read" | "booking.update" | "booking.delete" | "booking.invite" | "booking.remove" | "booking.changeMemberRole" | "booking.listMembers" | "booking.manageBilling" | "booking.readTeamBookings" | "booking.readOrgBookings" | "booking.readRecordings" | "insights.*" | "insights.create" | "insights.read" | "insights.update" | "insights.delete" | "insights.invite" | "insights.remove" | "insights.changeMemberRole" | "insights.listMembers" | "insights.manageBilling" | "insights.readTeamBookings" | "insights.readOrgBookings" | "insights.readRecordings" | "role.*" | "role.create" | "role.read" | "role.update" | "role.delete" | "role.invite" | "role.remove" | "role.changeMemberRole" | "role.listMembers" | "role.manageBilling" | "role.readTeamBookings" | "role.readOrgBookings" | "role.readRecordings" | "routingForm.*" | "routingForm.create" | "routingForm.read" | "routingForm.update" | "routingForm.delete" | "routingForm.invite" | "routingForm.remove" | "routingForm.changeMemberRole" | "routingForm.listMembers" | "routingForm.manageBilling" | "routingForm.readTeamBookings" | "routingForm.readOrgBookings" | "routingForm.readRecordings" | "workflow.*" | "workflow.create" | "workflow.read" | "workflow.update" | "workflow.delete" | "workflow.invite" | "workflow.remove" | "workflow.changeMemberRole" | "workflow.listMembers" | "workflow.manageBilling" | "workflow.readTeamBookings" | "workflow.readOrgBookings" | "workflow.readRecordings")[];
        };
        output: boolean;
    }>;
    createRole: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            name: string;
            teamId: number;
            permissions: ("*.*" | "*.create" | "*.read" | "*.update" | "*.delete" | "*.invite" | "*.remove" | "*.changeMemberRole" | "*.listMembers" | "*.manageBilling" | "*.readTeamBookings" | "*.readOrgBookings" | "*.readRecordings" | "eventType.*" | "eventType.create" | "eventType.read" | "eventType.update" | "eventType.delete" | "eventType.invite" | "eventType.remove" | "eventType.changeMemberRole" | "eventType.listMembers" | "eventType.manageBilling" | "eventType.readTeamBookings" | "eventType.readOrgBookings" | "eventType.readRecordings" | "team.*" | "team.create" | "team.read" | "team.update" | "team.delete" | "team.invite" | "team.remove" | "team.changeMemberRole" | "team.listMembers" | "team.manageBilling" | "team.readTeamBookings" | "team.readOrgBookings" | "team.readRecordings" | "organization.*" | "organization.create" | "organization.read" | "organization.update" | "organization.delete" | "organization.invite" | "organization.remove" | "organization.changeMemberRole" | "organization.listMembers" | "organization.manageBilling" | "organization.readTeamBookings" | "organization.readOrgBookings" | "organization.readRecordings" | "organization.attributes.*" | "organization.attributes.create" | "organization.attributes.read" | "organization.attributes.update" | "organization.attributes.delete" | "organization.attributes.invite" | "organization.attributes.remove" | "organization.attributes.changeMemberRole" | "organization.attributes.listMembers" | "organization.attributes.manageBilling" | "organization.attributes.readTeamBookings" | "organization.attributes.readOrgBookings" | "organization.attributes.readRecordings" | "booking.*" | "booking.create" | "booking.read" | "booking.update" | "booking.delete" | "booking.invite" | "booking.remove" | "booking.changeMemberRole" | "booking.listMembers" | "booking.manageBilling" | "booking.readTeamBookings" | "booking.readOrgBookings" | "booking.readRecordings" | "insights.*" | "insights.create" | "insights.read" | "insights.update" | "insights.delete" | "insights.invite" | "insights.remove" | "insights.changeMemberRole" | "insights.listMembers" | "insights.manageBilling" | "insights.readTeamBookings" | "insights.readOrgBookings" | "insights.readRecordings" | "role.*" | "role.create" | "role.read" | "role.update" | "role.delete" | "role.invite" | "role.remove" | "role.changeMemberRole" | "role.listMembers" | "role.manageBilling" | "role.readTeamBookings" | "role.readOrgBookings" | "role.readRecordings" | "routingForm.*" | "routingForm.create" | "routingForm.read" | "routingForm.update" | "routingForm.delete" | "routingForm.invite" | "routingForm.remove" | "routingForm.changeMemberRole" | "routingForm.listMembers" | "routingForm.manageBilling" | "routingForm.readTeamBookings" | "routingForm.readOrgBookings" | "routingForm.readRecordings" | "workflow.*" | "workflow.create" | "workflow.read" | "workflow.update" | "workflow.delete" | "workflow.invite" | "workflow.remove" | "workflow.changeMemberRole" | "workflow.listMembers" | "workflow.manageBilling" | "workflow.readTeamBookings" | "workflow.readOrgBookings" | "workflow.readRecordings")[];
            description?: string | undefined;
            color?: string | undefined;
        };
        output: import("@calcom/features/pbac/domain/models/Role").Role;
    }>;
    updateRole: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            teamId: number;
            permissions: ("*.*" | "*.create" | "*.read" | "*.update" | "*.delete" | "*.invite" | "*.remove" | "*.changeMemberRole" | "*.listMembers" | "*.manageBilling" | "*.readTeamBookings" | "*.readOrgBookings" | "*.readRecordings" | "eventType.*" | "eventType.create" | "eventType.read" | "eventType.update" | "eventType.delete" | "eventType.invite" | "eventType.remove" | "eventType.changeMemberRole" | "eventType.listMembers" | "eventType.manageBilling" | "eventType.readTeamBookings" | "eventType.readOrgBookings" | "eventType.readRecordings" | "team.*" | "team.create" | "team.read" | "team.update" | "team.delete" | "team.invite" | "team.remove" | "team.changeMemberRole" | "team.listMembers" | "team.manageBilling" | "team.readTeamBookings" | "team.readOrgBookings" | "team.readRecordings" | "organization.*" | "organization.create" | "organization.read" | "organization.update" | "organization.delete" | "organization.invite" | "organization.remove" | "organization.changeMemberRole" | "organization.listMembers" | "organization.manageBilling" | "organization.readTeamBookings" | "organization.readOrgBookings" | "organization.readRecordings" | "organization.attributes.*" | "organization.attributes.create" | "organization.attributes.read" | "organization.attributes.update" | "organization.attributes.delete" | "organization.attributes.invite" | "organization.attributes.remove" | "organization.attributes.changeMemberRole" | "organization.attributes.listMembers" | "organization.attributes.manageBilling" | "organization.attributes.readTeamBookings" | "organization.attributes.readOrgBookings" | "organization.attributes.readRecordings" | "booking.*" | "booking.create" | "booking.read" | "booking.update" | "booking.delete" | "booking.invite" | "booking.remove" | "booking.changeMemberRole" | "booking.listMembers" | "booking.manageBilling" | "booking.readTeamBookings" | "booking.readOrgBookings" | "booking.readRecordings" | "insights.*" | "insights.create" | "insights.read" | "insights.update" | "insights.delete" | "insights.invite" | "insights.remove" | "insights.changeMemberRole" | "insights.listMembers" | "insights.manageBilling" | "insights.readTeamBookings" | "insights.readOrgBookings" | "insights.readRecordings" | "role.*" | "role.create" | "role.read" | "role.update" | "role.delete" | "role.invite" | "role.remove" | "role.changeMemberRole" | "role.listMembers" | "role.manageBilling" | "role.readTeamBookings" | "role.readOrgBookings" | "role.readRecordings" | "routingForm.*" | "routingForm.create" | "routingForm.read" | "routingForm.update" | "routingForm.delete" | "routingForm.invite" | "routingForm.remove" | "routingForm.changeMemberRole" | "routingForm.listMembers" | "routingForm.manageBilling" | "routingForm.readTeamBookings" | "routingForm.readOrgBookings" | "routingForm.readRecordings" | "workflow.*" | "workflow.create" | "workflow.read" | "workflow.update" | "workflow.delete" | "workflow.invite" | "workflow.remove" | "workflow.changeMemberRole" | "workflow.listMembers" | "workflow.manageBilling" | "workflow.readTeamBookings" | "workflow.readOrgBookings" | "workflow.readRecordings")[];
            roleId: string;
            name?: string | undefined;
            color?: string | undefined;
        };
        output: import("@calcom/features/pbac/domain/models/Role").Role;
    }>;
    deleteRole: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            teamId: number;
            roleId: string;
        };
        output: {
            success: boolean;
        };
    }>;
    getTeamRoles: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            teamId: number;
        };
        output: import("@calcom/features/pbac/domain/models/Role").Role[];
    }>;
}>;
