import type { PrismaClient } from "@calcom/prisma";
import type { TResponseInputSchema } from "./response.schema";
interface ResponseHandlerOptions {
    ctx: {
        prisma: PrismaClient;
    };
    input: TResponseInputSchema;
}
export declare const responseHandler: ({ ctx, input }: ResponseHandlerOptions) => Promise<{
    isPreview: boolean;
    formResponse: {
        id: number;
        createdAt: Date;
        updatedAt: Date | null;
        response: import(".prisma/client").Prisma.JsonValue;
        uuid: string | null;
        formFillerId: string;
        formId: string;
        routedToBookingUid: string | null;
        chosenRouteId: string | null;
    } | {
        id: number;
        formId: string;
        response: Record<string, {
            label: string;
            value: (string | number | string[]) & (string | number | string[] | undefined);
            identifier?: string | undefined;
        }>;
        chosenRouteId: string | null;
        createdAt: Date;
        updatedAt: Date;
    } | null | undefined;
    queuedFormResponse: {
        id: string;
        createdAt: Date;
        updatedAt: Date | null;
        response: import(".prisma/client").Prisma.JsonValue;
        formId: string;
        chosenRouteId: string | null;
        actualResponseId: number | null;
    } | {
        id: string;
        formId: string;
        response: Record<string, {
            label: string;
            value: (string | number | string[]) & (string | number | string[] | undefined);
            identifier?: string | undefined;
        }>;
    } | null | undefined;
    teamMembersMatchingAttributeLogic: null;
    crmContactOwnerEmail: null;
    crmContactOwnerRecordType: null;
    crmAppSlug: null;
    crmRecordId: null;
    attributeRoutingConfig: {
        skipContactOwner?: boolean | undefined;
        salesforce?: {
            rrSkipToAccountLookupField?: boolean | undefined;
            rrSKipToAccountLookupFieldName?: string | undefined;
        } | undefined;
    } | null | undefined;
    timeTaken: Record<string, number | null>;
}>;
export default responseHandler;
