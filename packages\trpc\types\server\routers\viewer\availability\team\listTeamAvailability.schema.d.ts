import { z } from "zod";
export declare const ZListTeamAvailaiblityScheme: z.ZodObject<{
    limit: z.ZodNumber;
    cursor: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
    startDate: z.ZodString;
    endDate: z.ZodString;
    loggedInUsersTz: z.ZodString;
    teamId: z.Zod<PERSON>ptional<z.ZodNumber>;
    searchString: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    startDate: string;
    endDate: string;
    limit: number;
    loggedInUsersTz: string;
    cursor?: number | null | undefined;
    teamId?: number | undefined;
    searchString?: string | undefined;
}, {
    startDate: string;
    endDate: string;
    limit: number;
    loggedInUsersTz: string;
    cursor?: number | null | undefined;
    teamId?: number | undefined;
    searchString?: string | undefined;
}>;
export type TListTeamAvailaiblityScheme = z.infer<typeof ZListTeamAvailaiblityScheme>;
