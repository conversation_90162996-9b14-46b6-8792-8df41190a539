import { z } from "zod";
export declare const ZSendVerifyEmailCodeSchema: z.ZodObject<{
    email: z.ZodString;
    username: z.<PERSON>odOptional<z.ZodString>;
    language: z.ZodOptional<z.ZodString>;
    isVerifyingEmail: z.<PERSON>al<z.ZodBoolean>;
}, "strip", z.Z<PERSON>ype<PERSON>ny, {
    email: string;
    username?: string | undefined;
    language?: string | undefined;
    isVerifyingEmail?: boolean | undefined;
}, {
    email: string;
    username?: string | undefined;
    language?: string | undefined;
    isVerifyingEmail?: boolean | undefined;
}>;
export type TSendVerifyEmailCodeSchema = z.infer<typeof ZSendVerifyEmailCodeSchema>;
