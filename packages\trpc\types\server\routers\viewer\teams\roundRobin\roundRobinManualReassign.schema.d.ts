import { z } from "zod";
export declare const ZRoundRobinManualReassignInputSchema: z.ZodObject<{
    bookingId: z.ZodNumber;
    teamMemberId: z.ZodNumber;
    reassignReason: z.ZodOptional<z.ZodString>;
}, "strip", z.<PERSON>od<PERSON>ype<PERSON>ny, {
    bookingId: number;
    teamMemberId: number;
    reassignReason?: string | undefined;
}, {
    bookingId: number;
    teamMemberId: number;
    reassignReason?: string | undefined;
}>;
export type TRoundRobinManualReassignInputSchema = z.infer<typeof ZRoundRobinManualReassignInputSchema>;
