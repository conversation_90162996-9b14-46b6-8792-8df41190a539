import type { TListFilterSegmentsInputSchema } from "@calcom/lib/server/repository/filterSegment.type";
import type { TrpcSessionUser } from "@calcom/trpc/server/types";
export declare const listHandler: ({ ctx, input, }: {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TListFilterSegmentsInputSchema;
}) => Promise<{
    segments: import("@calcom/features/data-table/lib/types").FilterSegmentOutput[];
    preferredSegmentId: {
        id: number;
        type: "user";
    } | {
        id: string;
        type: "system";
    } | null;
}>;
