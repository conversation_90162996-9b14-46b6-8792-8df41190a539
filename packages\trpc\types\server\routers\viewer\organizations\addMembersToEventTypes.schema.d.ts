import { z } from "zod";
export declare const ZAddMembersToEventTypes: z.ZodObject<{
    userIds: z.<PERSON><z.<PERSON>odN<PERSON><PERSON>, "many">;
    teamIds: z.<PERSON><PERSON><z.<PERSON><PERSON><PERSON>, "many">;
    eventTypeIds: z.<PERSON><z.ZodNumber, "many">;
}, "strip", z.ZodType<PERSON>, {
    userIds: number[];
    teamIds: number[];
    eventTypeIds: number[];
}, {
    userIds: number[];
    teamIds: number[];
    eventTypeIds: number[];
}>;
export type TAddMembersToEventTypes = z.infer<typeof ZAddMembersToEventTypes>;
