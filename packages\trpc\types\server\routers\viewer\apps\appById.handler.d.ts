import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TAppByIdInputSchema } from "./appById.schema";
type AppByIdOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TAppByIdInputSchema;
};
export declare const appByIdHandler: ({ ctx, input }: AppByIdOptions) => Promise<{
    installed?: boolean;
    type: `${string}_calendar` | `${string}_messaging` | `${string}_payment` | `${string}_video` | `${string}_other` | `${string}_automation` | `${string}_analytics` | `${string}_crm` | `${string}_other_calendar`;
    title?: string;
    name: string;
    description: string;
    variant: "calendar" | "payment" | "conferencing" | "video" | "other" | "other_calendar" | "automation" | "crm";
    slug: string;
    category?: string;
    categories: import(".prisma/client").AppCategories[];
    extendsFeature?: "EventType" | "User";
    logo: string;
    publisher: string;
    url: string;
    docsUrl?: string;
    verified?: boolean;
    trending?: boolean;
    rating?: number;
    reviews?: number;
    isGlobal?: boolean;
    simplePath?: string;
    email: string;
    key?: import(".prisma/client").Prisma.JsonValue;
    feeType?: "monthly" | "usage-based" | "one-time" | "free";
    price?: number;
    commission?: number;
    licenseRequired?: boolean;
    teamsPlanRequired?: {
        upgradeUrl: string;
    };
    appData?: import("@calcom/types/App").AppData;
    paid?: import("@calcom/types/App").PaidAppData;
    dirName?: string;
    isTemplate?: boolean;
    __template?: string;
    dependencies?: string[];
    concurrentMeetings?: boolean;
    createdAt?: string;
    isOAuth?: boolean;
    delegationCredential?: {
        workspacePlatformSlug: string;
    };
    locationOption: import("@calcom/app-store/utils").LocationOption | null;
    isInstalled: number;
}>;
export {};
