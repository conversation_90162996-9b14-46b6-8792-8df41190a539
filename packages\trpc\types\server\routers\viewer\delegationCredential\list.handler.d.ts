import type { PrismaClient } from "@calcom/prisma";
export default function handler({ ctx, }: {
    ctx: {
        prisma: PrismaClient;
        user: {
            id: number;
            organization: {
                id: number | null;
            };
        };
    };
}): Promise<((Omit<{
    serviceAccountClientId: string | null;
    id: string;
    workspacePlatform: {
        name: string;
        id: number;
        slug: string;
    };
    createdAt: Date;
    updatedAt: Date;
    organizationId: number;
    enabled: boolean;
    domain: string;
    serviceAccountKey: import(".prisma/client").Prisma.JsonValue;
    lastEnabledAt: Date | null;
    lastDisabledAt: Date | null;
}, "serviceAccountKey"> & {
    serviceAccountKey: undefined;
}) | null)[]>;
