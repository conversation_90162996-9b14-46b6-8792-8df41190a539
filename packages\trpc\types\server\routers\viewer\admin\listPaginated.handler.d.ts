import type { TrpcSessionUser } from "../../../types";
import type { TListMembersSchema } from "./listPaginated.schema";
type GetOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TListMembersSchema;
};
declare const listPaginatedHandler: ({ input }: GetOptions) => Promise<{
    rows: {
        name: string | null;
        id: number;
        role: import(".prisma/client").$Enums.UserPermissionRole;
        email: string;
        username: string | null;
        timeZone: string;
        locked: boolean;
        whitelistWorkflows: boolean;
        profiles: {
            username: string;
        }[];
    }[];
    nextCursor: number | undefined;
    meta: {
        totalRowCount: number;
    };
}>;
export default listPaginatedHandler;
