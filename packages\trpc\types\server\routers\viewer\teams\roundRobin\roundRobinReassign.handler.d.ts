import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TRoundRobinReassignInputSchema } from "./roundRobinReassign.schema";
type RoundRobinReassignOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TRoundRobinReassignInputSchema;
};
export declare const roundRobinReassignHandler: ({ ctx, input }: RoundRobinReassignOptions) => Promise<{
    bookingId: number;
    reassignedTo: {
        id: number;
        name: string | null;
        email: string;
    };
}>;
export default roundRobinReassignHandler;
