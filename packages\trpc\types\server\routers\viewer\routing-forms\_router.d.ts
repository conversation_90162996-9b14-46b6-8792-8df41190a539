export declare const routingFormsRouter: import("@trpc/server/unstable-core-do-not-import").CreateRouterInner<import("@trpc/server/unstable-core-do-not-import").RootConfig<{
    ctx: import("../../../createContext").InnerContext;
    meta: object;
    errorShape: {
        message: string;
        code: number;
        data: {
            code: string;
            httpStatus: number;
            path?: string;
            [key: string]: unknown;
        };
    };
    transformer: {
        stringify: (object: any) => string;
        parse: <T = unknown>(string: string) => T;
        serialize: (object: any) => import("superjson/dist/types").SuperJSONResult;
        deserialize: <T_1 = unknown>(payload: import("superjson/dist/types").SuperJSONResult) => T_1;
        registerClass: (v: import("superjson/dist/types").Class, options?: string | import("superjson/dist/class-registry").RegisterOptions | undefined) => void;
        registerSymbol: (v: Symbol, identifier?: string | undefined) => void;
        registerCustom: <I, O extends import("superjson/dist/types").JSONValue>(transformer: Omit<import("superjson/dist/custom-transformer-registry").CustomTransfomer<I, O>, "name">, name: string) => void;
        allowErrorProps: (...props: string[]) => void;
    };
}>, {
    findTeamMembersMatchingAttributeLogicOfRoute: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            response: Record<string, {
                label: string;
                value: (string | number | string[]) & (string | number | string[] | undefined);
            }>;
            formId: string;
            route: {
                id: string;
                queryValue: {
                    type: "group";
                    id?: string | undefined;
                    children1?: Record<string, {
                        type?: string | undefined;
                        properties?: {
                            field?: any;
                            operator?: any;
                            value?: any;
                            valueSrc?: any;
                            valueError?: (string | null)[] | undefined;
                            valueType?: any;
                        } | undefined;
                    }> | undefined;
                    properties?: any;
                } | {
                    type: "switch_group";
                    id?: string | undefined;
                    children1?: Record<string, {
                        type?: string | undefined;
                        properties?: {
                            field?: any;
                            operator?: any;
                            value?: any;
                            valueSrc?: any;
                            valueError?: (string | null)[] | undefined;
                            valueType?: any;
                        } | undefined;
                    }> | undefined;
                    properties?: any;
                };
                action: {
                    type: import("@calcom/routing-forms/zod").RouteActionType;
                    value: string;
                    eventTypeId?: number | undefined;
                };
                name?: string | undefined;
                attributeIdForWeights?: string | undefined;
                attributeRoutingConfig?: {
                    skipContactOwner?: boolean | undefined;
                    salesforce?: {
                        rrSkipToAccountLookupField?: boolean | undefined;
                        rrSKipToAccountLookupFieldName?: string | undefined;
                    } | undefined;
                } | null | undefined;
                attributesQueryValue?: {
                    type: "group";
                    id?: string | undefined;
                    children1?: Record<string, {
                        type?: string | undefined;
                        properties?: {
                            field?: any;
                            operator?: any;
                            value?: any;
                            valueSrc?: any;
                            valueError?: (string | null)[] | undefined;
                            valueType?: any;
                        } | undefined;
                    }> | undefined;
                    properties?: any;
                } | {
                    type: "switch_group";
                    id?: string | undefined;
                    children1?: Record<string, {
                        type?: string | undefined;
                        properties?: {
                            field?: any;
                            operator?: any;
                            value?: any;
                            valueSrc?: any;
                            valueError?: (string | null)[] | undefined;
                            valueType?: any;
                        } | undefined;
                    }> | undefined;
                    properties?: any;
                } | undefined;
                fallbackAttributesQueryValue?: {
                    type: "group";
                    id?: string | undefined;
                    children1?: Record<string, {
                        type?: string | undefined;
                        properties?: {
                            field?: any;
                            operator?: any;
                            value?: any;
                            valueSrc?: any;
                            valueError?: (string | null)[] | undefined;
                            valueType?: any;
                        } | undefined;
                    }> | undefined;
                    properties?: any;
                } | {
                    type: "switch_group";
                    id?: string | undefined;
                    children1?: Record<string, {
                        type?: string | undefined;
                        properties?: {
                            field?: any;
                            operator?: any;
                            value?: any;
                            valueSrc?: any;
                            valueError?: (string | null)[] | undefined;
                            valueType?: any;
                        } | undefined;
                    }> | undefined;
                    properties?: any;
                } | undefined;
                isFallback?: boolean | undefined;
            };
            isPreview?: boolean | undefined;
            _enablePerf?: boolean | undefined;
            _concurrency?: number | undefined;
        };
        output: {
            troubleshooter: null;
            result: null;
            contactOwnerEmail: null;
            checkedFallback: boolean;
            mainWarnings: never[];
            fallbackWarnings: never[];
            eventTypeRedirectUrl: null;
            isUsingAttributeWeights: boolean;
        } | {
            contactOwnerEmail: string | null;
            troubleshooter: {
                type: import("@calcom/lib/raqb/findTeamMembersMatchingAttributeLogic").TroubleshooterCase;
                data: Record<string, any>;
            } | undefined;
            checkedFallback: boolean;
            mainWarnings: string[] | null;
            fallbackWarnings: string[] | null;
            eventTypeRedirectUrl: string;
            isUsingAttributeWeights: boolean;
            result: null;
        } | {
            troubleshooter: {
                type: import("@calcom/lib/raqb/findTeamMembersMatchingAttributeLogic").TroubleshooterCase;
                data: Record<string, any>;
            } | undefined;
            contactOwnerEmail: string | null;
            checkedFallback: boolean;
            mainWarnings: string[] | null;
            fallbackWarnings: string[] | null;
            result: {
                users: {
                    id: number;
                    name: string | null;
                    email: string;
                }[];
                perUserData: {
                    bookingsCount: Record<number, number>;
                    bookingShortfalls: Record<number, number> | null;
                    calibrations: Record<number, number> | null;
                    weights: Record<number, number> | null;
                } | null;
            };
            isUsingAttributeWeights: boolean;
            eventTypeRedirectUrl: string;
        };
    }>;
    public: import("@trpc/server/unstable-core-do-not-import").CreateRouterInner<import("@trpc/server/unstable-core-do-not-import").RootConfig<{
        ctx: import("../../../createContext").InnerContext;
        meta: object;
        errorShape: {
            message: string;
            code: number;
            data: {
                code: string;
                httpStatus: number;
                path?: string;
                [key: string]: unknown;
            };
        };
        transformer: {
            stringify: (object: any) => string;
            parse: <T = unknown>(string: string) => T;
            serialize: (object: any) => import("superjson/dist/types").SuperJSONResult;
            deserialize: <T_1 = unknown>(payload: import("superjson/dist/types").SuperJSONResult) => T_1;
            registerClass: (v: import("superjson/dist/types").Class, options?: string | import("superjson/dist/class-registry").RegisterOptions | undefined) => void;
            registerSymbol: (v: Symbol, identifier?: string | undefined) => void;
            registerCustom: <I, O extends import("superjson/dist/types").JSONValue>(transformer: Omit<import("superjson/dist/custom-transformer-registry").CustomTransfomer<I, O>, "name">, name: string) => void;
            allowErrorProps: (...props: string[]) => void;
        };
    }>, {
        response: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
            input: {
                response: Record<string, {
                    label: string;
                    value: (string | number | string[]) & (string | number | string[] | undefined);
                    identifier?: string | undefined;
                }>;
                formFillerId: string;
                formId: string;
                chosenRouteId?: string | undefined;
                isPreview?: boolean | undefined;
            };
            output: {
                isPreview: boolean;
                formResponse: {
                    id: number;
                    createdAt: Date;
                    updatedAt: Date | null;
                    response: import(".prisma/client").Prisma.JsonValue;
                    uuid: string | null;
                    formFillerId: string;
                    formId: string;
                    routedToBookingUid: string | null;
                    chosenRouteId: string | null;
                } | {
                    id: number;
                    formId: string;
                    response: Record<string, {
                        label: string;
                        value: (string | number | string[]) & (string | number | string[] | undefined);
                        identifier?: string | undefined;
                    }>;
                    chosenRouteId: string | null;
                    createdAt: Date;
                    updatedAt: Date;
                } | null | undefined;
                queuedFormResponse: {
                    id: string;
                    createdAt: Date;
                    updatedAt: Date | null;
                    response: import(".prisma/client").Prisma.JsonValue;
                    formId: string;
                    chosenRouteId: string | null;
                    actualResponseId: number | null;
                } | {
                    id: string;
                    formId: string;
                    response: Record<string, {
                        label: string;
                        value: (string | number | string[]) & (string | number | string[] | undefined);
                        identifier?: string | undefined;
                    }>;
                } | null | undefined;
                teamMembersMatchingAttributeLogic: null;
                crmContactOwnerEmail: null;
                crmContactOwnerRecordType: null;
                crmAppSlug: null;
                crmRecordId: null;
                attributeRoutingConfig: {
                    skipContactOwner?: boolean | undefined;
                    salesforce?: {
                        rrSkipToAccountLookupField?: boolean | undefined;
                        rrSKipToAccountLookupFieldName?: string | undefined;
                    } | undefined;
                } | null | undefined;
                timeTaken: Record<string, number | null>;
            };
        }>;
    }>;
}>;
