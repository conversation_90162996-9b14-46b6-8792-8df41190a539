import type { z } from "zod";
import { getTranslation } from "@calcom/lib/server/i18n";
import type { userMetadata } from "@calcom/prisma/zod-utils";
import type { TrpcSessionUser } from "../../../types";
import type { TEditLocationInputSchema } from "./editLocation.schema";
import type { BookingsProcedureContext } from "./util";
type EditLocationOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    } & BookingsProcedureContext;
    input: TEditLocationInputSchema;
};
type UserMetadata = z.infer<typeof userMetadata>;
/**
 * An error that should be shown to the user
 */
export declare class UserError extends Error {
    constructor(message: string);
}
/**
 * An error that should not be shown to the user
 */
export declare class SystemError extends Error {
    constructor(message: string);
}
export declare function getLocationForOrganizerDefaultConferencingAppInEvtFormat({ organizer, loggedInUserTranslate: translate, }: {
    organizer: {
        name: string;
        metadata: {
            defaultConferencingApp?: NonNullable<UserMetadata>["defaultConferencingApp"];
        } | null;
    };
    /**
     * translate is used to translate if any error is thrown
     */
    loggedInUserTranslate: Awaited<ReturnType<typeof getTranslation>>;
}): string;
export declare function editLocationHandler({ ctx, input }: EditLocationOptions): Promise<{
    message: string;
}>;
export {};
