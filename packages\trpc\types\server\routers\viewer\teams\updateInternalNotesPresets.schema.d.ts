import { z } from "zod";
export declare const ZUpdateInternalNotesPresetsInputSchema: z.ZodObject<{
    teamId: z.ZodNumber;
    presets: z.<PERSON><z.ZodObject<{
        id: z.ZodNumber;
        name: z.ZodString;
        cancellationReason: z.<PERSON><z.ZodString>;
    }, "strip", z.Zod<PERSON>, {
        name: string;
        id: number;
        cancellationReason?: string | undefined;
    }, {
        name: string;
        id: number;
        cancellationReason?: string | undefined;
    }>, "many">;
}, "strip", z.ZodType<PERSON>ny, {
    teamId: number;
    presets: {
        name: string;
        id: number;
        cancellationReason?: string | undefined;
    }[];
}, {
    teamId: number;
    presets: {
        name: string;
        id: number;
        cancellationReason?: string | undefined;
    }[];
}>;
export type TUpdateInternalNotesPresetsInputSchema = z.infer<typeof ZUpdateInternalNotesPresetsInputSchema>;
