import { getConnectedCalendars } from "@calcom/lib/CalendarManager";
import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TSetDestinationCalendarInputSchema } from "./setDestinationCalendar.schema";
type SessionUser = NonNullable<TrpcSessionUser>;
type User = {
    id: SessionUser["id"];
    email: SessionUser["email"];
    userLevelSelectedCalendars: SessionUser["userLevelSelectedCalendars"];
};
type SetDestinationCalendarOptions = {
    ctx: {
        user: User;
    };
    input: TSetDestinationCalendarInputSchema;
};
type ConnectedCalendar = Awaited<ReturnType<typeof getConnectedCalendars>>["connectedCalendars"][number];
type ConnectedCalendarCalendar = NonNullable<ConnectedCalendar["calendars"]>[number];
export declare const getFirstConnectedCalendar: ({ connectedCalendars, matcher, }: {
    connectedCalendars: ConnectedCalendar[];
    matcher: (calendar: ConnectedCalendarCalendar) => boolean;
}) => {
    readOnly: boolean;
    primary: true | null;
    isSelected: boolean;
    credentialId: number;
    delegationCredentialId: string | null;
    name?: string;
    email?: string;
    primaryEmail?: string;
    integrationTitle?: string;
    id?: string | undefined;
    error?: string | null | undefined;
    userId?: number | undefined;
    eventTypeId?: number | null | undefined;
    createdAt?: Date | null | undefined;
    updatedAt?: Date | null | undefined;
    integration?: string | undefined;
    domainWideDelegationCredentialId?: string | null | undefined;
    googleChannelId?: string | null | undefined;
    googleChannelKind?: string | null | undefined;
    googleChannelResourceId?: string | null | undefined;
    googleChannelResourceUri?: string | null | undefined;
    googleChannelExpiration?: string | null | undefined;
    lastErrorAt?: Date | null | undefined;
    watchAttempts?: number | undefined;
    unwatchAttempts?: number | undefined;
    maxAttempts?: number | undefined;
    externalId: string;
};
/**
 * It identifies the destination calendar by externalId, integration and eventTypeId and doesn't consider the `credentialId` or destinationCalendar.id
 * Also, DestinationCalendar doesn't have unique constraint on externalId, integration and eventTypeId, so there could be multiple destinationCalendars with same externalId, integration and eventTypeId in DB.
 * So, it could update any of the destinationCalendar when there are duplicates in DB. Ideally we should have unique constraint on externalId, integration and eventTypeId.
 *
 * With the addition of Delegation credential, it adds another dimension to the problem.
 * A user could have DelegationCredential and non-Delegation credential for the same calendar and he might be selecting Delegation credential connected calendar but it could still be set with nullish destinationCalendar.delegationCredentialId.
 */
export declare const setDestinationCalendarHandler: ({ ctx, input }: SetDestinationCalendarOptions) => Promise<void>;
export {};
