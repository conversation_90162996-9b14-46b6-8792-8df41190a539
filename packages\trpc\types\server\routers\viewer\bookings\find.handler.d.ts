import type { PrismaClient } from "@calcom/prisma";
import type { TFindInputSchema } from "./find.schema";
type GetOptions = {
    ctx: {
        prisma: PrismaClient;
    };
    input: TFindInputSchema;
};
export declare const getHandler: ({ ctx, input }: GetOptions) => Promise<{
    booking: {
        id: number;
        status: import(".prisma/client").$Enums.BookingStatus;
        description: string | null;
        startTime: Date;
        endTime: Date;
        uid: string;
        eventTypeId: number | null;
        paid: boolean;
    } | null;
}>;
export {};
