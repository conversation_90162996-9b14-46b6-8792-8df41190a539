import type { TrpcSessionUser } from "../../../types";
import type { ZDeleteAttributeSchema } from "./delete.schema";
type DeleteOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: ZDeleteAttributeSchema;
};
declare const deleteAttributeHandler: ({ input, ctx }: DeleteOptions) => Promise<{
    name: string;
    id: string;
    type: import(".prisma/client").$Enums.AttributeType;
    createdAt: Date;
    updatedAt: Date;
    slug: string;
    teamId: number;
    enabled: boolean;
    usersCanEditRelation: boolean;
    isWeightsEnabled: boolean;
    isLocked: boolean;
}>;
export default deleteAttributeHandler;
