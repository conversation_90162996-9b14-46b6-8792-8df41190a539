import type { PrismaClient } from "@calcom/prisma";
import type { TrpcSessionUser } from "@calcom/trpc/server/types";
type GetHashedLinkOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
        prisma: PrismaClient;
    };
    input: {
        linkId: string;
    };
};
export declare const getHashedLinkHandler: ({ ctx, input }: GetHashedLinkOptions) => Promise<{
    id: number;
    link: string;
    expiresAt: Date | null;
    maxUsageCount: number;
    usageCount: number;
}>;
export {};
