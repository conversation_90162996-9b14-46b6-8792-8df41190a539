import type { PrismaClient } from "@calcom/prisma";
import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TFilteredListInputSchema } from "./filteredList.schema";
type FilteredListOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
        prisma: PrismaClient;
    };
    input: TFilteredListInputSchema;
};
export declare const filteredListHandler: ({ ctx, input }: FilteredListOptions) => Promise<{
    filtered: ({
        id: number;
        position: number;
        name: string;
        userId: number | null;
        teamId: number | null;
        isActiveOnAll: boolean;
        trigger: import(".prisma/client").$Enums.WorkflowTriggerEvents;
        time: number | null;
        timeUnit: import(".prisma/client").$Enums.TimeUnit | null;
    } & {
        team: {
            id: number;
            name: string;
            members: import(".prisma/client").Membership[];
            slug: string | null;
            logo?: string | null;
        } | null;
        steps: import("@calcom/ee/workflows/lib/types").WorkflowStep[];
        activeOnTeams?: {
            team: {
                id: number;
                name?: string | null;
            };
        }[];
        activeOn?: {
            eventType: {
                id: number;
                title: string;
                parentId: number | null;
                _count: {
                    children: number;
                };
            };
        }[];
        readOnly?: boolean;
        permissions?: import("@calcom/lib/server/repository/workflow-permissions").WorkflowPermissions;
        isOrg?: boolean;
    } & {
        permissions: import("@calcom/lib/server/repository/workflow-permissions").WorkflowPermissions;
        readOnly: boolean;
    })[];
    totalCount: number;
} | undefined>;
export {};
