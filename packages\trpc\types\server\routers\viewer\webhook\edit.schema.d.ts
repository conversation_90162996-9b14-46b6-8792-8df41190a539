import { z } from "zod";
export declare const ZEditInputSchema: z.ZodObject<{
    teamId: z.<PERSON>od<PERSON>ptional<z.ZodNumber>;
    id: z.ZodString;
    subscriberUrl: z.ZodOptional<z.ZodString>;
    eventTriggers: z.<PERSON>od<PERSON>ptional<z.<PERSON><PERSON><PERSON><PERSON><PERSON><z.Zod<PERSON>num<["BOOKING_CANCELLED", "BOOKING_CREATED", "BOOKING_RESCHEDULED", "BOOKING_PAID", "BOOKING_PAYMENT_INITIATED", "MEETING_ENDED", "MEETING_STARTED", "BOOKING_REQUESTED", "BOOKING_REJECTED", "RECORDING_READY", "INSTANT_MEETING", "RECORDING_TRANSCRIPTION_GENERATED", "BOOKING_NO_SHOW_UPDATED", "OOO_CREATED", "AFTER_HOSTS_CAL_VIDEO_NO_SHOW", "AFTER_GUESTS_CAL_VIDEO_NO_SHOW", "FORM_SUBMITTED", "FORM_SUBMITTED_NO_EVENT"]>, "many">>;
    active: z.ZodOptional<z.ZodBoolean>;
    payloadTemplate: z.ZodNullable<z.ZodString>;
    eventTypeId: z.ZodOptional<z.ZodNumber>;
    appId: z.ZodNullable<z.ZodOptional<z.ZodString>>;
    secret: z.ZodNullable<z.ZodOptional<z.ZodString>>;
    time: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
    timeUnit: z.ZodOptional<z.ZodNullable<z.ZodEnum<["DAY", "HOUR", "MINUTE"]>>>;
}, "strip", z.ZodTypeAny, {
    id: string;
    payloadTemplate: string | null;
    teamId?: number | undefined;
    subscriberUrl?: string | undefined;
    eventTriggers?: ("BOOKING_CREATED" | "BOOKING_PAYMENT_INITIATED" | "BOOKING_PAID" | "BOOKING_RESCHEDULED" | "BOOKING_REQUESTED" | "BOOKING_CANCELLED" | "BOOKING_REJECTED" | "BOOKING_NO_SHOW_UPDATED" | "FORM_SUBMITTED" | "MEETING_ENDED" | "MEETING_STARTED" | "RECORDING_READY" | "INSTANT_MEETING" | "RECORDING_TRANSCRIPTION_GENERATED" | "OOO_CREATED" | "AFTER_HOSTS_CAL_VIDEO_NO_SHOW" | "AFTER_GUESTS_CAL_VIDEO_NO_SHOW" | "FORM_SUBMITTED_NO_EVENT")[] | undefined;
    active?: boolean | undefined;
    eventTypeId?: number | undefined;
    appId?: string | null | undefined;
    secret?: string | null | undefined;
    time?: number | null | undefined;
    timeUnit?: "DAY" | "HOUR" | "MINUTE" | null | undefined;
}, {
    id: string;
    payloadTemplate: string | null;
    teamId?: number | undefined;
    subscriberUrl?: string | undefined;
    eventTriggers?: ("BOOKING_CREATED" | "BOOKING_PAYMENT_INITIATED" | "BOOKING_PAID" | "BOOKING_RESCHEDULED" | "BOOKING_REQUESTED" | "BOOKING_CANCELLED" | "BOOKING_REJECTED" | "BOOKING_NO_SHOW_UPDATED" | "FORM_SUBMITTED" | "MEETING_ENDED" | "MEETING_STARTED" | "RECORDING_READY" | "INSTANT_MEETING" | "RECORDING_TRANSCRIPTION_GENERATED" | "OOO_CREATED" | "AFTER_HOSTS_CAL_VIDEO_NO_SHOW" | "AFTER_GUESTS_CAL_VIDEO_NO_SHOW" | "FORM_SUBMITTED_NO_EVENT")[] | undefined;
    active?: boolean | undefined;
    eventTypeId?: number | undefined;
    appId?: string | null | undefined;
    secret?: string | null | undefined;
    time?: number | null | undefined;
    timeUnit?: "DAY" | "HOUR" | "MINUTE" | null | undefined;
}>;
export type TEditInputSchema = z.infer<typeof ZEditInputSchema>;
