export declare const toggleFeatureFlag: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
    input: {
        slug: string;
        enabled: boolean;
    };
    output: {
        type: import(".prisma/client").$Enums.FeatureType | null;
        description: string | null;
        createdAt: Date | null;
        updatedAt: Date | null;
        slug: string;
        enabled: boolean;
        lastUsedAt: Date | null;
        updatedBy: number | null;
        stale: boolean | null;
    };
}>;
