import type { TrpcSessionUser } from "../../../types";
import type { TUpdateAppCredentialsInputSchema } from "./updateAppCredentials.schema";
export type UpdateAppCredentialsOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TUpdateAppCredentialsInputSchema;
};
export declare const handleCustomValidations: ({ ctx, input, appId, }: UpdateAppCredentialsOptions & {
    appId: string;
}) => Promise<({} & {
    [k: string]: unknown;
}) | {
    client_id: string;
    secret_key: string;
    webhook_id: string | true;
}>;
export declare const updateAppCredentialsHandler: ({ ctx, input }: UpdateAppCredentialsOptions) => Promise<boolean>;
