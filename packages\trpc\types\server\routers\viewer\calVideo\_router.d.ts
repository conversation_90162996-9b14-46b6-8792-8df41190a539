export declare const calVideoRouter: import("@trpc/server/unstable-core-do-not-import").CreateRouterInner<import("@trpc/server/unstable-core-do-not-import").RootConfig<{
    ctx: import("../../../createContext").InnerContext;
    meta: object;
    errorShape: {
        message: string;
        code: number;
        data: {
            code: string;
            httpStatus: number;
            path?: string;
            [key: string]: unknown;
        };
    };
    transformer: {
        stringify: (object: any) => string;
        parse: <T = unknown>(string: string) => T;
        serialize: (object: any) => import("superjson/dist/types").SuperJSONResult;
        deserialize: <T_1 = unknown>(payload: import("superjson/dist/types").SuperJSONResult) => T_1;
        registerClass: (v: import("superjson/dist/types").Class, options?: string | import("superjson/dist/class-registry").RegisterOptions | undefined) => void;
        registerSymbol: (v: Symbol, identifier?: string | undefined) => void;
        registerCustom: <I, O extends import("superjson/dist/types").JSONValue>(transformer: Omit<import("superjson/dist/custom-transformer-registry").CustomTransfomer<I, O>, "name">, name: string) => void;
        allowErrorProps: (...props: string[]) => void;
    };
}>, {
    getCalVideoRecordings: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            roomName: string;
        };
        output: {
            data: import("zod").objectOutputType<{
                id: import("zod").ZodString;
                room_name: import("zod").ZodString;
                start_ts: import("zod").ZodNumber;
                status: import("zod").ZodString;
                max_participants: import("zod").ZodOptional<import("zod").ZodNumber>;
                duration: import("zod").ZodNumber;
                share_token: import("zod").ZodString;
            }, import("zod").ZodTypeAny, "passthrough">[];
            total_count: number;
        } | {} | undefined;
    }>;
    getDownloadLinkOfCalVideoRecordings: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            recordingId: string;
        };
        output: {
            download_link: string;
        } | undefined;
    }>;
    getMeetingInformation: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            roomName: string;
        };
        output: any;
    }>;
}>;
