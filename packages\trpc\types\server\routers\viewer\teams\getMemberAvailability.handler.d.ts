import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TGetMemberAvailabilityInputSchema } from "./getMemberAvailability.schema";
type GetMemberAvailabilityOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TGetMemberAvailabilityInputSchema;
};
export declare const getMemberAvailabilityHandler: ({ ctx, input }: GetMemberAvailabilityOptions) => Promise<{
    busy: import("@calcom/types/Calendar").EventBusyDetails[];
    timeZone: string;
    dateRanges: {
        start: import("dayjs").Dayjs;
        end: import("dayjs").Dayjs;
    }[];
    oooExcludedDateRanges: {
        start: import("dayjs").Dayjs;
        end: import("dayjs").Dayjs;
    }[];
    workingHours: import("@calcom/types/schedule").WorkingHours[];
    dateOverrides: import("@calcom/types/schedule").TimeRange[];
    currentSeats: {
        uid: string;
        startTime: Date;
        _count: {
            attendees: number;
        };
    }[] | null;
    datesOutOfOffice: import("@calcom/lib/getUserAvailability").IOutOfOfficeData;
} | {
    busy: never[];
    timeZone: string;
    dateRanges: never[];
    oooExcludedDateRanges: never[];
    workingHours: never[];
    dateOverrides: never[];
    currentSeats: never[];
    datesOutOfOffice: undefined;
}>;
export default getMemberAvailabilityHandler;
