import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TRemoveMemberInputSchema } from "./removeMember.schema";
type RemoveMemberOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
        sourceIp?: string;
    };
    input: TRemoveMemberInputSchema;
};
export declare const removeMemberHandler: ({ ctx, input }: RemoveMemberOptions) => Promise<void>;
export default removeMemberHandler;
