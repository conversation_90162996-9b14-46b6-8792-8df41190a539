import type { TrpcSessionUser } from "../../../types";
import type { TConfirmInputSchema } from "./confirm.schema";
type ConfirmOptions = {
    ctx: {
        user: Pick<NonNullable<TrpcSessionUser>, "id" | "email" | "username" | "role" | "destinationCalendar">;
    };
    input: TConfirmInputSchema;
};
export declare const confirmHandler: ({ ctx, input }: ConfirmOptions) => Promise<{
    message: string;
    status: "ACCEPTED" | "REJECTED";
}>;
export {};
