import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TEditInputSchema } from "./edit.schema";
type EditOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TEditInputSchema;
};
export declare const editHandler: ({ input, ctx }: EditOptions) => Promise<{
    id: string;
    time: number | null;
    userId: number | null;
    eventTypeId: number | null;
    createdAt: Date;
    teamId: number | null;
    secret: string | null;
    appId: string | null;
    active: boolean;
    platform: boolean;
    subscriberUrl: string;
    payloadTemplate: string | null;
    eventTriggers: import(".prisma/client").$Enums.WebhookTriggerEvents[];
    timeUnit: import(".prisma/client").$Enums.TimeUnit | null;
    platformOAuthClientId: string | null;
} | null>;
export {};
