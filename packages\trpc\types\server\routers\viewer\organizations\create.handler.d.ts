import type { TrpcSessionUser } from "../../../types";
import type { TCreateInputSchema } from "./create.schema";
type CreateOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TCreateInputSchema;
};
/**
 * TODO: To be removed. We need to reuse the logic from orgCreationUtils like in intentToCreateOrgHandler
 */
export declare const createHandler: ({ input, ctx }: CreateOptions) => Promise<{
    userId: number;
    email: string;
    organizationId: number;
    upId: string;
}>;
export default createHandler;
