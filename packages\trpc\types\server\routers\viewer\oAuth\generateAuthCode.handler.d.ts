import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TGenerateAuthCodeInputSchema } from "./generateAuthCode.schema";
type AddClientOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TGenerateAuthCodeInputSchema;
};
export declare const generateAuth<PERSON>odeHandler: ({ ctx, input }: AddClientOptions) => Promise<{
    client: {
        name: string;
        clientId: string;
        redirectUri: string;
    };
    authorizationCode: string;
}>;
export {};
