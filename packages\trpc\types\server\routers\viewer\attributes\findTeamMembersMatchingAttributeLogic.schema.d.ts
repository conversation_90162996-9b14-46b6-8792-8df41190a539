import { z } from "zod";
export declare const ZFindTeamMembersMatchingAttributeLogicInputSchema: z.ZodObject<{
    teamId: z.ZodNumber;
    attributesQueryValue: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
        id: z.ZodOptional<z.ZodString>;
        type: z.ZodL<PERSON>al<"group">;
        children1: z.<PERSON>od<PERSON>ptional<z.ZodEffects<z.ZodRecord<z.ZodString, z.ZodObject<{
            type: z.ZodOptional<z.ZodString>;
            properties: z.ZodOptional<z.ZodObject<{
                field: z.ZodOptional<z.ZodAny>;
                operator: z.ZodOptional<z.ZodAny>;
                value: z.ZodOptional<z.ZodAny>;
                valueSrc: z.ZodOptional<z.ZodAny>;
                valueError: z.ZodOptional<z.ZodArray<z.ZodUnion<[z.Zod<PERSON>, z.Zod<PERSON>ull]>, "many">>;
                valueType: z.<PERSON>ption<PERSON><z.ZodAny>;
            }, "strip", z.ZodType<PERSON>ny, {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            }, {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            }>>;
        }, "strip", z.ZodTypeAny, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }>>, Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }>, Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }>>>;
        properties: z.ZodAny;
    }, "strip", z.ZodTypeAny, {
        type: "group";
        id?: string | undefined;
        children1?: Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }> | undefined;
        properties?: any;
    }, {
        type: "group";
        id?: string | undefined;
        children1?: Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }> | undefined;
        properties?: any;
    }>, z.ZodObject<{
        id: z.ZodOptional<z.ZodString>;
        type: z.ZodLiteral<"switch_group">;
        children1: z.ZodOptional<z.ZodEffects<z.ZodRecord<z.ZodString, z.ZodObject<{
            type: z.ZodOptional<z.ZodString>;
            properties: z.ZodOptional<z.ZodObject<{
                field: z.ZodOptional<z.ZodAny>;
                operator: z.ZodOptional<z.ZodAny>;
                value: z.ZodOptional<z.ZodAny>;
                valueSrc: z.ZodOptional<z.ZodAny>;
                valueError: z.ZodOptional<z.ZodArray<z.ZodUnion<[z.ZodString, z.ZodNull]>, "many">>;
                valueType: z.ZodOptional<z.ZodAny>;
            }, "strip", z.ZodTypeAny, {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            }, {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            }>>;
        }, "strip", z.ZodTypeAny, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }>>, Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }>, Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }>>>;
        properties: z.ZodAny;
    }, "strip", z.ZodTypeAny, {
        type: "switch_group";
        id?: string | undefined;
        children1?: Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }> | undefined;
        properties?: any;
    }, {
        type: "switch_group";
        id?: string | undefined;
        children1?: Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }> | undefined;
        properties?: any;
    }>]>>;
    isPreview: z.ZodOptional<z.ZodBoolean>;
    _enablePerf: z.ZodOptional<z.ZodBoolean>;
    _concurrency: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    teamId: number;
    attributesQueryValue: {
        type: "group";
        id?: string | undefined;
        children1?: Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }> | undefined;
        properties?: any;
    } | {
        type: "switch_group";
        id?: string | undefined;
        children1?: Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }> | undefined;
        properties?: any;
    } | null;
    isPreview?: boolean | undefined;
    _enablePerf?: boolean | undefined;
    _concurrency?: number | undefined;
}, {
    teamId: number;
    attributesQueryValue: {
        type: "group";
        id?: string | undefined;
        children1?: Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }> | undefined;
        properties?: any;
    } | {
        type: "switch_group";
        id?: string | undefined;
        children1?: Record<string, {
            type?: string | undefined;
            properties?: {
                field?: any;
                operator?: any;
                value?: any;
                valueSrc?: any;
                valueError?: (string | null)[] | undefined;
                valueType?: any;
            } | undefined;
        }> | undefined;
        properties?: any;
    } | null;
    isPreview?: boolean | undefined;
    _enablePerf?: boolean | undefined;
    _concurrency?: number | undefined;
}>;
export type TFindTeamMembersMatchingAttributeLogicInputSchema = z.infer<typeof ZFindTeamMembersMatchingAttributeLogicInputSchema>;
