import type { TrpcSessionUser } from "../../../../types";
import type { TGetInputSchema } from "./get.schema";
type GetOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TGetInputSchema;
};
export declare const getHandler: ({ ctx, input }: GetOptions) => Promise<{
    id: number;
    name: string;
    isManaged: boolean;
    workingHours: import("@calcom/types/schedule").WorkingHours[];
    schedule: {
        id: number;
        date: Date | null;
        startTime: Date;
        endTime: Date;
        userId: number | null;
        eventTypeId: number | null;
        scheduleId: number | null;
        days: number[];
    }[];
    availability: {
        end: Date;
        userId?: number | null;
        start: Date;
    }[][];
    timeZone: string;
    dateOverrides: {
        ranges: import("@calcom/types/schedule").TimeRange[];
    }[];
    isDefault: boolean;
    isLastSchedule: boolean;
    readOnly: boolean;
    userId: number;
}>;
export {};
