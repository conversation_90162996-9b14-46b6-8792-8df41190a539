import type { Dayjs } from "@calcom/dayjs";
import type { IRedisService } from "@calcom/features/redis/IRedisService";
import type { MembershipRepository } from "@calcom/lib/server/repository/membership";
import type { TeamRepository } from "@calcom/lib/server/repository/team";
type EventDetails = {
    username: string;
    eventSlug: string;
    startTime: Dayjs;
    endTime: Dayjs;
    visitorTimezone?: string;
    visitorUid?: string;
};
export interface INoSlotsNotificationService {
    teamRepo: TeamRepository;
    membershipRepo: MembershipRepository;
    redisClient: IRedisService;
}
export declare class NoSlotsNotificationService {
    readonly dependencies: INoSlotsNotificationService;
    constructor(dependencies: INoSlotsNotificationService);
    handleNotificationWhenNoSlots({ eventDetails, orgDetails, teamId, }: {
        eventDetails: EventDetails;
        orgDetails: {
            currentOrgDomain: string | null;
        };
        teamId?: number;
    }): Promise<void>;
}
export {};
