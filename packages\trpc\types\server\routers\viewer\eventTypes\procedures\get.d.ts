export declare const get: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
    input: {
        id: number;
        eventTypeId?: number | undefined;
        users?: number[] | undefined;
    };
    output: {
        eventType: {
            schedule: number | null;
            restrictionScheduleId: number | null;
            restrictionScheduleName: string | null;
            useBookerTimezone: boolean;
            instantMeetingSchedule: number | null;
            scheduleName: string | null;
            recurringEvent: import("@calcom/types/Calendar").RecurringEvent | null;
            bookingLimits: Partial<Record<"PER_DAY" | "PER_MONTH" | "PER_WEEK" | "PER_YEAR", number | undefined>> | null;
            durationLimits: Partial<Record<"PER_DAY" | "PER_MONTH" | "PER_WEEK" | "PER_YEAR", number | undefined>> | null;
            eventTypeColor: {
                lightEventTypeColor: string;
                darkEventTypeColor: string;
            } | null;
            locations: import("@calcom/app-store/locations").LocationObject[];
            metadata: {
                config?: {
                    useHostSchedulesForTeamEvent?: boolean | undefined;
                } | undefined;
                smartContractAddress?: string | undefined;
                blockchainId?: number | undefined;
                multipleDuration?: number[] | undefined;
                giphyThankYouPage?: string | undefined;
                additionalNotesRequired?: boolean | undefined;
                disableSuccessPage?: boolean | undefined;
                disableStandardEmails?: {
                    all?: {
                        host?: boolean | undefined;
                        attendee?: boolean | undefined;
                    } | undefined;
                    confirmation?: {
                        host?: boolean | undefined;
                        attendee?: boolean | undefined;
                    } | undefined;
                } | undefined;
                managedEventConfig?: {
                    unlockedFields?: {
                        users?: true | undefined;
                        children?: true | undefined;
                        length?: true | undefined;
                        title?: true | undefined;
                        metadata?: true | undefined;
                        description?: true | undefined;
                        userId?: true | undefined;
                        calVideoSettings?: true | undefined;
                        destinationCalendar?: true | undefined;
                        profile?: true | undefined;
                        team?: true | undefined;
                        schedule?: true | undefined;
                        availability?: true | undefined;
                        hashedLink?: true | undefined;
                        secondaryEmail?: true | undefined;
                        customInputs?: true | undefined;
                        timeZone?: true | undefined;
                        bookings?: true | undefined;
                        selectedCalendars?: true | undefined;
                        webhooks?: true | undefined;
                        workflows?: true | undefined;
                        hosts?: true | undefined;
                        slug?: true | undefined;
                        parentId?: true | undefined;
                        bookingLimits?: true | undefined;
                        parent?: true | undefined;
                        teamId?: true | undefined;
                        hidden?: true | undefined;
                        _count?: true | undefined;
                        interfaceLanguage?: true | undefined;
                        position?: true | undefined;
                        locations?: true | undefined;
                        offsetStart?: true | undefined;
                        profileId?: true | undefined;
                        useEventLevelSelectedCalendars?: true | undefined;
                        eventName?: true | undefined;
                        bookingFields?: true | undefined;
                        periodType?: true | undefined;
                        periodStartDate?: true | undefined;
                        periodEndDate?: true | undefined;
                        periodDays?: true | undefined;
                        periodCountCalendarDays?: true | undefined;
                        lockTimeZoneToggleOnBookingPage?: true | undefined;
                        lockedTimeZone?: true | undefined;
                        requiresConfirmation?: true | undefined;
                        requiresConfirmationWillBlockSlot?: true | undefined;
                        requiresConfirmationForFreeEmail?: true | undefined;
                        requiresBookerEmailVerification?: true | undefined;
                        canSendCalVideoTranscriptionEmails?: true | undefined;
                        autoTranslateDescriptionEnabled?: true | undefined;
                        recurringEvent?: true | undefined;
                        disableGuests?: true | undefined;
                        hideCalendarNotes?: true | undefined;
                        hideCalendarEventDetails?: true | undefined;
                        minimumBookingNotice?: true | undefined;
                        beforeEventBuffer?: true | undefined;
                        afterEventBuffer?: true | undefined;
                        seatsPerTimeSlot?: true | undefined;
                        onlyShowFirstAvailableSlot?: true | undefined;
                        disableCancelling?: true | undefined;
                        disableRescheduling?: true | undefined;
                        seatsShowAttendees?: true | undefined;
                        seatsShowAvailabilityCount?: true | undefined;
                        schedulingType?: true | undefined;
                        scheduleId?: true | undefined;
                        allowReschedulingCancelledBookings?: true | undefined;
                        price?: true | undefined;
                        currency?: true | undefined;
                        slotInterval?: true | undefined;
                        successRedirectUrl?: true | undefined;
                        forwardParamsSuccessRedirect?: true | undefined;
                        durationLimits?: true | undefined;
                        isInstantEvent?: true | undefined;
                        instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                        instantMeetingScheduleId?: true | undefined;
                        instantMeetingParameters?: true | undefined;
                        assignAllTeamMembers?: true | undefined;
                        assignRRMembersUsingSegment?: true | undefined;
                        rrSegmentQueryValue?: true | undefined;
                        useEventTypeDestinationCalendarEmail?: true | undefined;
                        isRRWeightsEnabled?: true | undefined;
                        maxLeadThreshold?: true | undefined;
                        includeNoShowInRRCalculation?: true | undefined;
                        allowReschedulingPastBookings?: true | undefined;
                        hideOrganizerEmail?: true | undefined;
                        maxActiveBookingsPerBooker?: true | undefined;
                        maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                        customReplyToEmail?: true | undefined;
                        eventTypeColor?: true | undefined;
                        rescheduleWithSameRoundRobinHost?: true | undefined;
                        secondaryEmailId?: true | undefined;
                        useBookerTimezone?: true | undefined;
                        restrictionScheduleId?: true | undefined;
                        bookingRequiresAuthentication?: true | undefined;
                        owner?: true | undefined;
                        instantMeetingSchedule?: true | undefined;
                        aiPhoneCallConfig?: true | undefined;
                        fieldTranslations?: true | undefined;
                        restrictionSchedule?: true | undefined;
                        hostGroups?: true | undefined;
                    } | undefined;
                } | undefined;
                requiresConfirmationThreshold?: {
                    time: number;
                    unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
                } | undefined;
                bookerLayouts?: {
                    enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
                    defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
                } | null | undefined;
                apps?: {
                    alby?: {
                        price: number;
                        currency: string;
                        appCategories?: string[] | undefined;
                        paymentOption?: string | undefined;
                        enabled?: boolean | undefined;
                        credentialId?: number | undefined;
                    } | undefined;
                    basecamp3?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    btcpayserver?: {
                        price: number;
                        currency: string;
                        appCategories?: string[] | undefined;
                        paymentOption?: string | undefined;
                        enabled?: boolean | undefined;
                        credentialId?: number | undefined;
                    } | undefined;
                    closecom?: {
                        enabled?: boolean | undefined;
                        credentialId?: number | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    dailyvideo?: {} | undefined;
                    dub?: {} | undefined;
                    fathom?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        trackingId?: string | undefined;
                    } | undefined;
                    feishucalendar?: {} | undefined;
                    ga4?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        trackingId?: string | undefined;
                    } | undefined;
                    giphy?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        thankYouPage?: string | undefined;
                    } | undefined;
                    googlecalendar?: {} | undefined;
                    googlevideo?: {} | undefined;
                    gtm?: {
                        trackingId: string;
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    hitpay?: {
                        price: number;
                        currency: string;
                        credentialId?: number | undefined;
                        appCategories?: string[] | undefined;
                        paymentOption?: string | undefined;
                        enabled?: boolean | undefined;
                    } | undefined;
                    hubspot?: {
                        enabled?: boolean | undefined;
                        credentialId?: number | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    insihts?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        SITE_ID?: string | undefined;
                        SCRIPT_URL?: string | undefined;
                    } | undefined;
                    intercom?: {} | undefined;
                    jelly?: {} | undefined;
                    jitsivideo?: {} | undefined;
                    larkcalendar?: {} | undefined;
                    make?: {} | undefined;
                    matomo?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        MATOMO_URL?: string | undefined;
                        SITE_ID?: string | undefined;
                    } | undefined;
                    metapixel?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        trackingId?: string | undefined;
                    } | undefined;
                    "mock-payment-app"?: {
                        price: number;
                        currency: string;
                        credentialId?: number | undefined;
                        appCategories?: string[] | undefined;
                        paymentOption?: string | undefined;
                        enabled?: boolean | undefined;
                    } | undefined;
                    nextcloudtalk?: {} | undefined;
                    office365calendar?: {
                        client_id: string;
                        client_secret: string;
                    } | undefined;
                    office365video?: {
                        client_id: string;
                        client_secret: string;
                    } | undefined;
                    paypal?: {
                        price: number;
                        currency: string;
                        credentialId?: number | undefined;
                        appCategories?: string[] | undefined;
                        paymentOption?: string | undefined;
                        enabled?: boolean | undefined;
                    } | undefined;
                    "pipedrive-crm"?: {
                        enabled?: boolean | undefined;
                        credentialId?: number | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    plausible?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        PLAUSIBLE_URL?: string | undefined;
                        trackingId?: string | undefined;
                    } | undefined;
                    posthog?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        TRACKING_ID?: string | undefined;
                        API_HOST?: string | undefined;
                    } | undefined;
                    qr_code?: {
                        enabled?: boolean | undefined;
                        credentialId?: number | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    "routing-forms"?: any;
                    salesforce?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        roundRobinLeadSkip?: boolean | undefined;
                        roundRobinSkipCheckRecordOn?: import("@calcom/app-store/salesforce/lib/enums").SalesforceRecordEnum | undefined;
                        ifFreeEmailDomainSkipOwnerCheck?: boolean | undefined;
                        roundRobinSkipFallbackToLeadOwner?: boolean | undefined;
                        skipContactCreation?: boolean | undefined;
                        createEventOn?: import("@calcom/app-store/salesforce/lib/enums").SalesforceRecordEnum | undefined;
                        createNewContactUnderAccount?: boolean | undefined;
                        createLeadIfAccountNull?: boolean | undefined;
                        onBookingWriteToEventObject?: boolean | undefined;
                        onBookingWriteToEventObjectMap?: Record<string, any> | undefined;
                        createEventOnLeadCheckForContact?: boolean | undefined;
                        onBookingChangeRecordOwner?: boolean | undefined;
                        onBookingChangeRecordOwnerName?: string | undefined;
                        sendNoShowAttendeeData?: boolean | undefined;
                        sendNoShowAttendeeDataField?: string | undefined;
                        onBookingWriteToRecord?: boolean | undefined;
                        onBookingWriteToRecordFields?: Record<string, {
                            value: string | boolean;
                            fieldType: import("@calcom/app-store/salesforce/lib/enums").SalesforceFieldType;
                            whenToWrite: import("@calcom/app-store/salesforce/lib/enums").WhenToWriteToRecord;
                        }> | undefined;
                        ignoreGuests?: boolean | undefined;
                        onCancelWriteToEventRecord?: boolean | undefined;
                        onCancelWriteToEventRecordFields?: Record<string, {
                            value: string | boolean;
                            fieldType: import("@calcom/app-store/salesforce/lib/enums").SalesforceFieldType;
                            whenToWrite: import("@calcom/app-store/salesforce/lib/enums").WhenToWriteToRecord;
                        }> | undefined;
                    } | undefined;
                    shimmervideo?: {} | undefined;
                    stripe?: {
                        price: number;
                        currency: string;
                        credentialId?: number | undefined;
                        appCategories?: string[] | undefined;
                        paymentOption?: string | undefined;
                        enabled?: boolean | undefined;
                        refundPolicy?: import("@calcom/lib/payment/types").RefundPolicy | undefined;
                        refundDaysCount?: number | undefined;
                        refundCountCalendarDays?: boolean | undefined;
                    } | undefined;
                    tandemvideo?: {} | undefined;
                    "booking-pages-tag"?: {
                        trackingId: string;
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    "event-type-app-card"?: {
                        isSunrise: boolean;
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    twipla?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        SITE_ID?: string | undefined;
                    } | undefined;
                    umami?: {
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                        SITE_ID?: string | undefined;
                        SCRIPT_URL?: string | undefined;
                    } | undefined;
                    vital?: {} | undefined;
                    webex?: {} | undefined;
                    wordpress?: {
                        isSunrise: boolean;
                        credentialId?: number | undefined;
                        enabled?: boolean | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    zapier?: {} | undefined;
                    "zoho-bigin"?: {
                        enabled?: boolean | undefined;
                        credentialId?: number | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    zohocalendar?: {} | undefined;
                    zohocrm?: {
                        enabled?: boolean | undefined;
                        credentialId?: number | undefined;
                        appCategories?: string[] | undefined;
                    } | undefined;
                    zoomvideo?: {} | undefined;
                } | undefined;
            };
            customInputs: {
                id: number;
                type: "TEXT" | "TEXTLONG" | "NUMBER" | "BOOL" | "RADIO" | "PHONE";
                label: string;
                required: boolean;
                eventTypeId: number;
                placeholder: string;
                options?: {
                    type: string;
                    label: string;
                }[] | null | undefined;
                hasToBeCreated?: boolean | undefined;
            }[];
            users: {
                name: string | null;
                id: number;
                locale: string | null;
                email: string;
                username: string | null;
                avatarUrl: string | null;
                timeZone: string;
                defaultScheduleId: number | null;
                isPlatformManaged: boolean;
            }[];
            bookerUrl: string;
            children: {
                owner: {
                    avatar: string;
                    email: string;
                    name: string;
                    username: string;
                    membership: import(".prisma/client").$Enums.MembershipRole;
                    id: number;
                    avatarUrl: string | null;
                    nonProfileUsername: string | null;
                    profile: import("@calcom/types/UserProfile").UserProfile;
                };
                created: boolean;
                slug: string;
                hidden: boolean;
            }[];
            id: number;
            length: number;
            title: string;
            description: string | null;
            userId: number | null;
            calVideoSettings: {
                disableRecordingForOrganizer: boolean;
                disableRecordingForGuests: boolean;
                enableAutomaticTranscription: boolean;
                enableAutomaticRecordingForOrganizer: boolean;
                redirectUrlOnExit: string | null;
                disableTranscriptionForGuests: boolean;
                disableTranscriptionForOrganizer: boolean;
            } | null;
            destinationCalendar: {
                id: number;
                userId: number | null;
                eventTypeId: number | null;
                createdAt: Date | null;
                updatedAt: Date | null;
                integration: string;
                externalId: string;
                primaryEmail: string | null;
                credentialId: number | null;
                delegationCredentialId: string | null;
                domainWideDelegationCredentialId: string | null;
            } | null;
            team: {
                name: string;
                id: number;
                slug: string | null;
                rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
                parentId: number | null;
                members: {
                    user: {
                        name: string | null;
                        id: number;
                        locale: string | null;
                        email: string;
                        username: string | null;
                        avatarUrl: string | null;
                        timeZone: string;
                        defaultScheduleId: number | null;
                        isPlatformManaged: boolean;
                        eventTypes: {
                            slug: string;
                        }[];
                    };
                    role: import(".prisma/client").$Enums.MembershipRole;
                    accepted: boolean;
                }[];
                parent: {
                    organizationSettings: {
                        lockEventTypeCreationForUsers: boolean;
                    } | null;
                    slug: string | null;
                } | null;
            } | null;
            hashedLink: {
                link: string;
                id: number;
                expiresAt: Date | null;
                maxUsageCount: number;
                usageCount: number;
            }[];
            timeZone: string | null;
            webhooks: {
                id: string;
                eventTypeId: number | null;
                secret: string | null;
                active: boolean;
                subscriberUrl: string;
                payloadTemplate: string | null;
                eventTriggers: import(".prisma/client").$Enums.WebhookTriggerEvents[];
            }[];
            workflows: ({
                workflow: {
                    name: string;
                    id: number;
                    time: number | null;
                    userId: number | null;
                    team: {
                        name: string;
                        id: number;
                        slug: string | null;
                        members: {
                            id: number;
                            role: import(".prisma/client").$Enums.MembershipRole;
                            userId: number;
                            createdAt: Date | null;
                            updatedAt: Date | null;
                            disableImpersonation: boolean;
                            teamId: number;
                            accepted: boolean;
                            customRoleId: string | null;
                        }[];
                    } | null;
                    teamId: number | null;
                    steps: {
                        id: number;
                        template: import(".prisma/client").$Enums.WorkflowTemplates;
                        action: import(".prisma/client").$Enums.WorkflowActions;
                        stepNumber: number;
                        workflowId: number;
                        sendTo: string | null;
                        reminderBody: string | null;
                        emailSubject: string | null;
                        numberRequired: boolean | null;
                        sender: string | null;
                        numberVerificationPending: boolean;
                        includeCalendarEvent: boolean;
                        verifiedAt: Date | null;
                        agentId: string | null;
                    }[];
                    trigger: import(".prisma/client").$Enums.WorkflowTriggerEvents;
                    timeUnit: import(".prisma/client").$Enums.TimeUnit | null;
                    activeOn: {
                        eventType: {
                            id: number;
                            title: string;
                            parentId: number | null;
                            _count: {
                                children: number;
                            };
                        };
                    }[];
                };
            } & {
                id: number;
                eventTypeId: number;
                workflowId: number;
            })[];
            hosts: {
                user: {
                    timeZone: string;
                };
                userId: number;
                scheduleId: number | null;
                weight: number | null;
                isFixed: boolean;
                priority: number | null;
            }[];
            slug: string;
            parent: {
                id: number;
                teamId: number | null;
            } | null;
            teamId: number | null;
            hidden: boolean;
            interfaceLanguage: string | null;
            offsetStart: number;
            useEventLevelSelectedCalendars: boolean;
            eventName: string | null;
            bookingFields: import(".prisma/client").Prisma.JsonValue;
            periodType: import(".prisma/client").$Enums.PeriodType;
            periodStartDate: Date | null;
            periodEndDate: Date | null;
            periodDays: number | null;
            periodCountCalendarDays: boolean | null;
            lockTimeZoneToggleOnBookingPage: boolean;
            lockedTimeZone: string | null;
            requiresConfirmation: boolean;
            requiresConfirmationWillBlockSlot: boolean;
            requiresConfirmationForFreeEmail: boolean;
            requiresBookerEmailVerification: boolean;
            canSendCalVideoTranscriptionEmails: boolean;
            autoTranslateDescriptionEnabled: boolean;
            disableGuests: boolean;
            hideCalendarNotes: boolean;
            hideCalendarEventDetails: boolean;
            minimumBookingNotice: number;
            beforeEventBuffer: number;
            afterEventBuffer: number;
            seatsPerTimeSlot: number | null;
            onlyShowFirstAvailableSlot: boolean;
            disableCancelling: boolean | null;
            disableRescheduling: boolean | null;
            seatsShowAttendees: boolean | null;
            seatsShowAvailabilityCount: boolean | null;
            schedulingType: import(".prisma/client").$Enums.SchedulingType | null;
            allowReschedulingCancelledBookings: boolean | null;
            price: number;
            currency: string;
            slotInterval: number | null;
            successRedirectUrl: string | null;
            forwardParamsSuccessRedirect: boolean | null;
            isInstantEvent: boolean;
            instantMeetingExpiryTimeOffsetInSeconds: number;
            instantMeetingParameters: string[];
            assignAllTeamMembers: boolean;
            assignRRMembersUsingSegment: boolean;
            rrSegmentQueryValue: import(".prisma/client").Prisma.JsonValue;
            useEventTypeDestinationCalendarEmail: boolean;
            isRRWeightsEnabled: boolean;
            maxLeadThreshold: number | null;
            includeNoShowInRRCalculation: boolean;
            allowReschedulingPastBookings: boolean;
            hideOrganizerEmail: boolean;
            maxActiveBookingsPerBooker: number | null;
            maxActiveBookingPerBookerOfferReschedule: boolean;
            customReplyToEmail: string | null;
            rescheduleWithSameRoundRobinHost: boolean;
            secondaryEmailId: number | null;
            owner: {
                id: number;
                timeZone: string;
            } | null;
            aiPhoneCallConfig: {
                id: number;
                eventTypeId: number;
                enabled: boolean;
                templateType: string;
                schedulerName: string | null;
                generalPrompt: string | null;
                yourPhoneNumber: string;
                numberToCall: string;
                guestName: string | null;
                guestEmail: string | null;
                guestCompany: string | null;
                beginMessage: string | null;
                llmId: string | null;
            } | null;
            fieldTranslations: {
                field: import(".prisma/client").$Enums.EventTypeAutoTranslatedField;
                targetLocale: string;
                translatedText: string;
            }[];
            restrictionSchedule: {
                name: string;
                id: number;
            } | null;
            hostGroups: {
                name: string;
                id: string;
            }[];
        } & {
            users: ({
                name: string | null;
                id: number;
                locale: string | null;
                email: string;
                username: string | null;
                avatarUrl: string | null;
                timeZone: string;
                defaultScheduleId: number | null;
                isPlatformManaged: boolean;
            } & {
                avatar: string;
            })[];
            periodStartDate: string | null;
            periodEndDate: string | null;
            bookingFields: {
                name: string;
                type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
                label?: string | undefined;
                options?: {
                    label: string;
                    value: string;
                }[] | undefined;
                required?: boolean | undefined;
                placeholder?: string | undefined;
                maxLength?: number | undefined;
                defaultLabel?: string | undefined;
                defaultPlaceholder?: string | undefined;
                labelAsSafeHtml?: string | undefined;
                getOptionsAt?: string | undefined;
                optionsInputs?: Record<string, {
                    type: "phone" | "address" | "text";
                    required?: boolean | undefined;
                    placeholder?: string | undefined;
                }> | undefined;
                minLength?: number | undefined;
                excludeEmails?: string | undefined;
                requireEmails?: string | undefined;
                variant?: string | undefined;
                variantsConfig?: {
                    variants: Record<string, {
                        fields: {
                            name: string;
                            type: "number" | "boolean" | "phone" | "name" | "address" | "select" | "textarea" | "text" | "checkbox" | "radio" | "email" | "url" | "multiselect" | "multiemail" | "radioInput";
                            label?: string | undefined;
                            required?: boolean | undefined;
                            placeholder?: string | undefined;
                            maxLength?: number | undefined;
                            labelAsSafeHtml?: string | undefined;
                            minLength?: number | undefined;
                            excludeEmails?: string | undefined;
                            requireEmails?: string | undefined;
                        }[];
                    }>;
                } | undefined;
                views?: {
                    id: string;
                    label: string;
                    description?: string | undefined;
                }[] | undefined;
                hideWhenJustOneOption?: boolean | undefined;
                hidden?: boolean | undefined;
                editable?: "user" | "system" | "system-but-optional" | "system-but-hidden" | "user-readonly" | undefined;
                sources?: {
                    id: string;
                    type: string;
                    label: string;
                    editUrl?: string | undefined;
                    fieldRequired?: boolean | undefined;
                }[] | undefined;
                disableOnPrefill?: boolean | undefined;
            }[] & import("zod").BRAND<"HAS_SYSTEM_FIELDS">;
        };
        locationOptions: {
            label: string;
            options: {
                label: string;
                value: string;
                disabled?: boolean;
                icon?: string;
                slug?: string;
                credentialId?: number;
                supportsCustomLabel?: boolean;
            }[];
        }[];
        destinationCalendar: {
            id: number;
            userId: number | null;
            eventTypeId: number | null;
            createdAt: Date | null;
            updatedAt: Date | null;
            integration: string;
            externalId: string;
            primaryEmail: string | null;
            credentialId: number | null;
            delegationCredentialId: string | null;
            domainWideDelegationCredentialId: string | null;
        } | null;
        team: {
            name: string;
            id: number;
            slug: string | null;
            rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
            parentId: number | null;
            members: {
                user: {
                    name: string | null;
                    id: number;
                    locale: string | null;
                    email: string;
                    username: string | null;
                    avatarUrl: string | null;
                    timeZone: string;
                    defaultScheduleId: number | null;
                    isPlatformManaged: boolean;
                    eventTypes: {
                        slug: string;
                    }[];
                };
                role: import(".prisma/client").$Enums.MembershipRole;
                accepted: boolean;
            }[];
            parent: {
                organizationSettings: {
                    lockEventTypeCreationForUsers: boolean;
                } | null;
                slug: string | null;
            } | null;
        } | null;
        teamMembers: {
            profileId: number | null;
            eventTypes: string[];
            membership: import(".prisma/client").$Enums.MembershipRole;
            name: string | null;
            id: number;
            locale: string | null;
            email: string;
            username: string | null;
            avatarUrl: string | null;
            timeZone: string;
            defaultScheduleId: number | null;
            isPlatformManaged: boolean;
            nonProfileUsername: string | null;
            profile: import("@calcom/types/UserProfile").UserProfile;
            avatar: string;
        }[];
        currentUserMembership: {
            user: {
                name: string | null;
                id: number;
                locale: string | null;
                email: string;
                username: string | null;
                avatarUrl: string | null;
                timeZone: string;
                defaultScheduleId: number | null;
                isPlatformManaged: boolean;
                eventTypes: {
                    slug: string;
                }[];
            };
            role: import(".prisma/client").$Enums.MembershipRole;
            accepted: boolean;
        } | null;
        isUserOrganizationAdmin: boolean;
    };
}>;
