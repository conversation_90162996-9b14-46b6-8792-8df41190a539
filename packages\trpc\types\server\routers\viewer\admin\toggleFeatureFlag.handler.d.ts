import type { PrismaClient } from "@calcom/prisma";
import type { TrpcSessionUser } from "../../../types";
import type { TAdminToggleFeatureFlagSchema } from "./toggleFeatureFlag.schema";
type GetOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
        prisma: PrismaClient;
    };
    input: TAdminToggleFeatureFlagSchema;
};
export declare const toggleFeatureFlagHandler: (opts: GetOptions) => Promise<{
    type: import(".prisma/client").$Enums.FeatureType | null;
    description: string | null;
    createdAt: Date | null;
    updatedAt: Date | null;
    slug: string;
    enabled: boolean;
    lastUsedAt: Date | null;
    updatedBy: number | null;
    stale: boolean | null;
}>;
export default toggleFeatureFlagHandler;
