import type { z } from "zod";
import type { DelegationCredentialCreateSchema } from "./schema";
export default function handler({ input, ctx, }: {
    input: z.infer<typeof DelegationCredentialCreateSchema>;
    ctx: {
        user: {
            id: number;
            organizationId: number | null;
        };
    };
}): Promise<(Omit<{
    id: string;
    createdAt: Date;
    updatedAt: Date;
    organizationId: number;
    enabled: boolean;
    domain: string;
    workspacePlatformId: number;
    serviceAccountKey: import(".prisma/client").Prisma.JsonValue;
    lastEnabledAt: Date | null;
    lastDisabledAt: Date | null;
}, "serviceAccountKey"> & {
    serviceAccountKey: undefined;
}) | null | undefined>;
