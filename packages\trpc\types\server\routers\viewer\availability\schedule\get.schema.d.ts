import { z } from "zod";
export declare const ZGetInputSchema: z.ZodObject<{
    scheduleId: z.Zod<PERSON>ptional<z.ZodNumber>;
    isManagedEventType: z.ZodOptional<z.ZodBoolean>;
}, "strip", z.<PERSON>ype<PERSON>ny, {
    scheduleId?: number | undefined;
    isManagedEventType?: boolean | undefined;
}, {
    scheduleId?: number | undefined;
    isManagedEventType?: boolean | undefined;
}>;
export type TGetInputSchema = z.infer<typeof ZGetInputSchema>;
