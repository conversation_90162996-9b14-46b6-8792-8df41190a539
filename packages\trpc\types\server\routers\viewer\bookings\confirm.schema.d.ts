import type { z } from "zod";
export declare const ZConfirmInputSchema: z.ZodObject<{
    bookingId: z.ZodNumber;
    confirmed: z.ZodBoolean;
    recurringEventId: z.ZodOptional<z.ZodString>;
    reason: z.ZodOptional<z.ZodString>;
    emailsEnabled: z.ZodDefault<z.ZodBoolean>;
    platformClientParams: z.ZodOptional<z.ZodObject<{
        platformClientId: z.ZodOptional<z.ZodString>;
        platformRescheduleUrl: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        platformCancelUrl: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        platformBookingUrl: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        platformBookingLocation: z.ZodOptional<z.ZodString>;
        areCalendarEventsEnabled: z.ZodOptional<z.ZodBoolean>;
    }, "strip", z.Z<PERSON><PERSON><PERSON>Any, {
        platformClientId?: string | undefined;
        platformRescheduleUrl?: string | null | undefined;
        platformCancelUrl?: string | null | undefined;
        platformBookingUrl?: string | null | undefined;
        platformBookingLocation?: string | undefined;
        areCalendarEventsEnabled?: boolean | undefined;
    }, {
        platformClientId?: string | undefined;
        platformRescheduleUrl?: string | null | undefined;
        platformCancelUrl?: string | null | undefined;
        platformBookingUrl?: string | null | undefined;
        platformBookingLocation?: string | undefined;
        areCalendarEventsEnabled?: boolean | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    bookingId: number;
    confirmed: boolean;
    emailsEnabled: boolean;
    recurringEventId?: string | undefined;
    reason?: string | undefined;
    platformClientParams?: {
        platformClientId?: string | undefined;
        platformRescheduleUrl?: string | null | undefined;
        platformCancelUrl?: string | null | undefined;
        platformBookingUrl?: string | null | undefined;
        platformBookingLocation?: string | undefined;
        areCalendarEventsEnabled?: boolean | undefined;
    } | undefined;
}, {
    bookingId: number;
    confirmed: boolean;
    recurringEventId?: string | undefined;
    reason?: string | undefined;
    emailsEnabled?: boolean | undefined;
    platformClientParams?: {
        platformClientId?: string | undefined;
        platformRescheduleUrl?: string | null | undefined;
        platformCancelUrl?: string | null | undefined;
        platformBookingUrl?: string | null | undefined;
        platformBookingLocation?: string | undefined;
        areCalendarEventsEnabled?: boolean | undefined;
    } | undefined;
}>;
export type TConfirmInputSchema = z.infer<typeof ZConfirmInputSchema>;
