import { z } from "zod";
export declare const ZInviteMemberInputSchema: z.ZodObject<{
    teamId: z.Zod<PERSON>ber;
    usernameOrEmail: z.ZodEffects<z.ZodEffects<z.ZodEffects<z.ZodU<PERSON>n<[z.Zod<PERSON>, z.<PERSON><z.ZodUnion<[z.ZodString, z.ZodObject<{
        email: z.ZodString;
        role: z.ZodNativeEnum<{
            readonly MEMBER: "MEMBER";
            readonly ADMIN: "ADMIN";
            readonly OWNER: "OWNER";
        }>;
    }, "strip", z.<PERSON>odTypeAny, {
        role: "ADMIN" | "MEMBER" | "OWNER";
        email: string;
    }, {
        role: "ADMIN" | "MEMBER" | "OWNER";
        email: string;
    }>]>, "many">]>, string | (string | {
        email: string;
        role: "ADMIN" | "MEMBER" | "OWNER";
    })[], string | (string | {
        role: "ADMIN" | "MEMBER" | "OWNER";
        email: string;
    })[]>, string | (string | {
        email: string;
        role: "ADMIN" | "MEMBER" | "OWNER";
    })[], string | (string | {
        role: "ADMIN" | "MEMBER" | "OWNER";
        email: string;
    })[]>, string | (string | {
        email: string;
        role: "ADMIN" | "MEMBER" | "OWNER";
    })[], string | (string | {
        role: "ADMIN" | "MEMBER" | "OWNER";
        email: string;
    })[]>;
    role: z.ZodOptional<z.ZodNativeEnum<{
        readonly MEMBER: "MEMBER";
        readonly ADMIN: "ADMIN";
        readonly OWNER: "OWNER";
    }>>;
    language: z.ZodString;
    isPlatform: z.ZodOptional<z.ZodBoolean>;
    creationSource: z.ZodNativeEnum<{
        readonly API_V1: "API_V1";
        readonly API_V2: "API_V2";
        readonly WEBAPP: "WEBAPP";
    }>;
}, "strip", z.ZodTypeAny, {
    creationSource: "API_V1" | "API_V2" | "WEBAPP";
    teamId: number;
    language: string;
    usernameOrEmail: (string | (string | {
        email: string;
        role: "ADMIN" | "MEMBER" | "OWNER";
    })[]) & (string | (string | {
        email: string;
        role: "ADMIN" | "MEMBER" | "OWNER";
    })[] | undefined);
    role?: "ADMIN" | "MEMBER" | "OWNER" | undefined;
    isPlatform?: boolean | undefined;
}, {
    creationSource: "API_V1" | "API_V2" | "WEBAPP";
    teamId: number;
    language: string;
    usernameOrEmail: (string | (string | {
        role: "ADMIN" | "MEMBER" | "OWNER";
        email: string;
    })[]) & (string | (string | {
        role: "ADMIN" | "MEMBER" | "OWNER";
        email: string;
    })[] | undefined);
    role?: "ADMIN" | "MEMBER" | "OWNER" | undefined;
    isPlatform?: boolean | undefined;
}>;
export type TInviteMemberInputSchema = z.infer<typeof ZInviteMemberInputSchema>;
