export declare const loggedInViewerRouter: import("@trpc/server/unstable-core-do-not-import").CreateRouterInner<import("@trpc/server/unstable-core-do-not-import").RootConfig<{
    ctx: import("../../createContext").InnerContext;
    meta: object;
    errorShape: {
        message: string;
        code: number;
        data: {
            code: string;
            httpStatus: number;
            path?: string;
            [key: string]: unknown;
        };
    };
    transformer: {
        stringify: (object: any) => string;
        parse: <T = unknown>(string: string) => T;
        serialize: (object: any) => import("superjson/dist/types").SuperJSONResult;
        deserialize: <T_1 = unknown>(payload: import("superjson/dist/types").SuperJSONResult) => T_1;
        registerClass: (v: import("superjson/dist/types").Class, options?: string | import("superjson/dist/class-registry").RegisterOptions | undefined) => void;
        registerSymbol: (v: Symbol, identifier?: string | undefined) => void;
        registerCustom: <I, O extends import("superjson/dist/types").JSONValue>(transformer: Omit<import("superjson/dist/custom-transformer-registry").CustomTransfomer<I, O>, "name">, name: string) => void;
        allowErrorProps: (...props: string[]) => void;
    };
}>, {
    stripeCustomer: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: {
            isPremium: boolean;
            username: string | null;
        };
    }>;
    unlinkConnectedAccount: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: void;
        output: {
            message: string;
        };
    }>;
    eventTypeOrder: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            ids: number[];
        };
        output: void;
    }>;
    routingFormOrder: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            ids: string[];
        };
        output: void;
    }>;
    teamsAndUserProfilesQuery: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            includeOrg?: boolean | undefined;
            withPermission?: {
                permission: string;
                fallbackRoles?: string[] | undefined;
            } | undefined;
        } | undefined;
        output: ({
            teamId: number;
            name: string;
            slug: string | null;
            image: string;
            role: import(".prisma/client").$Enums.MembershipRole;
            readOnly: boolean;
        } | {
            teamId: null;
            name: string | null;
            slug: string | null;
            image: string;
            readOnly: boolean;
        })[];
    }>;
    connectAndJoin: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            token: string;
        };
        output: {
            isBookingAlreadyAcceptedBySomeoneElse: boolean;
            meetingUrl: string;
        };
    }>;
    addSecondaryEmail: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            email: string;
        };
        output: {
            data: {
                id: number;
                userId: number;
                email: string;
                emailVerified: Date | null;
            };
            message: string;
        };
    }>;
    addNotificationsSubscription: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            subscription: string;
        };
        output: {
            message: string;
        };
    }>;
    removeNotificationsSubscription: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            subscription: string;
        };
        output: {
            message: string;
        };
    }>;
    markNoShow: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            bookingUid: string;
            attendees?: {
                email: string;
                noShow: boolean;
            }[] | undefined;
            noShowHost?: boolean | undefined;
        };
        output: {
            attendees: import("@calcom/features/handleMarkNoShow").NoShowAttendees;
            noShowHost: boolean;
            message: string;
        };
    }>;
}>;
