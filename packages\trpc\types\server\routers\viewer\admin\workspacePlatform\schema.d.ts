import { z } from "zod";
export declare const workspacePlatformCreateSchema: z.ZodObject<{
    slug: z.ZodString;
    name: z.ZodString;
    description: z.ZodString;
    defaultServiceAccountKey: z.ZodOptional<z.ZodObject<{
        private_key: z.ZodString;
        client_email: z.ZodOptional<z.ZodString>;
        client_id: z.ZodString;
        tenant_id: z.ZodOptional<z.ZodString>;
    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
        private_key: z.ZodString;
        client_email: z.ZodOptional<z.ZodString>;
        client_id: z.ZodString;
        tenant_id: z.ZodOptional<z.ZodString>;
    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
        private_key: z.ZodString;
        client_email: z.ZodOptional<z.ZodString>;
        client_id: z.ZodString;
        tenant_id: z.ZodOptional<z.ZodString>;
    }, z.ZodTypeAny, "passthrough">>>;
    enabled: z.Zod<PERSON>efault<z.ZodOptional<z.ZodBoolean>>;
}, "strip", z.ZodTypeAny, {
    name: string;
    description: string;
    slug: string;
    enabled: boolean;
    defaultServiceAccountKey?: z.objectOutputType<{
        private_key: z.ZodString;
        client_email: z.ZodOptional<z.ZodString>;
        client_id: z.ZodString;
        tenant_id: z.ZodOptional<z.ZodString>;
    }, z.ZodTypeAny, "passthrough"> | undefined;
}, {
    name: string;
    description: string;
    slug: string;
    defaultServiceAccountKey?: z.objectInputType<{
        private_key: z.ZodString;
        client_email: z.ZodOptional<z.ZodString>;
        client_id: z.ZodString;
        tenant_id: z.ZodOptional<z.ZodString>;
    }, z.ZodTypeAny, "passthrough"> | undefined;
    enabled?: boolean | undefined;
}>;
export declare const workspacePlatformUpdateSchema: z.ZodObject<{
    id: z.ZodNumber;
    name: z.ZodString;
    description: z.ZodString;
}, "strip", z.ZodTypeAny, {
    name: string;
    id: number;
    description: string;
}, {
    name: string;
    id: number;
    description: string;
}>;
export declare const workspacePlatformUpdateServiceAccountSchema: z.ZodObject<{
    id: z.ZodNumber;
    defaultServiceAccountKey: z.ZodObject<{
        private_key: z.ZodString;
        client_email: z.ZodOptional<z.ZodString>;
        client_id: z.ZodString;
        tenant_id: z.ZodOptional<z.ZodString>;
    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
        private_key: z.ZodString;
        client_email: z.ZodOptional<z.ZodString>;
        client_id: z.ZodString;
        tenant_id: z.ZodOptional<z.ZodString>;
    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
        private_key: z.ZodString;
        client_email: z.ZodOptional<z.ZodString>;
        client_id: z.ZodString;
        tenant_id: z.ZodOptional<z.ZodString>;
    }, z.ZodTypeAny, "passthrough">>;
}, "strip", z.ZodTypeAny, {
    id: number;
    defaultServiceAccountKey: {
        client_id: string;
        private_key: string;
        client_email?: string | undefined;
        tenant_id?: string | undefined;
    } & {
        [k: string]: unknown;
    };
}, {
    id: number;
    defaultServiceAccountKey: {
        client_id: string;
        private_key: string;
        client_email?: string | undefined;
        tenant_id?: string | undefined;
    } & {
        [k: string]: unknown;
    };
}>;
export declare const workspacePlatformToggleEnabledSchema: z.ZodObject<{
    id: z.ZodNumber;
    enabled: z.ZodBoolean;
}, "strip", z.ZodTypeAny, {
    id: number;
    enabled: boolean;
}, {
    id: number;
    enabled: boolean;
}>;
