export declare const appsRouter: import("@trpc/server/unstable-core-do-not-import").CreateRouterInner<import("@trpc/server/unstable-core-do-not-import").RootConfig<{
    ctx: import("../../../createContext").InnerContext;
    meta: object;
    errorShape: {
        message: string;
        code: number;
        data: {
            code: string;
            httpStatus: number;
            path?: string;
            [key: string]: unknown;
        };
    };
    transformer: {
        stringify: (object: any) => string;
        parse: <T = unknown>(string: string) => T;
        serialize: (object: any) => import("superjson/dist/types").SuperJSONResult;
        deserialize: <T_1 = unknown>(payload: import("superjson/dist/types").SuperJSONResult) => T_1;
        registerClass: (v: import("superjson/dist/types").Class, options?: string | import("superjson/dist/class-registry").RegisterOptions | undefined) => void;
        registerSymbol: (v: Symbol, identifier?: string | undefined) => void;
        registerCustom: <I, O extends import("superjson/dist/types").JSONValue>(transformer: Omit<import("superjson/dist/custom-transformer-registry").CustomTransfomer<I, O>, "name">, name: string) => void;
        allowErrorProps: (...props: string[]) => void;
    };
}>, {
    appById: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            appId: string;
        };
        output: {
            installed?: boolean;
            type: `${string}_calendar` | `${string}_messaging` | `${string}_payment` | `${string}_video` | `${string}_other` | `${string}_automation` | `${string}_analytics` | `${string}_crm` | `${string}_other_calendar`;
            title?: string;
            name: string;
            description: string;
            variant: "calendar" | "payment" | "conferencing" | "video" | "other" | "other_calendar" | "automation" | "crm";
            slug: string;
            category?: string;
            categories: import(".prisma/client").AppCategories[];
            extendsFeature?: "EventType" | "User";
            logo: string;
            publisher: string;
            url: string;
            docsUrl?: string;
            verified?: boolean;
            trending?: boolean;
            rating?: number;
            reviews?: number;
            isGlobal?: boolean;
            simplePath?: string;
            email: string;
            key?: import(".prisma/client").Prisma.JsonValue;
            feeType?: "monthly" | "usage-based" | "one-time" | "free";
            price?: number;
            commission?: number;
            licenseRequired?: boolean;
            teamsPlanRequired?: {
                upgradeUrl: string;
            };
            appData?: import("@calcom/types/App").AppData;
            paid?: import("@calcom/types/App").PaidAppData;
            dirName?: string;
            isTemplate?: boolean;
            __template?: string;
            dependencies?: string[];
            concurrentMeetings?: boolean;
            createdAt?: string;
            isOAuth?: boolean;
            delegationCredential?: {
                workspacePlatformSlug: string;
            };
            locationOption: import("@calcom/app-store/utils").LocationOption | null;
            isInstalled: number;
        };
    }>;
    appCredentialsByType: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            appType: string;
        };
        output: {
            credentials: ({
                id: number;
                delegatedToId: string;
                userId: number;
                user: {
                    email: string;
                };
                key: {
                    access_token: string;
                };
                invalid: boolean;
                teamId: null;
                team: null;
                delegationCredentialId: string;
                type: `${string}_other` | `${string}_calendar` | `${string}_messaging` | `${string}_payment` | `${string}_video` | `${string}_automation` | `${string}_analytics` | `${string}_crm` | `${string}_other_calendar`;
                appId: string;
            } | {
                user: {
                    name: string | null;
                } | null;
                id: number;
                type: string;
                userId: number | null;
                team: {
                    name: string;
                } | null;
                delegationCredentialId: string | null;
                teamId: number | null;
                appId: string | null;
                invalid: boolean | null;
            })[];
            userAdminTeams: number[];
        };
    }>;
    getUsersDefaultConferencingApp: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: {
            appSlug?: string | undefined;
            appLink?: string | undefined;
        } | undefined;
    }>;
    integrations: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            variant?: string | undefined;
            exclude?: string[] | undefined;
            onlyInstalled?: boolean | undefined;
            includeTeamInstalledApps?: boolean | undefined;
            extendsFeature?: "EventType" | undefined;
            teamId?: number | null | undefined;
            sortByMostPopular?: boolean | undefined;
            sortByInstalledFirst?: boolean | undefined;
            categories?: ("calendar" | "messaging" | "other" | "payment" | "video" | "web3" | "automation" | "analytics" | "conferencing" | "crm")[] | undefined;
            appId?: string | undefined;
        };
        output: {
            items: {
                dependencyData?: import("@calcom/app-store/_appRegistry").TDependencyData | undefined;
                userCredentialIds: number[];
                invalidCredentialIds: number[];
                teams: ({
                    teamId: number;
                    name: string;
                    logoUrl: string | null;
                    credentialId: number;
                    isAdmin: boolean;
                } | null)[];
                isInstalled: boolean | undefined;
                isSetupAlready: boolean | undefined;
                credentialOwner?: import("@calcom/app-store/types").CredentialOwner | undefined;
                installed?: boolean;
                type: `${string}_calendar` | `${string}_messaging` | `${string}_payment` | `${string}_video` | `${string}_other` | `${string}_automation` | `${string}_analytics` | `${string}_crm` | `${string}_other_calendar`;
                title?: string;
                name: string;
                description: string;
                variant: "calendar" | "payment" | "conferencing" | "video" | "other" | "other_calendar" | "automation" | "crm";
                slug: string;
                category?: string;
                categories: import(".prisma/client").AppCategories[];
                extendsFeature?: "EventType" | "User";
                logo: string;
                publisher: string;
                url: string;
                docsUrl?: string;
                verified?: boolean;
                trending?: boolean;
                rating?: number;
                reviews?: number;
                isGlobal?: boolean;
                simplePath?: string;
                email: string;
                feeType?: "monthly" | "usage-based" | "one-time" | "free";
                price?: number;
                commission?: number;
                licenseRequired?: boolean;
                teamsPlanRequired?: {
                    upgradeUrl: string;
                };
                appData?: import("@calcom/types/App").AppData;
                paid?: import("@calcom/types/App").PaidAppData;
                dirName?: string;
                isTemplate?: boolean;
                __template?: string;
                dependencies?: string[];
                concurrentMeetings?: boolean;
                createdAt?: string;
                isOAuth?: boolean;
                delegationCredential?: {
                    workspacePlatformSlug: string;
                };
                locationOption: import("@calcom/app-store/utils").LocationOption | null;
                enabled: boolean;
            }[];
        };
    }>;
    listLocal: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            category: string;
        };
        output: ({
            name: string;
            slug: string;
            logo: string;
            title: string | undefined;
            type: `${string}_other` | `${string}_calendar` | `${string}_messaging` | `${string}_payment` | `${string}_video` | `${string}_automation` | `${string}_analytics` | `${string}_crm` | `${string}_other_calendar`;
            description: string;
            keys: import(".prisma/client").Prisma.JsonObject | null;
            dirName: string;
            enabled: boolean;
            isTemplate: boolean | undefined;
        } | {
            name: string;
            slug: string;
            logo: string;
            type: `${string}_other` | `${string}_calendar` | `${string}_messaging` | `${string}_payment` | `${string}_video` | `${string}_automation` | `${string}_analytics` | `${string}_crm` | `${string}_other_calendar`;
            title: string | undefined;
            description: string;
            enabled: boolean;
            dirName: string;
            keys: Record<string, string> | null;
            isTemplate?: undefined;
        })[];
    }>;
    locationOptions: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            teamId?: number | undefined;
        };
        output: {
            label: string;
            options: {
                label: string;
                value: string;
                disabled?: boolean;
                icon?: string;
                slug?: string;
                credentialId?: number;
                supportsCustomLabel?: boolean;
            }[];
        }[];
    }>;
    toggle: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            slug: string;
            enabled: boolean;
        };
        output: boolean;
    }>;
    saveKeys: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            type: string;
            slug: string;
            dirName: string;
            keys?: unknown;
            fromEnabled?: boolean | undefined;
        };
        output: void;
    }>;
    checkForGCal: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: boolean;
    }>;
    setDefaultConferencingApp: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            slug: string;
        };
        output: void;
    }>;
    updateAppCredentials: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            key: {} & {
                [k: string]: unknown;
            };
            credentialId: number;
        };
        output: boolean;
    }>;
    queryForDependencies: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: string[] | undefined;
        output: {
            name: string;
            slug: string;
            installed: boolean;
        }[] | undefined;
    }>;
    checkGlobalKeys: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            slug: string;
        };
        output: boolean;
    }>;
    updateUserDefaultConferencingApp: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            appSlug?: string | undefined;
            appLink?: string | undefined;
        };
        output: {
            appSlug?: string | undefined;
            appLink?: string | undefined;
        };
    }>;
}>;
