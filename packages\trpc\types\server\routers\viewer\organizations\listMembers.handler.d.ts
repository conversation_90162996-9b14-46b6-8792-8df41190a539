import type { TrpcSessionUser } from "../../../types";
import type { TListMembersSchema } from "./listMembers.schema";
type GetOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TListMembersSchema;
};
export declare const listMembersHandler: ({ ctx, input }: GetOptions) => Promise<{
    canUserGetMembers: boolean;
    rows: never[];
    meta: {
        totalRowCount: number;
    };
} | {
    rows: {
        teams: {
            id: number;
            name: string;
            slug: string | null;
        }[];
        attributes: {
            weight: number;
            id: string;
            contains: string[];
            value: string;
            slug: string;
            attributeId: string;
            isGroup: boolean;
        }[] | undefined;
        twoFactorEnabled?: boolean | undefined;
        id: number;
        username: string | null;
        email: string;
        timeZone: string;
        role: import(".prisma/client").$Enums.MembershipRole;
        customRole: {
            name: string;
            color: string | null;
            id: string;
            type: import(".prisma/client").$Enums.RoleType;
            description: string | null;
            createdAt: Date;
            updatedAt: Date;
            teamId: number | null;
        } | null;
        accepted: boolean;
        disableImpersonation: boolean;
        completedOnboarding: boolean;
        lastActiveAt: string | null;
        createdAt: string | null;
        updatedAt: string | null;
        avatarUrl: string | null;
    }[];
    meta: {
        totalRowCount: number;
    };
    canUserGetMembers?: undefined;
}>;
export default listMembersHandler;
