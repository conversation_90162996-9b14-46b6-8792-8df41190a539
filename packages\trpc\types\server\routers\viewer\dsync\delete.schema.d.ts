import { z } from "zod";
export declare const ZDeleteInputSchema: z.ZodObject<{
    organizationId: z.Z<PERSON><[z.<PERSON><PERSON><PERSON><PERSON><PERSON>, z.ZodNull]>;
    directoryId: z.ZodString;
}, "strip", z.<PERSON>od<PERSON>ype<PERSON>ny, {
    organizationId: number | null;
    directoryId: string;
}, {
    organizationId: number | null;
    directoryId: string;
}>;
export type ZDeleteInputSchema = z.infer<typeof ZDeleteInputSchema>;
