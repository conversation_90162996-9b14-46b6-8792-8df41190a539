import { z } from "zod";
export declare const ZRemoveMemberInputSchema: z.ZodObject<{
    teamIds: z.<PERSON><z.<PERSON>odN<PERSON><PERSON>, "many">;
    memberIds: z.<PERSON><z.<PERSON>od<PERSON>, "many">;
    isOrg: z.<PERSON><z.ZodBoolean>;
}, "strip", z.<PERSON>, {
    teamIds: number[];
    isOrg: boolean;
    memberIds: number[];
}, {
    teamIds: number[];
    memberIds: number[];
    isOrg?: boolean | undefined;
}>;
export type TRemoveMemberInputSchema = z.infer<typeof ZRemoveMemberInputSchema>;
