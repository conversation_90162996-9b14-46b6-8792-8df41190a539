import type { z } from "zod";
export declare const ZCreateInputSchema: z.Zod<PERSON>ffects<z.ZodObject<{
    length: z.ZodNumber;
    title: z.ZodString;
    metadata: z.ZodOptional<z.ZodNullable<z.ZodObject<{
        config: z.ZodOptional<z.ZodObject<{
            useHostSchedulesForTeamEvent: z.ZodOptional<z.ZodBoolean>;
        }, "strip", z.ZodTypeAny, {
            useHostSchedulesForTeamEvent?: boolean | undefined;
        }, {
            useHostSchedulesForTeamEvent?: boolean | undefined;
        }>>;
        smartContractAddress: z.ZodOptional<z.ZodString>;
        blockchainId: z.ZodOptional<z.ZodNumber>;
        multipleDuration: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        giphyThankYouPage: z.<PERSON>od<PERSON>ptional<z.ZodString>;
        additionalNotesRequired: z.<PERSON>ptional<z.ZodBoolean>;
        disableSuccessPage: z.<PERSON>od<PERSON>ptional<z.ZodBoolean>;
        disableStandardEmails: z.ZodOptional<z.ZodObject<{
            all: z.ZodOptional<z.ZodObject<{
                host: z.ZodOptional<z.ZodBoolean>;
                attendee: z.ZodOptional<z.ZodBoolean>;
            }, "strip", z.ZodTypeAny, {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            }, {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            }>>;
            confirmation: z.ZodOptional<z.ZodObject<{
                host: z.ZodOptional<z.ZodBoolean>;
                attendee: z.ZodOptional<z.ZodBoolean>;
            }, "strip", z.ZodTypeAny, {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            }, {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            }>>;
        }, "strip", z.ZodTypeAny, {
            all?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
            confirmation?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
        }, {
            all?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
            confirmation?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
        }>>;
        managedEventConfig: z.ZodOptional<z.ZodObject<{
            unlockedFields: z.ZodOptional<z.ZodType<{
                users?: true | undefined;
                children?: true | undefined;
                length?: true | undefined;
                title?: true | undefined;
                metadata?: true | undefined;
                description?: true | undefined;
                userId?: true | undefined;
                calVideoSettings?: true | undefined;
                destinationCalendar?: true | undefined;
                profile?: true | undefined;
                team?: true | undefined;
                schedule?: true | undefined;
                availability?: true | undefined;
                hashedLink?: true | undefined;
                secondaryEmail?: true | undefined;
                customInputs?: true | undefined;
                timeZone?: true | undefined;
                bookings?: true | undefined;
                selectedCalendars?: true | undefined;
                webhooks?: true | undefined;
                workflows?: true | undefined;
                hosts?: true | undefined;
                slug?: true | undefined;
                parentId?: true | undefined;
                bookingLimits?: true | undefined;
                parent?: true | undefined;
                teamId?: true | undefined;
                hidden?: true | undefined;
                _count?: true | undefined;
                interfaceLanguage?: true | undefined;
                position?: true | undefined;
                locations?: true | undefined;
                offsetStart?: true | undefined;
                profileId?: true | undefined;
                useEventLevelSelectedCalendars?: true | undefined;
                eventName?: true | undefined;
                bookingFields?: true | undefined;
                periodType?: true | undefined;
                periodStartDate?: true | undefined;
                periodEndDate?: true | undefined;
                periodDays?: true | undefined;
                periodCountCalendarDays?: true | undefined;
                lockTimeZoneToggleOnBookingPage?: true | undefined;
                lockedTimeZone?: true | undefined;
                requiresConfirmation?: true | undefined;
                requiresConfirmationWillBlockSlot?: true | undefined;
                requiresConfirmationForFreeEmail?: true | undefined;
                requiresBookerEmailVerification?: true | undefined;
                canSendCalVideoTranscriptionEmails?: true | undefined;
                autoTranslateDescriptionEnabled?: true | undefined;
                recurringEvent?: true | undefined;
                disableGuests?: true | undefined;
                hideCalendarNotes?: true | undefined;
                hideCalendarEventDetails?: true | undefined;
                minimumBookingNotice?: true | undefined;
                beforeEventBuffer?: true | undefined;
                afterEventBuffer?: true | undefined;
                seatsPerTimeSlot?: true | undefined;
                onlyShowFirstAvailableSlot?: true | undefined;
                disableCancelling?: true | undefined;
                disableRescheduling?: true | undefined;
                seatsShowAttendees?: true | undefined;
                seatsShowAvailabilityCount?: true | undefined;
                schedulingType?: true | undefined;
                scheduleId?: true | undefined;
                allowReschedulingCancelledBookings?: true | undefined;
                price?: true | undefined;
                currency?: true | undefined;
                slotInterval?: true | undefined;
                successRedirectUrl?: true | undefined;
                forwardParamsSuccessRedirect?: true | undefined;
                durationLimits?: true | undefined;
                isInstantEvent?: true | undefined;
                instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                instantMeetingScheduleId?: true | undefined;
                instantMeetingParameters?: true | undefined;
                assignAllTeamMembers?: true | undefined;
                assignRRMembersUsingSegment?: true | undefined;
                rrSegmentQueryValue?: true | undefined;
                useEventTypeDestinationCalendarEmail?: true | undefined;
                isRRWeightsEnabled?: true | undefined;
                maxLeadThreshold?: true | undefined;
                includeNoShowInRRCalculation?: true | undefined;
                allowReschedulingPastBookings?: true | undefined;
                hideOrganizerEmail?: true | undefined;
                maxActiveBookingsPerBooker?: true | undefined;
                maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                customReplyToEmail?: true | undefined;
                eventTypeColor?: true | undefined;
                rescheduleWithSameRoundRobinHost?: true | undefined;
                secondaryEmailId?: true | undefined;
                useBookerTimezone?: true | undefined;
                restrictionScheduleId?: true | undefined;
                bookingRequiresAuthentication?: true | undefined;
                owner?: true | undefined;
                instantMeetingSchedule?: true | undefined;
                aiPhoneCallConfig?: true | undefined;
                fieldTranslations?: true | undefined;
                restrictionSchedule?: true | undefined;
                hostGroups?: true | undefined;
            }, z.ZodTypeDef, {
                users?: true | undefined;
                children?: true | undefined;
                length?: true | undefined;
                title?: true | undefined;
                metadata?: true | undefined;
                description?: true | undefined;
                userId?: true | undefined;
                calVideoSettings?: true | undefined;
                destinationCalendar?: true | undefined;
                profile?: true | undefined;
                team?: true | undefined;
                schedule?: true | undefined;
                availability?: true | undefined;
                hashedLink?: true | undefined;
                secondaryEmail?: true | undefined;
                customInputs?: true | undefined;
                timeZone?: true | undefined;
                bookings?: true | undefined;
                selectedCalendars?: true | undefined;
                webhooks?: true | undefined;
                workflows?: true | undefined;
                hosts?: true | undefined;
                slug?: true | undefined;
                parentId?: true | undefined;
                bookingLimits?: true | undefined;
                parent?: true | undefined;
                teamId?: true | undefined;
                hidden?: true | undefined;
                _count?: true | undefined;
                interfaceLanguage?: true | undefined;
                position?: true | undefined;
                locations?: true | undefined;
                offsetStart?: true | undefined;
                profileId?: true | undefined;
                useEventLevelSelectedCalendars?: true | undefined;
                eventName?: true | undefined;
                bookingFields?: true | undefined;
                periodType?: true | undefined;
                periodStartDate?: true | undefined;
                periodEndDate?: true | undefined;
                periodDays?: true | undefined;
                periodCountCalendarDays?: true | undefined;
                lockTimeZoneToggleOnBookingPage?: true | undefined;
                lockedTimeZone?: true | undefined;
                requiresConfirmation?: true | undefined;
                requiresConfirmationWillBlockSlot?: true | undefined;
                requiresConfirmationForFreeEmail?: true | undefined;
                requiresBookerEmailVerification?: true | undefined;
                canSendCalVideoTranscriptionEmails?: true | undefined;
                autoTranslateDescriptionEnabled?: true | undefined;
                recurringEvent?: true | undefined;
                disableGuests?: true | undefined;
                hideCalendarNotes?: true | undefined;
                hideCalendarEventDetails?: true | undefined;
                minimumBookingNotice?: true | undefined;
                beforeEventBuffer?: true | undefined;
                afterEventBuffer?: true | undefined;
                seatsPerTimeSlot?: true | undefined;
                onlyShowFirstAvailableSlot?: true | undefined;
                disableCancelling?: true | undefined;
                disableRescheduling?: true | undefined;
                seatsShowAttendees?: true | undefined;
                seatsShowAvailabilityCount?: true | undefined;
                schedulingType?: true | undefined;
                scheduleId?: true | undefined;
                allowReschedulingCancelledBookings?: true | undefined;
                price?: true | undefined;
                currency?: true | undefined;
                slotInterval?: true | undefined;
                successRedirectUrl?: true | undefined;
                forwardParamsSuccessRedirect?: true | undefined;
                durationLimits?: true | undefined;
                isInstantEvent?: true | undefined;
                instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                instantMeetingScheduleId?: true | undefined;
                instantMeetingParameters?: true | undefined;
                assignAllTeamMembers?: true | undefined;
                assignRRMembersUsingSegment?: true | undefined;
                rrSegmentQueryValue?: true | undefined;
                useEventTypeDestinationCalendarEmail?: true | undefined;
                isRRWeightsEnabled?: true | undefined;
                maxLeadThreshold?: true | undefined;
                includeNoShowInRRCalculation?: true | undefined;
                allowReschedulingPastBookings?: true | undefined;
                hideOrganizerEmail?: true | undefined;
                maxActiveBookingsPerBooker?: true | undefined;
                maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                customReplyToEmail?: true | undefined;
                eventTypeColor?: true | undefined;
                rescheduleWithSameRoundRobinHost?: true | undefined;
                secondaryEmailId?: true | undefined;
                useBookerTimezone?: true | undefined;
                restrictionScheduleId?: true | undefined;
                bookingRequiresAuthentication?: true | undefined;
                owner?: true | undefined;
                instantMeetingSchedule?: true | undefined;
                aiPhoneCallConfig?: true | undefined;
                fieldTranslations?: true | undefined;
                restrictionSchedule?: true | undefined;
                hostGroups?: true | undefined;
            }>>;
        }, "strip", z.ZodTypeAny, {
            unlockedFields?: {
                users?: true | undefined;
                children?: true | undefined;
                length?: true | undefined;
                title?: true | undefined;
                metadata?: true | undefined;
                description?: true | undefined;
                userId?: true | undefined;
                calVideoSettings?: true | undefined;
                destinationCalendar?: true | undefined;
                profile?: true | undefined;
                team?: true | undefined;
                schedule?: true | undefined;
                availability?: true | undefined;
                hashedLink?: true | undefined;
                secondaryEmail?: true | undefined;
                customInputs?: true | undefined;
                timeZone?: true | undefined;
                bookings?: true | undefined;
                selectedCalendars?: true | undefined;
                webhooks?: true | undefined;
                workflows?: true | undefined;
                hosts?: true | undefined;
                slug?: true | undefined;
                parentId?: true | undefined;
                bookingLimits?: true | undefined;
                parent?: true | undefined;
                teamId?: true | undefined;
                hidden?: true | undefined;
                _count?: true | undefined;
                interfaceLanguage?: true | undefined;
                position?: true | undefined;
                locations?: true | undefined;
                offsetStart?: true | undefined;
                profileId?: true | undefined;
                useEventLevelSelectedCalendars?: true | undefined;
                eventName?: true | undefined;
                bookingFields?: true | undefined;
                periodType?: true | undefined;
                periodStartDate?: true | undefined;
                periodEndDate?: true | undefined;
                periodDays?: true | undefined;
                periodCountCalendarDays?: true | undefined;
                lockTimeZoneToggleOnBookingPage?: true | undefined;
                lockedTimeZone?: true | undefined;
                requiresConfirmation?: true | undefined;
                requiresConfirmationWillBlockSlot?: true | undefined;
                requiresConfirmationForFreeEmail?: true | undefined;
                requiresBookerEmailVerification?: true | undefined;
                canSendCalVideoTranscriptionEmails?: true | undefined;
                autoTranslateDescriptionEnabled?: true | undefined;
                recurringEvent?: true | undefined;
                disableGuests?: true | undefined;
                hideCalendarNotes?: true | undefined;
                hideCalendarEventDetails?: true | undefined;
                minimumBookingNotice?: true | undefined;
                beforeEventBuffer?: true | undefined;
                afterEventBuffer?: true | undefined;
                seatsPerTimeSlot?: true | undefined;
                onlyShowFirstAvailableSlot?: true | undefined;
                disableCancelling?: true | undefined;
                disableRescheduling?: true | undefined;
                seatsShowAttendees?: true | undefined;
                seatsShowAvailabilityCount?: true | undefined;
                schedulingType?: true | undefined;
                scheduleId?: true | undefined;
                allowReschedulingCancelledBookings?: true | undefined;
                price?: true | undefined;
                currency?: true | undefined;
                slotInterval?: true | undefined;
                successRedirectUrl?: true | undefined;
                forwardParamsSuccessRedirect?: true | undefined;
                durationLimits?: true | undefined;
                isInstantEvent?: true | undefined;
                instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                instantMeetingScheduleId?: true | undefined;
                instantMeetingParameters?: true | undefined;
                assignAllTeamMembers?: true | undefined;
                assignRRMembersUsingSegment?: true | undefined;
                rrSegmentQueryValue?: true | undefined;
                useEventTypeDestinationCalendarEmail?: true | undefined;
                isRRWeightsEnabled?: true | undefined;
                maxLeadThreshold?: true | undefined;
                includeNoShowInRRCalculation?: true | undefined;
                allowReschedulingPastBookings?: true | undefined;
                hideOrganizerEmail?: true | undefined;
                maxActiveBookingsPerBooker?: true | undefined;
                maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                customReplyToEmail?: true | undefined;
                eventTypeColor?: true | undefined;
                rescheduleWithSameRoundRobinHost?: true | undefined;
                secondaryEmailId?: true | undefined;
                useBookerTimezone?: true | undefined;
                restrictionScheduleId?: true | undefined;
                bookingRequiresAuthentication?: true | undefined;
                owner?: true | undefined;
                instantMeetingSchedule?: true | undefined;
                aiPhoneCallConfig?: true | undefined;
                fieldTranslations?: true | undefined;
                restrictionSchedule?: true | undefined;
                hostGroups?: true | undefined;
            } | undefined;
        }, {
            unlockedFields?: {
                users?: true | undefined;
                children?: true | undefined;
                length?: true | undefined;
                title?: true | undefined;
                metadata?: true | undefined;
                description?: true | undefined;
                userId?: true | undefined;
                calVideoSettings?: true | undefined;
                destinationCalendar?: true | undefined;
                profile?: true | undefined;
                team?: true | undefined;
                schedule?: true | undefined;
                availability?: true | undefined;
                hashedLink?: true | undefined;
                secondaryEmail?: true | undefined;
                customInputs?: true | undefined;
                timeZone?: true | undefined;
                bookings?: true | undefined;
                selectedCalendars?: true | undefined;
                webhooks?: true | undefined;
                workflows?: true | undefined;
                hosts?: true | undefined;
                slug?: true | undefined;
                parentId?: true | undefined;
                bookingLimits?: true | undefined;
                parent?: true | undefined;
                teamId?: true | undefined;
                hidden?: true | undefined;
                _count?: true | undefined;
                interfaceLanguage?: true | undefined;
                position?: true | undefined;
                locations?: true | undefined;
                offsetStart?: true | undefined;
                profileId?: true | undefined;
                useEventLevelSelectedCalendars?: true | undefined;
                eventName?: true | undefined;
                bookingFields?: true | undefined;
                periodType?: true | undefined;
                periodStartDate?: true | undefined;
                periodEndDate?: true | undefined;
                periodDays?: true | undefined;
                periodCountCalendarDays?: true | undefined;
                lockTimeZoneToggleOnBookingPage?: true | undefined;
                lockedTimeZone?: true | undefined;
                requiresConfirmation?: true | undefined;
                requiresConfirmationWillBlockSlot?: true | undefined;
                requiresConfirmationForFreeEmail?: true | undefined;
                requiresBookerEmailVerification?: true | undefined;
                canSendCalVideoTranscriptionEmails?: true | undefined;
                autoTranslateDescriptionEnabled?: true | undefined;
                recurringEvent?: true | undefined;
                disableGuests?: true | undefined;
                hideCalendarNotes?: true | undefined;
                hideCalendarEventDetails?: true | undefined;
                minimumBookingNotice?: true | undefined;
                beforeEventBuffer?: true | undefined;
                afterEventBuffer?: true | undefined;
                seatsPerTimeSlot?: true | undefined;
                onlyShowFirstAvailableSlot?: true | undefined;
                disableCancelling?: true | undefined;
                disableRescheduling?: true | undefined;
                seatsShowAttendees?: true | undefined;
                seatsShowAvailabilityCount?: true | undefined;
                schedulingType?: true | undefined;
                scheduleId?: true | undefined;
                allowReschedulingCancelledBookings?: true | undefined;
                price?: true | undefined;
                currency?: true | undefined;
                slotInterval?: true | undefined;
                successRedirectUrl?: true | undefined;
                forwardParamsSuccessRedirect?: true | undefined;
                durationLimits?: true | undefined;
                isInstantEvent?: true | undefined;
                instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                instantMeetingScheduleId?: true | undefined;
                instantMeetingParameters?: true | undefined;
                assignAllTeamMembers?: true | undefined;
                assignRRMembersUsingSegment?: true | undefined;
                rrSegmentQueryValue?: true | undefined;
                useEventTypeDestinationCalendarEmail?: true | undefined;
                isRRWeightsEnabled?: true | undefined;
                maxLeadThreshold?: true | undefined;
                includeNoShowInRRCalculation?: true | undefined;
                allowReschedulingPastBookings?: true | undefined;
                hideOrganizerEmail?: true | undefined;
                maxActiveBookingsPerBooker?: true | undefined;
                maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                customReplyToEmail?: true | undefined;
                eventTypeColor?: true | undefined;
                rescheduleWithSameRoundRobinHost?: true | undefined;
                secondaryEmailId?: true | undefined;
                useBookerTimezone?: true | undefined;
                restrictionScheduleId?: true | undefined;
                bookingRequiresAuthentication?: true | undefined;
                owner?: true | undefined;
                instantMeetingSchedule?: true | undefined;
                aiPhoneCallConfig?: true | undefined;
                fieldTranslations?: true | undefined;
                restrictionSchedule?: true | undefined;
                hostGroups?: true | undefined;
            } | undefined;
        }>>;
        requiresConfirmationThreshold: z.ZodOptional<z.ZodObject<{
            time: z.ZodNumber;
            unit: z.ZodType<import("dayjs").UnitTypeLongPlural, z.ZodTypeDef, import("dayjs").UnitTypeLongPlural>;
        }, "strip", z.ZodTypeAny, {
            time: number;
            unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
        }, {
            time: number;
            unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
        }>>;
        bookerLayouts: z.ZodOptional<z.ZodNullable<z.ZodObject<{
            enabledLayouts: z.ZodArray<z.ZodUnion<[z.ZodLiteral<import("@calcom/prisma/zod-utils").BookerLayouts>, z.ZodLiteral<import("@calcom/prisma/zod-utils").BookerLayouts>, z.ZodLiteral<import("@calcom/prisma/zod-utils").BookerLayouts>]>, "many">;
            defaultLayout: z.ZodUnion<[z.ZodLiteral<import("@calcom/prisma/zod-utils").BookerLayouts>, z.ZodLiteral<import("@calcom/prisma/zod-utils").BookerLayouts>, z.ZodLiteral<import("@calcom/prisma/zod-utils").BookerLayouts>]>;
        }, "strip", z.ZodTypeAny, {
            enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
            defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
        }, {
            enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
            defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
        }>>>;
        apps: z.ZodOptional<z.ZodUnknown>;
    }, "strip", z.ZodTypeAny, {
        config?: {
            useHostSchedulesForTeamEvent?: boolean | undefined;
        } | undefined;
        smartContractAddress?: string | undefined;
        blockchainId?: number | undefined;
        multipleDuration?: number[] | undefined;
        giphyThankYouPage?: string | undefined;
        additionalNotesRequired?: boolean | undefined;
        disableSuccessPage?: boolean | undefined;
        disableStandardEmails?: {
            all?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
            confirmation?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
        } | undefined;
        managedEventConfig?: {
            unlockedFields?: {
                users?: true | undefined;
                children?: true | undefined;
                length?: true | undefined;
                title?: true | undefined;
                metadata?: true | undefined;
                description?: true | undefined;
                userId?: true | undefined;
                calVideoSettings?: true | undefined;
                destinationCalendar?: true | undefined;
                profile?: true | undefined;
                team?: true | undefined;
                schedule?: true | undefined;
                availability?: true | undefined;
                hashedLink?: true | undefined;
                secondaryEmail?: true | undefined;
                customInputs?: true | undefined;
                timeZone?: true | undefined;
                bookings?: true | undefined;
                selectedCalendars?: true | undefined;
                webhooks?: true | undefined;
                workflows?: true | undefined;
                hosts?: true | undefined;
                slug?: true | undefined;
                parentId?: true | undefined;
                bookingLimits?: true | undefined;
                parent?: true | undefined;
                teamId?: true | undefined;
                hidden?: true | undefined;
                _count?: true | undefined;
                interfaceLanguage?: true | undefined;
                position?: true | undefined;
                locations?: true | undefined;
                offsetStart?: true | undefined;
                profileId?: true | undefined;
                useEventLevelSelectedCalendars?: true | undefined;
                eventName?: true | undefined;
                bookingFields?: true | undefined;
                periodType?: true | undefined;
                periodStartDate?: true | undefined;
                periodEndDate?: true | undefined;
                periodDays?: true | undefined;
                periodCountCalendarDays?: true | undefined;
                lockTimeZoneToggleOnBookingPage?: true | undefined;
                lockedTimeZone?: true | undefined;
                requiresConfirmation?: true | undefined;
                requiresConfirmationWillBlockSlot?: true | undefined;
                requiresConfirmationForFreeEmail?: true | undefined;
                requiresBookerEmailVerification?: true | undefined;
                canSendCalVideoTranscriptionEmails?: true | undefined;
                autoTranslateDescriptionEnabled?: true | undefined;
                recurringEvent?: true | undefined;
                disableGuests?: true | undefined;
                hideCalendarNotes?: true | undefined;
                hideCalendarEventDetails?: true | undefined;
                minimumBookingNotice?: true | undefined;
                beforeEventBuffer?: true | undefined;
                afterEventBuffer?: true | undefined;
                seatsPerTimeSlot?: true | undefined;
                onlyShowFirstAvailableSlot?: true | undefined;
                disableCancelling?: true | undefined;
                disableRescheduling?: true | undefined;
                seatsShowAttendees?: true | undefined;
                seatsShowAvailabilityCount?: true | undefined;
                schedulingType?: true | undefined;
                scheduleId?: true | undefined;
                allowReschedulingCancelledBookings?: true | undefined;
                price?: true | undefined;
                currency?: true | undefined;
                slotInterval?: true | undefined;
                successRedirectUrl?: true | undefined;
                forwardParamsSuccessRedirect?: true | undefined;
                durationLimits?: true | undefined;
                isInstantEvent?: true | undefined;
                instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                instantMeetingScheduleId?: true | undefined;
                instantMeetingParameters?: true | undefined;
                assignAllTeamMembers?: true | undefined;
                assignRRMembersUsingSegment?: true | undefined;
                rrSegmentQueryValue?: true | undefined;
                useEventTypeDestinationCalendarEmail?: true | undefined;
                isRRWeightsEnabled?: true | undefined;
                maxLeadThreshold?: true | undefined;
                includeNoShowInRRCalculation?: true | undefined;
                allowReschedulingPastBookings?: true | undefined;
                hideOrganizerEmail?: true | undefined;
                maxActiveBookingsPerBooker?: true | undefined;
                maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                customReplyToEmail?: true | undefined;
                eventTypeColor?: true | undefined;
                rescheduleWithSameRoundRobinHost?: true | undefined;
                secondaryEmailId?: true | undefined;
                useBookerTimezone?: true | undefined;
                restrictionScheduleId?: true | undefined;
                bookingRequiresAuthentication?: true | undefined;
                owner?: true | undefined;
                instantMeetingSchedule?: true | undefined;
                aiPhoneCallConfig?: true | undefined;
                fieldTranslations?: true | undefined;
                restrictionSchedule?: true | undefined;
                hostGroups?: true | undefined;
            } | undefined;
        } | undefined;
        requiresConfirmationThreshold?: {
            time: number;
            unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
        } | undefined;
        bookerLayouts?: {
            enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
            defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
        } | null | undefined;
        apps?: unknown;
    }, {
        config?: {
            useHostSchedulesForTeamEvent?: boolean | undefined;
        } | undefined;
        smartContractAddress?: string | undefined;
        blockchainId?: number | undefined;
        multipleDuration?: number[] | undefined;
        giphyThankYouPage?: string | undefined;
        additionalNotesRequired?: boolean | undefined;
        disableSuccessPage?: boolean | undefined;
        disableStandardEmails?: {
            all?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
            confirmation?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
        } | undefined;
        managedEventConfig?: {
            unlockedFields?: {
                users?: true | undefined;
                children?: true | undefined;
                length?: true | undefined;
                title?: true | undefined;
                metadata?: true | undefined;
                description?: true | undefined;
                userId?: true | undefined;
                calVideoSettings?: true | undefined;
                destinationCalendar?: true | undefined;
                profile?: true | undefined;
                team?: true | undefined;
                schedule?: true | undefined;
                availability?: true | undefined;
                hashedLink?: true | undefined;
                secondaryEmail?: true | undefined;
                customInputs?: true | undefined;
                timeZone?: true | undefined;
                bookings?: true | undefined;
                selectedCalendars?: true | undefined;
                webhooks?: true | undefined;
                workflows?: true | undefined;
                hosts?: true | undefined;
                slug?: true | undefined;
                parentId?: true | undefined;
                bookingLimits?: true | undefined;
                parent?: true | undefined;
                teamId?: true | undefined;
                hidden?: true | undefined;
                _count?: true | undefined;
                interfaceLanguage?: true | undefined;
                position?: true | undefined;
                locations?: true | undefined;
                offsetStart?: true | undefined;
                profileId?: true | undefined;
                useEventLevelSelectedCalendars?: true | undefined;
                eventName?: true | undefined;
                bookingFields?: true | undefined;
                periodType?: true | undefined;
                periodStartDate?: true | undefined;
                periodEndDate?: true | undefined;
                periodDays?: true | undefined;
                periodCountCalendarDays?: true | undefined;
                lockTimeZoneToggleOnBookingPage?: true | undefined;
                lockedTimeZone?: true | undefined;
                requiresConfirmation?: true | undefined;
                requiresConfirmationWillBlockSlot?: true | undefined;
                requiresConfirmationForFreeEmail?: true | undefined;
                requiresBookerEmailVerification?: true | undefined;
                canSendCalVideoTranscriptionEmails?: true | undefined;
                autoTranslateDescriptionEnabled?: true | undefined;
                recurringEvent?: true | undefined;
                disableGuests?: true | undefined;
                hideCalendarNotes?: true | undefined;
                hideCalendarEventDetails?: true | undefined;
                minimumBookingNotice?: true | undefined;
                beforeEventBuffer?: true | undefined;
                afterEventBuffer?: true | undefined;
                seatsPerTimeSlot?: true | undefined;
                onlyShowFirstAvailableSlot?: true | undefined;
                disableCancelling?: true | undefined;
                disableRescheduling?: true | undefined;
                seatsShowAttendees?: true | undefined;
                seatsShowAvailabilityCount?: true | undefined;
                schedulingType?: true | undefined;
                scheduleId?: true | undefined;
                allowReschedulingCancelledBookings?: true | undefined;
                price?: true | undefined;
                currency?: true | undefined;
                slotInterval?: true | undefined;
                successRedirectUrl?: true | undefined;
                forwardParamsSuccessRedirect?: true | undefined;
                durationLimits?: true | undefined;
                isInstantEvent?: true | undefined;
                instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                instantMeetingScheduleId?: true | undefined;
                instantMeetingParameters?: true | undefined;
                assignAllTeamMembers?: true | undefined;
                assignRRMembersUsingSegment?: true | undefined;
                rrSegmentQueryValue?: true | undefined;
                useEventTypeDestinationCalendarEmail?: true | undefined;
                isRRWeightsEnabled?: true | undefined;
                maxLeadThreshold?: true | undefined;
                includeNoShowInRRCalculation?: true | undefined;
                allowReschedulingPastBookings?: true | undefined;
                hideOrganizerEmail?: true | undefined;
                maxActiveBookingsPerBooker?: true | undefined;
                maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                customReplyToEmail?: true | undefined;
                eventTypeColor?: true | undefined;
                rescheduleWithSameRoundRobinHost?: true | undefined;
                secondaryEmailId?: true | undefined;
                useBookerTimezone?: true | undefined;
                restrictionScheduleId?: true | undefined;
                bookingRequiresAuthentication?: true | undefined;
                owner?: true | undefined;
                instantMeetingSchedule?: true | undefined;
                aiPhoneCallConfig?: true | undefined;
                fieldTranslations?: true | undefined;
                restrictionSchedule?: true | undefined;
                hostGroups?: true | undefined;
            } | undefined;
        } | undefined;
        requiresConfirmationThreshold?: {
            time: number;
            unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
        } | undefined;
        bookerLayouts?: {
            enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
            defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
        } | null | undefined;
        apps?: unknown;
    }>>>;
    description: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    calVideoSettings: z.ZodNullable<z.ZodOptional<z.ZodObject<{
        disableRecordingForGuests: z.ZodOptional<z.ZodNullable<z.ZodBoolean>>;
        disableRecordingForOrganizer: z.ZodOptional<z.ZodNullable<z.ZodBoolean>>;
        redirectUrlOnExit: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        enableAutomaticRecordingForOrganizer: z.ZodOptional<z.ZodNullable<z.ZodBoolean>>;
        enableAutomaticTranscription: z.ZodOptional<z.ZodNullable<z.ZodBoolean>>;
        disableTranscriptionForGuests: z.ZodOptional<z.ZodNullable<z.ZodBoolean>>;
        disableTranscriptionForOrganizer: z.ZodOptional<z.ZodNullable<z.ZodBoolean>>;
    }, "strip", z.ZodTypeAny, {
        disableRecordingForGuests?: boolean | null | undefined;
        disableRecordingForOrganizer?: boolean | null | undefined;
        redirectUrlOnExit?: string | null | undefined;
        enableAutomaticRecordingForOrganizer?: boolean | null | undefined;
        enableAutomaticTranscription?: boolean | null | undefined;
        disableTranscriptionForGuests?: boolean | null | undefined;
        disableTranscriptionForOrganizer?: boolean | null | undefined;
    }, {
        disableRecordingForGuests?: boolean | null | undefined;
        disableRecordingForOrganizer?: boolean | null | undefined;
        redirectUrlOnExit?: string | null | undefined;
        enableAutomaticRecordingForOrganizer?: boolean | null | undefined;
        enableAutomaticTranscription?: boolean | null | undefined;
        disableTranscriptionForGuests?: boolean | null | undefined;
        disableTranscriptionForOrganizer?: boolean | null | undefined;
    }>>>;
    slug: z.ZodEffects<z.ZodEffects<z.ZodString, string, string>, string, string>;
    teamId: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
    hidden: z.ZodOptional<z.ZodBoolean>;
    locations: z.ZodOptional<z.ZodArray<z.ZodObject<{
        type: z.ZodString;
        address: z.ZodOptional<z.ZodString>;
        link: z.ZodOptional<z.ZodString>;
        displayLocationPublicly: z.ZodOptional<z.ZodBoolean>;
        hostPhoneNumber: z.ZodOptional<z.ZodString>;
        credentialId: z.ZodOptional<z.ZodNumber>;
        teamName: z.ZodOptional<z.ZodString>;
        customLabel: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        type: string;
        address?: string | undefined;
        link?: string | undefined;
        displayLocationPublicly?: boolean | undefined;
        hostPhoneNumber?: string | undefined;
        credentialId?: number | undefined;
        teamName?: string | undefined;
        customLabel?: string | undefined;
    }, {
        type: string;
        address?: string | undefined;
        link?: string | undefined;
        displayLocationPublicly?: boolean | undefined;
        hostPhoneNumber?: string | undefined;
        credentialId?: number | undefined;
        teamName?: string | undefined;
        customLabel?: string | undefined;
    }>, "many">>;
    disableGuests: z.ZodOptional<z.ZodBoolean>;
    minimumBookingNotice: z.ZodOptional<z.ZodNumber>;
    beforeEventBuffer: z.ZodOptional<z.ZodNumber>;
    afterEventBuffer: z.ZodOptional<z.ZodNumber>;
    schedulingType: z.ZodOptional<z.ZodNullable<z.ZodNativeEnum<{
        readonly ROUND_ROBIN: "ROUND_ROBIN";
        readonly COLLECTIVE: "COLLECTIVE";
        readonly MANAGED: "MANAGED";
    }>>>;
    scheduleId: z.ZodOptional<z.ZodNumber>;
    slotInterval: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
}, "strip", z.ZodTypeAny, {
    length: number;
    title: string;
    slug: string;
    metadata?: {
        config?: {
            useHostSchedulesForTeamEvent?: boolean | undefined;
        } | undefined;
        smartContractAddress?: string | undefined;
        blockchainId?: number | undefined;
        multipleDuration?: number[] | undefined;
        giphyThankYouPage?: string | undefined;
        additionalNotesRequired?: boolean | undefined;
        disableSuccessPage?: boolean | undefined;
        disableStandardEmails?: {
            all?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
            confirmation?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
        } | undefined;
        managedEventConfig?: {
            unlockedFields?: {
                users?: true | undefined;
                children?: true | undefined;
                length?: true | undefined;
                title?: true | undefined;
                metadata?: true | undefined;
                description?: true | undefined;
                userId?: true | undefined;
                calVideoSettings?: true | undefined;
                destinationCalendar?: true | undefined;
                profile?: true | undefined;
                team?: true | undefined;
                schedule?: true | undefined;
                availability?: true | undefined;
                hashedLink?: true | undefined;
                secondaryEmail?: true | undefined;
                customInputs?: true | undefined;
                timeZone?: true | undefined;
                bookings?: true | undefined;
                selectedCalendars?: true | undefined;
                webhooks?: true | undefined;
                workflows?: true | undefined;
                hosts?: true | undefined;
                slug?: true | undefined;
                parentId?: true | undefined;
                bookingLimits?: true | undefined;
                parent?: true | undefined;
                teamId?: true | undefined;
                hidden?: true | undefined;
                _count?: true | undefined;
                interfaceLanguage?: true | undefined;
                position?: true | undefined;
                locations?: true | undefined;
                offsetStart?: true | undefined;
                profileId?: true | undefined;
                useEventLevelSelectedCalendars?: true | undefined;
                eventName?: true | undefined;
                bookingFields?: true | undefined;
                periodType?: true | undefined;
                periodStartDate?: true | undefined;
                periodEndDate?: true | undefined;
                periodDays?: true | undefined;
                periodCountCalendarDays?: true | undefined;
                lockTimeZoneToggleOnBookingPage?: true | undefined;
                lockedTimeZone?: true | undefined;
                requiresConfirmation?: true | undefined;
                requiresConfirmationWillBlockSlot?: true | undefined;
                requiresConfirmationForFreeEmail?: true | undefined;
                requiresBookerEmailVerification?: true | undefined;
                canSendCalVideoTranscriptionEmails?: true | undefined;
                autoTranslateDescriptionEnabled?: true | undefined;
                recurringEvent?: true | undefined;
                disableGuests?: true | undefined;
                hideCalendarNotes?: true | undefined;
                hideCalendarEventDetails?: true | undefined;
                minimumBookingNotice?: true | undefined;
                beforeEventBuffer?: true | undefined;
                afterEventBuffer?: true | undefined;
                seatsPerTimeSlot?: true | undefined;
                onlyShowFirstAvailableSlot?: true | undefined;
                disableCancelling?: true | undefined;
                disableRescheduling?: true | undefined;
                seatsShowAttendees?: true | undefined;
                seatsShowAvailabilityCount?: true | undefined;
                schedulingType?: true | undefined;
                scheduleId?: true | undefined;
                allowReschedulingCancelledBookings?: true | undefined;
                price?: true | undefined;
                currency?: true | undefined;
                slotInterval?: true | undefined;
                successRedirectUrl?: true | undefined;
                forwardParamsSuccessRedirect?: true | undefined;
                durationLimits?: true | undefined;
                isInstantEvent?: true | undefined;
                instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                instantMeetingScheduleId?: true | undefined;
                instantMeetingParameters?: true | undefined;
                assignAllTeamMembers?: true | undefined;
                assignRRMembersUsingSegment?: true | undefined;
                rrSegmentQueryValue?: true | undefined;
                useEventTypeDestinationCalendarEmail?: true | undefined;
                isRRWeightsEnabled?: true | undefined;
                maxLeadThreshold?: true | undefined;
                includeNoShowInRRCalculation?: true | undefined;
                allowReschedulingPastBookings?: true | undefined;
                hideOrganizerEmail?: true | undefined;
                maxActiveBookingsPerBooker?: true | undefined;
                maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                customReplyToEmail?: true | undefined;
                eventTypeColor?: true | undefined;
                rescheduleWithSameRoundRobinHost?: true | undefined;
                secondaryEmailId?: true | undefined;
                useBookerTimezone?: true | undefined;
                restrictionScheduleId?: true | undefined;
                bookingRequiresAuthentication?: true | undefined;
                owner?: true | undefined;
                instantMeetingSchedule?: true | undefined;
                aiPhoneCallConfig?: true | undefined;
                fieldTranslations?: true | undefined;
                restrictionSchedule?: true | undefined;
                hostGroups?: true | undefined;
            } | undefined;
        } | undefined;
        requiresConfirmationThreshold?: {
            time: number;
            unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
        } | undefined;
        bookerLayouts?: {
            enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
            defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
        } | null | undefined;
        apps?: unknown;
    } | null | undefined;
    description?: string | null | undefined;
    calVideoSettings?: {
        disableRecordingForGuests?: boolean | null | undefined;
        disableRecordingForOrganizer?: boolean | null | undefined;
        redirectUrlOnExit?: string | null | undefined;
        enableAutomaticRecordingForOrganizer?: boolean | null | undefined;
        enableAutomaticTranscription?: boolean | null | undefined;
        disableTranscriptionForGuests?: boolean | null | undefined;
        disableTranscriptionForOrganizer?: boolean | null | undefined;
    } | null | undefined;
    teamId?: number | null | undefined;
    hidden?: boolean | undefined;
    locations?: {
        type: string;
        address?: string | undefined;
        link?: string | undefined;
        displayLocationPublicly?: boolean | undefined;
        hostPhoneNumber?: string | undefined;
        credentialId?: number | undefined;
        teamName?: string | undefined;
        customLabel?: string | undefined;
    }[] | undefined;
    disableGuests?: boolean | undefined;
    minimumBookingNotice?: number | undefined;
    beforeEventBuffer?: number | undefined;
    afterEventBuffer?: number | undefined;
    schedulingType?: "ROUND_ROBIN" | "COLLECTIVE" | "MANAGED" | null | undefined;
    scheduleId?: number | undefined;
    slotInterval?: number | null | undefined;
}, {
    length: number;
    title: string;
    slug: string;
    metadata?: {
        config?: {
            useHostSchedulesForTeamEvent?: boolean | undefined;
        } | undefined;
        smartContractAddress?: string | undefined;
        blockchainId?: number | undefined;
        multipleDuration?: number[] | undefined;
        giphyThankYouPage?: string | undefined;
        additionalNotesRequired?: boolean | undefined;
        disableSuccessPage?: boolean | undefined;
        disableStandardEmails?: {
            all?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
            confirmation?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
        } | undefined;
        managedEventConfig?: {
            unlockedFields?: {
                users?: true | undefined;
                children?: true | undefined;
                length?: true | undefined;
                title?: true | undefined;
                metadata?: true | undefined;
                description?: true | undefined;
                userId?: true | undefined;
                calVideoSettings?: true | undefined;
                destinationCalendar?: true | undefined;
                profile?: true | undefined;
                team?: true | undefined;
                schedule?: true | undefined;
                availability?: true | undefined;
                hashedLink?: true | undefined;
                secondaryEmail?: true | undefined;
                customInputs?: true | undefined;
                timeZone?: true | undefined;
                bookings?: true | undefined;
                selectedCalendars?: true | undefined;
                webhooks?: true | undefined;
                workflows?: true | undefined;
                hosts?: true | undefined;
                slug?: true | undefined;
                parentId?: true | undefined;
                bookingLimits?: true | undefined;
                parent?: true | undefined;
                teamId?: true | undefined;
                hidden?: true | undefined;
                _count?: true | undefined;
                interfaceLanguage?: true | undefined;
                position?: true | undefined;
                locations?: true | undefined;
                offsetStart?: true | undefined;
                profileId?: true | undefined;
                useEventLevelSelectedCalendars?: true | undefined;
                eventName?: true | undefined;
                bookingFields?: true | undefined;
                periodType?: true | undefined;
                periodStartDate?: true | undefined;
                periodEndDate?: true | undefined;
                periodDays?: true | undefined;
                periodCountCalendarDays?: true | undefined;
                lockTimeZoneToggleOnBookingPage?: true | undefined;
                lockedTimeZone?: true | undefined;
                requiresConfirmation?: true | undefined;
                requiresConfirmationWillBlockSlot?: true | undefined;
                requiresConfirmationForFreeEmail?: true | undefined;
                requiresBookerEmailVerification?: true | undefined;
                canSendCalVideoTranscriptionEmails?: true | undefined;
                autoTranslateDescriptionEnabled?: true | undefined;
                recurringEvent?: true | undefined;
                disableGuests?: true | undefined;
                hideCalendarNotes?: true | undefined;
                hideCalendarEventDetails?: true | undefined;
                minimumBookingNotice?: true | undefined;
                beforeEventBuffer?: true | undefined;
                afterEventBuffer?: true | undefined;
                seatsPerTimeSlot?: true | undefined;
                onlyShowFirstAvailableSlot?: true | undefined;
                disableCancelling?: true | undefined;
                disableRescheduling?: true | undefined;
                seatsShowAttendees?: true | undefined;
                seatsShowAvailabilityCount?: true | undefined;
                schedulingType?: true | undefined;
                scheduleId?: true | undefined;
                allowReschedulingCancelledBookings?: true | undefined;
                price?: true | undefined;
                currency?: true | undefined;
                slotInterval?: true | undefined;
                successRedirectUrl?: true | undefined;
                forwardParamsSuccessRedirect?: true | undefined;
                durationLimits?: true | undefined;
                isInstantEvent?: true | undefined;
                instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                instantMeetingScheduleId?: true | undefined;
                instantMeetingParameters?: true | undefined;
                assignAllTeamMembers?: true | undefined;
                assignRRMembersUsingSegment?: true | undefined;
                rrSegmentQueryValue?: true | undefined;
                useEventTypeDestinationCalendarEmail?: true | undefined;
                isRRWeightsEnabled?: true | undefined;
                maxLeadThreshold?: true | undefined;
                includeNoShowInRRCalculation?: true | undefined;
                allowReschedulingPastBookings?: true | undefined;
                hideOrganizerEmail?: true | undefined;
                maxActiveBookingsPerBooker?: true | undefined;
                maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                customReplyToEmail?: true | undefined;
                eventTypeColor?: true | undefined;
                rescheduleWithSameRoundRobinHost?: true | undefined;
                secondaryEmailId?: true | undefined;
                useBookerTimezone?: true | undefined;
                restrictionScheduleId?: true | undefined;
                bookingRequiresAuthentication?: true | undefined;
                owner?: true | undefined;
                instantMeetingSchedule?: true | undefined;
                aiPhoneCallConfig?: true | undefined;
                fieldTranslations?: true | undefined;
                restrictionSchedule?: true | undefined;
                hostGroups?: true | undefined;
            } | undefined;
        } | undefined;
        requiresConfirmationThreshold?: {
            time: number;
            unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
        } | undefined;
        bookerLayouts?: {
            enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
            defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
        } | null | undefined;
        apps?: unknown;
    } | null | undefined;
    description?: string | null | undefined;
    calVideoSettings?: {
        disableRecordingForGuests?: boolean | null | undefined;
        disableRecordingForOrganizer?: boolean | null | undefined;
        redirectUrlOnExit?: string | null | undefined;
        enableAutomaticRecordingForOrganizer?: boolean | null | undefined;
        enableAutomaticTranscription?: boolean | null | undefined;
        disableTranscriptionForGuests?: boolean | null | undefined;
        disableTranscriptionForOrganizer?: boolean | null | undefined;
    } | null | undefined;
    teamId?: number | null | undefined;
    hidden?: boolean | undefined;
    locations?: {
        type: string;
        address?: string | undefined;
        link?: string | undefined;
        displayLocationPublicly?: boolean | undefined;
        hostPhoneNumber?: string | undefined;
        credentialId?: number | undefined;
        teamName?: string | undefined;
        customLabel?: string | undefined;
    }[] | undefined;
    disableGuests?: boolean | undefined;
    minimumBookingNotice?: number | undefined;
    beforeEventBuffer?: number | undefined;
    afterEventBuffer?: number | undefined;
    schedulingType?: "ROUND_ROBIN" | "COLLECTIVE" | "MANAGED" | null | undefined;
    scheduleId?: number | undefined;
    slotInterval?: number | null | undefined;
}>, {
    length: number;
    title: string;
    slug: string;
    metadata?: {
        config?: {
            useHostSchedulesForTeamEvent?: boolean | undefined;
        } | undefined;
        smartContractAddress?: string | undefined;
        blockchainId?: number | undefined;
        multipleDuration?: number[] | undefined;
        giphyThankYouPage?: string | undefined;
        additionalNotesRequired?: boolean | undefined;
        disableSuccessPage?: boolean | undefined;
        disableStandardEmails?: {
            all?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
            confirmation?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
        } | undefined;
        managedEventConfig?: {
            unlockedFields?: {
                users?: true | undefined;
                children?: true | undefined;
                length?: true | undefined;
                title?: true | undefined;
                metadata?: true | undefined;
                description?: true | undefined;
                userId?: true | undefined;
                calVideoSettings?: true | undefined;
                destinationCalendar?: true | undefined;
                profile?: true | undefined;
                team?: true | undefined;
                schedule?: true | undefined;
                availability?: true | undefined;
                hashedLink?: true | undefined;
                secondaryEmail?: true | undefined;
                customInputs?: true | undefined;
                timeZone?: true | undefined;
                bookings?: true | undefined;
                selectedCalendars?: true | undefined;
                webhooks?: true | undefined;
                workflows?: true | undefined;
                hosts?: true | undefined;
                slug?: true | undefined;
                parentId?: true | undefined;
                bookingLimits?: true | undefined;
                parent?: true | undefined;
                teamId?: true | undefined;
                hidden?: true | undefined;
                _count?: true | undefined;
                interfaceLanguage?: true | undefined;
                position?: true | undefined;
                locations?: true | undefined;
                offsetStart?: true | undefined;
                profileId?: true | undefined;
                useEventLevelSelectedCalendars?: true | undefined;
                eventName?: true | undefined;
                bookingFields?: true | undefined;
                periodType?: true | undefined;
                periodStartDate?: true | undefined;
                periodEndDate?: true | undefined;
                periodDays?: true | undefined;
                periodCountCalendarDays?: true | undefined;
                lockTimeZoneToggleOnBookingPage?: true | undefined;
                lockedTimeZone?: true | undefined;
                requiresConfirmation?: true | undefined;
                requiresConfirmationWillBlockSlot?: true | undefined;
                requiresConfirmationForFreeEmail?: true | undefined;
                requiresBookerEmailVerification?: true | undefined;
                canSendCalVideoTranscriptionEmails?: true | undefined;
                autoTranslateDescriptionEnabled?: true | undefined;
                recurringEvent?: true | undefined;
                disableGuests?: true | undefined;
                hideCalendarNotes?: true | undefined;
                hideCalendarEventDetails?: true | undefined;
                minimumBookingNotice?: true | undefined;
                beforeEventBuffer?: true | undefined;
                afterEventBuffer?: true | undefined;
                seatsPerTimeSlot?: true | undefined;
                onlyShowFirstAvailableSlot?: true | undefined;
                disableCancelling?: true | undefined;
                disableRescheduling?: true | undefined;
                seatsShowAttendees?: true | undefined;
                seatsShowAvailabilityCount?: true | undefined;
                schedulingType?: true | undefined;
                scheduleId?: true | undefined;
                allowReschedulingCancelledBookings?: true | undefined;
                price?: true | undefined;
                currency?: true | undefined;
                slotInterval?: true | undefined;
                successRedirectUrl?: true | undefined;
                forwardParamsSuccessRedirect?: true | undefined;
                durationLimits?: true | undefined;
                isInstantEvent?: true | undefined;
                instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                instantMeetingScheduleId?: true | undefined;
                instantMeetingParameters?: true | undefined;
                assignAllTeamMembers?: true | undefined;
                assignRRMembersUsingSegment?: true | undefined;
                rrSegmentQueryValue?: true | undefined;
                useEventTypeDestinationCalendarEmail?: true | undefined;
                isRRWeightsEnabled?: true | undefined;
                maxLeadThreshold?: true | undefined;
                includeNoShowInRRCalculation?: true | undefined;
                allowReschedulingPastBookings?: true | undefined;
                hideOrganizerEmail?: true | undefined;
                maxActiveBookingsPerBooker?: true | undefined;
                maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                customReplyToEmail?: true | undefined;
                eventTypeColor?: true | undefined;
                rescheduleWithSameRoundRobinHost?: true | undefined;
                secondaryEmailId?: true | undefined;
                useBookerTimezone?: true | undefined;
                restrictionScheduleId?: true | undefined;
                bookingRequiresAuthentication?: true | undefined;
                owner?: true | undefined;
                instantMeetingSchedule?: true | undefined;
                aiPhoneCallConfig?: true | undefined;
                fieldTranslations?: true | undefined;
                restrictionSchedule?: true | undefined;
                hostGroups?: true | undefined;
            } | undefined;
        } | undefined;
        requiresConfirmationThreshold?: {
            time: number;
            unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
        } | undefined;
        bookerLayouts?: {
            enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
            defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
        } | null | undefined;
        apps?: unknown;
    } | null | undefined;
    description?: string | null | undefined;
    calVideoSettings?: {
        disableRecordingForGuests?: boolean | null | undefined;
        disableRecordingForOrganizer?: boolean | null | undefined;
        redirectUrlOnExit?: string | null | undefined;
        enableAutomaticRecordingForOrganizer?: boolean | null | undefined;
        enableAutomaticTranscription?: boolean | null | undefined;
        disableTranscriptionForGuests?: boolean | null | undefined;
        disableTranscriptionForOrganizer?: boolean | null | undefined;
    } | null | undefined;
    teamId?: number | null | undefined;
    hidden?: boolean | undefined;
    locations?: {
        type: string;
        address?: string | undefined;
        link?: string | undefined;
        displayLocationPublicly?: boolean | undefined;
        hostPhoneNumber?: string | undefined;
        credentialId?: number | undefined;
        teamName?: string | undefined;
        customLabel?: string | undefined;
    }[] | undefined;
    disableGuests?: boolean | undefined;
    minimumBookingNotice?: number | undefined;
    beforeEventBuffer?: number | undefined;
    afterEventBuffer?: number | undefined;
    schedulingType?: "ROUND_ROBIN" | "COLLECTIVE" | "MANAGED" | null | undefined;
    scheduleId?: number | undefined;
    slotInterval?: number | null | undefined;
}, {
    length: number;
    title: string;
    slug: string;
    metadata?: {
        config?: {
            useHostSchedulesForTeamEvent?: boolean | undefined;
        } | undefined;
        smartContractAddress?: string | undefined;
        blockchainId?: number | undefined;
        multipleDuration?: number[] | undefined;
        giphyThankYouPage?: string | undefined;
        additionalNotesRequired?: boolean | undefined;
        disableSuccessPage?: boolean | undefined;
        disableStandardEmails?: {
            all?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
            confirmation?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
        } | undefined;
        managedEventConfig?: {
            unlockedFields?: {
                users?: true | undefined;
                children?: true | undefined;
                length?: true | undefined;
                title?: true | undefined;
                metadata?: true | undefined;
                description?: true | undefined;
                userId?: true | undefined;
                calVideoSettings?: true | undefined;
                destinationCalendar?: true | undefined;
                profile?: true | undefined;
                team?: true | undefined;
                schedule?: true | undefined;
                availability?: true | undefined;
                hashedLink?: true | undefined;
                secondaryEmail?: true | undefined;
                customInputs?: true | undefined;
                timeZone?: true | undefined;
                bookings?: true | undefined;
                selectedCalendars?: true | undefined;
                webhooks?: true | undefined;
                workflows?: true | undefined;
                hosts?: true | undefined;
                slug?: true | undefined;
                parentId?: true | undefined;
                bookingLimits?: true | undefined;
                parent?: true | undefined;
                teamId?: true | undefined;
                hidden?: true | undefined;
                _count?: true | undefined;
                interfaceLanguage?: true | undefined;
                position?: true | undefined;
                locations?: true | undefined;
                offsetStart?: true | undefined;
                profileId?: true | undefined;
                useEventLevelSelectedCalendars?: true | undefined;
                eventName?: true | undefined;
                bookingFields?: true | undefined;
                periodType?: true | undefined;
                periodStartDate?: true | undefined;
                periodEndDate?: true | undefined;
                periodDays?: true | undefined;
                periodCountCalendarDays?: true | undefined;
                lockTimeZoneToggleOnBookingPage?: true | undefined;
                lockedTimeZone?: true | undefined;
                requiresConfirmation?: true | undefined;
                requiresConfirmationWillBlockSlot?: true | undefined;
                requiresConfirmationForFreeEmail?: true | undefined;
                requiresBookerEmailVerification?: true | undefined;
                canSendCalVideoTranscriptionEmails?: true | undefined;
                autoTranslateDescriptionEnabled?: true | undefined;
                recurringEvent?: true | undefined;
                disableGuests?: true | undefined;
                hideCalendarNotes?: true | undefined;
                hideCalendarEventDetails?: true | undefined;
                minimumBookingNotice?: true | undefined;
                beforeEventBuffer?: true | undefined;
                afterEventBuffer?: true | undefined;
                seatsPerTimeSlot?: true | undefined;
                onlyShowFirstAvailableSlot?: true | undefined;
                disableCancelling?: true | undefined;
                disableRescheduling?: true | undefined;
                seatsShowAttendees?: true | undefined;
                seatsShowAvailabilityCount?: true | undefined;
                schedulingType?: true | undefined;
                scheduleId?: true | undefined;
                allowReschedulingCancelledBookings?: true | undefined;
                price?: true | undefined;
                currency?: true | undefined;
                slotInterval?: true | undefined;
                successRedirectUrl?: true | undefined;
                forwardParamsSuccessRedirect?: true | undefined;
                durationLimits?: true | undefined;
                isInstantEvent?: true | undefined;
                instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                instantMeetingScheduleId?: true | undefined;
                instantMeetingParameters?: true | undefined;
                assignAllTeamMembers?: true | undefined;
                assignRRMembersUsingSegment?: true | undefined;
                rrSegmentQueryValue?: true | undefined;
                useEventTypeDestinationCalendarEmail?: true | undefined;
                isRRWeightsEnabled?: true | undefined;
                maxLeadThreshold?: true | undefined;
                includeNoShowInRRCalculation?: true | undefined;
                allowReschedulingPastBookings?: true | undefined;
                hideOrganizerEmail?: true | undefined;
                maxActiveBookingsPerBooker?: true | undefined;
                maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                customReplyToEmail?: true | undefined;
                eventTypeColor?: true | undefined;
                rescheduleWithSameRoundRobinHost?: true | undefined;
                secondaryEmailId?: true | undefined;
                useBookerTimezone?: true | undefined;
                restrictionScheduleId?: true | undefined;
                bookingRequiresAuthentication?: true | undefined;
                owner?: true | undefined;
                instantMeetingSchedule?: true | undefined;
                aiPhoneCallConfig?: true | undefined;
                fieldTranslations?: true | undefined;
                restrictionSchedule?: true | undefined;
                hostGroups?: true | undefined;
            } | undefined;
        } | undefined;
        requiresConfirmationThreshold?: {
            time: number;
            unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
        } | undefined;
        bookerLayouts?: {
            enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
            defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
        } | null | undefined;
        apps?: unknown;
    } | null | undefined;
    description?: string | null | undefined;
    calVideoSettings?: {
        disableRecordingForGuests?: boolean | null | undefined;
        disableRecordingForOrganizer?: boolean | null | undefined;
        redirectUrlOnExit?: string | null | undefined;
        enableAutomaticRecordingForOrganizer?: boolean | null | undefined;
        enableAutomaticTranscription?: boolean | null | undefined;
        disableTranscriptionForGuests?: boolean | null | undefined;
        disableTranscriptionForOrganizer?: boolean | null | undefined;
    } | null | undefined;
    teamId?: number | null | undefined;
    hidden?: boolean | undefined;
    locations?: {
        type: string;
        address?: string | undefined;
        link?: string | undefined;
        displayLocationPublicly?: boolean | undefined;
        hostPhoneNumber?: string | undefined;
        credentialId?: number | undefined;
        teamName?: string | undefined;
        customLabel?: string | undefined;
    }[] | undefined;
    disableGuests?: boolean | undefined;
    minimumBookingNotice?: number | undefined;
    beforeEventBuffer?: number | undefined;
    afterEventBuffer?: number | undefined;
    schedulingType?: "ROUND_ROBIN" | "COLLECTIVE" | "MANAGED" | null | undefined;
    scheduleId?: number | undefined;
    slotInterval?: number | null | undefined;
}>;
export type TCreateInputSchema = z.infer<typeof ZCreateInputSchema>;
