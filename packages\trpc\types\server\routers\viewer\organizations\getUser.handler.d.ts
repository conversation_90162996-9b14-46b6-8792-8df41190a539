import type { TrpcSessionUser } from "../../../types";
import type { TGetUserInput } from "./getUser.schema";
type AdminVerifyOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TGetUserInput;
};
export declare function getUserHandler({ input, ctx }: AdminVerifyOptions): Promise<{
    username: string | null;
    teams: {
        accepted: boolean;
        name: string;
        id: number;
    }[];
    role: import(".prisma/client").$Enums.MembershipRole;
    name: string | null;
    id: number;
    email: string;
    bio: string | null;
    avatarUrl: string | null;
    timeZone: string;
    schedules: {
        name: string;
        id: number;
    }[];
    profiles: {
        username: string;
    }[];
}>;
export default getUserHandler;
