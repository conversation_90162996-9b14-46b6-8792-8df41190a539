export declare const delegationCredentialRouter: import("@trpc/server/unstable-core-do-not-import").CreateRouterInner<import("@trpc/server/unstable-core-do-not-import").RootConfig<{
    ctx: import("../../../createContext").InnerContext;
    meta: object;
    errorShape: {
        message: string;
        code: number;
        data: {
            code: string;
            httpStatus: number;
            path?: string;
            [key: string]: unknown;
        };
    };
    transformer: {
        stringify: (object: any) => string;
        parse: <T = unknown>(string: string) => T;
        serialize: (object: any) => import("superjson/dist/types").SuperJSONResult;
        deserialize: <T_1 = unknown>(payload: import("superjson/dist/types").SuperJSONResult) => T_1;
        registerClass: (v: import("superjson/dist/types").Class, options?: string | import("superjson/dist/class-registry").RegisterOptions | undefined) => void;
        registerSymbol: (v: Symbol, identifier?: string | undefined) => void;
        registerCustom: <I, O extends import("superjson/dist/types").JSONValue>(transformer: Omit<import("superjson/dist/custom-transformer-registry").CustomTransfomer<I, O>, "name">, name: string) => void;
        allowErrorProps: (...props: string[]) => void;
    };
}>, {
    check: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: any;
    }>;
    list: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: ((Omit<{
            serviceAccountClientId: string | null;
            id: string;
            workspacePlatform: {
                name: string;
                id: number;
                slug: string;
            };
            createdAt: Date;
            updatedAt: Date;
            organizationId: number;
            enabled: boolean;
            domain: string;
            serviceAccountKey: import(".prisma/client").Prisma.JsonValue;
            lastEnabledAt: Date | null;
            lastDisabledAt: Date | null;
        }, "serviceAccountKey"> & {
            serviceAccountKey: undefined;
        }) | null)[];
    }>;
    update: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            id: string;
            domain: string;
            workspacePlatformSlug: string;
        };
        output: (Omit<{
            id: string;
            workspacePlatform: {
                name: string;
                slug: string;
            };
            createdAt: Date;
            updatedAt: Date;
            organizationId: number;
            enabled: boolean;
            domain: string;
            lastEnabledAt: Date | null;
            lastDisabledAt: Date | null;
        }, "serviceAccountKey"> & {
            serviceAccountKey: undefined;
        }) | null | undefined;
    }>;
    add: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            domain: string;
            serviceAccountKey: {
                client_id: string;
                private_key: string;
                client_email?: string | undefined;
                tenant_id?: string | undefined;
            } & {
                [k: string]: unknown;
            };
            workspacePlatformSlug: string;
        };
        output: (Omit<{
            id: string;
            createdAt: Date;
            updatedAt: Date;
            organizationId: number;
            enabled: boolean;
            domain: string;
            workspacePlatformId: number;
            serviceAccountKey: import(".prisma/client").Prisma.JsonValue;
            lastEnabledAt: Date | null;
            lastDisabledAt: Date | null;
        }, "serviceAccountKey"> & {
            serviceAccountKey: undefined;
        }) | null | undefined;
    }>;
    toggleEnabled: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            id: string;
            enabled: boolean;
        };
        output: {
            id: string;
            workspacePlatform: {
                name: string;
                slug: string;
            };
            createdAt: Date;
            updatedAt: Date;
            organizationId: number;
            enabled: boolean;
            domain: string;
            lastEnabledAt: Date | null;
            lastDisabledAt: Date | null;
        } | null;
    }>;
    getAffectedMembersForDisable: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            id: string;
        };
        output: {
            email: string;
            name: string | null;
            id: number;
        }[];
    }>;
    delete: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            id: string;
        };
        output: {
            id: string;
        };
    }>;
    listWorkspacePlatforms: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: void;
        output: {
            name: string;
            id: number;
            description: string;
            slug: string;
            enabled: boolean;
        }[];
    }>;
}>;
