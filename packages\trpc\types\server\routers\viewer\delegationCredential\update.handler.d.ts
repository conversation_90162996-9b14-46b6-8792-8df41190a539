import type { z } from "zod";
import type { DelegationCredentialUpdateSchema } from "./schema";
export default function handler({ input, ctx, }: {
    input: z.infer<typeof DelegationCredentialUpdateSchema>;
    ctx: {
        user: {
            id: number;
            organizationId: number | null;
        };
    };
}): Promise<(Omit<{
    id: string;
    workspacePlatform: {
        name: string;
        slug: string;
    };
    createdAt: Date;
    updatedAt: Date;
    organizationId: number;
    enabled: boolean;
    domain: string;
    lastEnabledAt: Date | null;
    lastDisabledAt: Date | null;
}, "serviceAccountKey"> & {
    serviceAccountKey: undefined;
}) | null | undefined>;
