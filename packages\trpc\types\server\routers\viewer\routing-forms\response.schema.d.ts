import { z } from "zod";
export declare const ZResponseInputSchema: z.ZodObject<{
    formId: z.ZodString;
    formFillerId: z.ZodString;
    response: z.<PERSON>od<PERSON><PERSON>ord<z.ZodString, z.ZodObject<{
        label: z.ZodString;
        identifier: z.<PERSON>ptional<z.ZodString>;
        value: z.<PERSON><[z.ZodString, z.Zod<PERSON>, z.Zod<PERSON><z.ZodString, "many">]>;
    }, "strip", z.ZodTypeAny, {
        label: string;
        value: (string | number | string[]) & (string | number | string[] | undefined);
        identifier?: string | undefined;
    }, {
        label: string;
        value: (string | number | string[]) & (string | number | string[] | undefined);
        identifier?: string | undefined;
    }>>;
    chosenRouteId: z.ZodOptional<z.ZodString>;
    isPreview: z.ZodOptional<z.ZodBoolean>;
}, "strip", z.<PERSON><PERSON>ny, {
    response: Record<string, {
        label: string;
        value: (string | number | string[]) & (string | number | string[] | undefined);
        identifier?: string | undefined;
    }>;
    formFillerId: string;
    formId: string;
    chosenRouteId?: string | undefined;
    isPreview?: boolean | undefined;
}, {
    response: Record<string, {
        label: string;
        value: (string | number | string[]) & (string | number | string[] | undefined);
        identifier?: string | undefined;
    }>;
    formFillerId: string;
    formId: string;
    chosenRouteId?: string | undefined;
    isPreview?: boolean | undefined;
}>;
export type TResponseInputSchema = z.infer<typeof ZResponseInputSchema>;
