import { z } from "zod";
export declare const ZInviteMemberByTokenSchemaInputSchema: z.ZodObject<{
    token: z.ZodString;
    creationSource: z.ZodNativeEnum<{
        readonly API_V1: "API_V1";
        readonly API_V2: "API_V2";
        readonly WEBAPP: "WEBAPP";
    }>;
}, "strip", z.Z<PERSON>, {
    creationSource: "API_V1" | "API_V2" | "WEBAPP";
    token: string;
}, {
    creationSource: "API_V1" | "API_V2" | "WEBAPP";
    token: string;
}>;
export type TInviteMemberByTokenSchemaInputSchema = z.infer<typeof ZInviteMemberByTokenSchemaInputSchema>;
