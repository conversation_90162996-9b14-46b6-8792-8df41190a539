import type { TrpcSessionUser } from "../../../types";
type GetOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
};
declare const getSMSLockStateTeamsUsers: ({ ctx }: GetOptions) => Promise<{
    users: {
        locked: {
            name: string | null;
            id: number;
            email: string;
            username: string | null;
            avatarUrl: string | null;
            smsLockState: import(".prisma/client").$Enums.SMSLockState;
        }[];
        reviewNeeded: {
            name: string | null;
            id: number;
            email: string;
            username: string | null;
            avatarUrl: string | null;
            smsLockState: import(".prisma/client").$Enums.SMSLockState;
        }[];
    };
    teams: {
        locked: {
            name: string;
            id: number;
            smsLockState: import(".prisma/client").$Enums.SMSLockState;
            slug: string | null;
            logoUrl: string | null;
        }[];
        reviewNeeded: {
            name: string;
            id: number;
            smsLockState: import(".prisma/client").$Enums.SMSLockState;
            slug: string | null;
            logoUrl: string | null;
        }[];
    };
}>;
export default getSMSLockStateTeamsUsers;
