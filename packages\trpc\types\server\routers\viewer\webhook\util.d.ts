export declare const webhookProcedure: import("@trpc/server/unstable-core-do-not-import").ProcedureBuilder<import("../../../createContext").InnerContext, object, {
    user: {
        avatar: string;
        organization: {
            id: number | null;
            isOrgAdmin: boolean;
            metadata: {
                defaultConferencingApp?: {
                    appSlug?: string | undefined;
                    appLink?: string | undefined;
                } | undefined;
                requestedSlug?: string | null | undefined;
                paymentId?: string | undefined;
                subscriptionId?: string | null | undefined;
                subscriptionItemId?: string | null | undefined;
                orgSeats?: number | null | undefined;
                orgPricePerSeat?: number | null | undefined;
                migratedToOrgFrom?: {
                    teamSlug?: string | null | undefined;
                    lastMigrationTime?: string | undefined;
                    reverted?: boolean | undefined;
                    lastRevertTime?: string | undefined;
                } | undefined;
                billingPeriod?: import("@calcom/prisma/zod-utils").BillingPeriod | undefined;
            } | null;
            requestedSlug: string | null;
            name?: string | undefined;
            organizationSettings?: {
                allowSEOIndexing: boolean;
                lockEventTypeCreationForUsers: boolean;
            } | null | undefined;
            hideBranding?: boolean | undefined;
            slug?: string | null | undefined;
            logoUrl?: string | null | undefined;
            isPrivate?: boolean | undefined;
            bannerUrl?: string | null | undefined;
            isPlatform?: boolean | undefined;
        };
        organizationId: number | null;
        id: number;
        email: string;
        username: string | null;
        locale: string;
        defaultBookerLayouts: {
            enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
            defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
        } | null;
        name: string | null;
        role: import(".prisma/client").$Enums.UserPermissionRole;
        metadata: import(".prisma/client").Prisma.JsonValue;
        startTime: number;
        endTime: number;
        destinationCalendar: {
            id: number;
            userId: number | null;
            eventTypeId: number | null;
            createdAt: Date | null;
            updatedAt: Date | null;
            integration: string;
            externalId: string;
            primaryEmail: string | null;
            credentialId: number | null;
            delegationCredentialId: string | null;
            domainWideDelegationCredentialId: string | null;
        } | null;
        movedToProfileId: number | null;
        emailVerified: Date | null;
        bio: string | null;
        avatarUrl: string | null;
        timeZone: string;
        weekStart: string;
        bufferTime: number;
        hideBranding: boolean;
        theme: string | null;
        appTheme: string | null;
        createdDate: Date;
        trialEndsAt: Date | null;
        defaultScheduleId: number | null;
        completedOnboarding: boolean;
        timeFormat: number | null;
        twoFactorEnabled: boolean;
        identityProvider: import(".prisma/client").$Enums.IdentityProvider;
        identityProviderId: string | null;
        brandColor: string | null;
        darkBrandColor: string | null;
        allowDynamicBooking: boolean | null;
        allowSEOIndexing: boolean | null;
        receiveMonthlyDigestEmail: boolean | null;
        disableImpersonation: boolean;
        profiles: {
            id: number;
            userId: number;
            uid: string;
            createdAt: Date;
            updatedAt: Date;
            username: string;
            organizationId: number;
        }[];
        allSelectedCalendars: {
            eventTypeId: number | null;
            updatedAt: Date | null;
            integration: string;
            externalId: string;
            googleChannelId: string | null;
        }[];
        userLevelSelectedCalendars: {
            eventTypeId: number | null;
            updatedAt: Date | null;
            integration: string;
            externalId: string;
            googleChannelId: string | null;
        }[];
        profile: import("@calcom/types/UserProfile").UserAsPersonalProfile;
    } | {
        avatar: string;
        organization: {
            id: number | null;
            isOrgAdmin: boolean;
            metadata: {
                defaultConferencingApp?: {
                    appSlug?: string | undefined;
                    appLink?: string | undefined;
                } | undefined;
                requestedSlug?: string | null | undefined;
                paymentId?: string | undefined;
                subscriptionId?: string | null | undefined;
                subscriptionItemId?: string | null | undefined;
                orgSeats?: number | null | undefined;
                orgPricePerSeat?: number | null | undefined;
                migratedToOrgFrom?: {
                    teamSlug?: string | null | undefined;
                    lastMigrationTime?: string | undefined;
                    reverted?: boolean | undefined;
                    lastRevertTime?: string | undefined;
                } | undefined;
                billingPeriod?: import("@calcom/prisma/zod-utils").BillingPeriod | undefined;
            } | null;
            requestedSlug: string | null;
            name?: string | undefined;
            organizationSettings?: {
                allowSEOIndexing: boolean;
                lockEventTypeCreationForUsers: boolean;
            } | null | undefined;
            hideBranding?: boolean | undefined;
            slug?: string | null | undefined;
            logoUrl?: string | null | undefined;
            isPrivate?: boolean | undefined;
            bannerUrl?: string | null | undefined;
            isPlatform?: boolean | undefined;
        };
        organizationId: number | null;
        id: number;
        email: string;
        username: string | null;
        locale: string;
        defaultBookerLayouts: {
            enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
            defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
        } | null;
        name: string | null;
        role: import(".prisma/client").$Enums.UserPermissionRole;
        metadata: import(".prisma/client").Prisma.JsonValue;
        startTime: number;
        endTime: number;
        destinationCalendar: {
            id: number;
            userId: number | null;
            eventTypeId: number | null;
            createdAt: Date | null;
            updatedAt: Date | null;
            integration: string;
            externalId: string;
            primaryEmail: string | null;
            credentialId: number | null;
            delegationCredentialId: string | null;
            domainWideDelegationCredentialId: string | null;
        } | null;
        movedToProfileId: number | null;
        emailVerified: Date | null;
        bio: string | null;
        avatarUrl: string | null;
        timeZone: string;
        weekStart: string;
        bufferTime: number;
        hideBranding: boolean;
        theme: string | null;
        appTheme: string | null;
        createdDate: Date;
        trialEndsAt: Date | null;
        defaultScheduleId: number | null;
        completedOnboarding: boolean;
        timeFormat: number | null;
        twoFactorEnabled: boolean;
        identityProvider: import(".prisma/client").$Enums.IdentityProvider;
        identityProviderId: string | null;
        brandColor: string | null;
        darkBrandColor: string | null;
        allowDynamicBooking: boolean | null;
        allowSEOIndexing: boolean | null;
        receiveMonthlyDigestEmail: boolean | null;
        disableImpersonation: boolean;
        profiles: {
            id: number;
            userId: number;
            uid: string;
            createdAt: Date;
            updatedAt: Date;
            username: string;
            organizationId: number;
        }[];
        allSelectedCalendars: {
            eventTypeId: number | null;
            updatedAt: Date | null;
            integration: string;
            externalId: string;
            googleChannelId: string | null;
        }[];
        userLevelSelectedCalendars: {
            eventTypeId: number | null;
            updatedAt: Date | null;
            integration: string;
            externalId: string;
            googleChannelId: string | null;
        }[];
        profile: {
            name: string | null;
            avatarUrl: string | null;
            startTime: number;
            endTime: number;
            bufferTime: number;
            username: string | null;
            upId: string;
            id: null;
            organizationId: null;
            organization: null;
        } | {
            name: string | null;
            avatarUrl: string | null;
            startTime: number;
            endTime: number;
            bufferTime: number;
            user: {
                name: string | null;
                id: number;
                locale: string | null;
                startTime: number;
                endTime: number;
                email: string;
                username: string | null;
                avatarUrl: string | null;
                bufferTime: number;
                defaultScheduleId: number | null;
                isPlatformManaged: boolean;
            };
            organization: {
                name: string;
                id: number;
                metadata: import(".prisma/client").Prisma.JsonValue;
                organizationSettings: {
                    allowSEOIndexing: boolean;
                    lockEventTypeCreationForUsers: boolean;
                } | null;
                hideBranding: boolean;
                slug: string | null;
                logoUrl: string | null;
                isPrivate: boolean;
                bannerUrl: string | null;
                isPlatform: boolean;
                members: {
                    id: number;
                    role: import(".prisma/client").$Enums.MembershipRole;
                    userId: number;
                    disableImpersonation: boolean;
                    teamId: number;
                    accepted: boolean;
                }[];
            } & Omit<Pick<{
                id: number;
                name: string;
                slug: string | null;
                logoUrl: string | null;
                calVideoLogo: string | null;
                appLogo: string | null;
                appIconLogo: string | null;
                bio: string | null;
                hideBranding: boolean;
                hideTeamProfileLink: boolean;
                isPrivate: boolean;
                hideBookATeamMember: boolean;
                createdAt: Date;
                metadata: import(".prisma/client").Prisma.JsonValue | null;
                theme: string | null;
                rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
                rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
                brandColor: string | null;
                darkBrandColor: string | null;
                bannerUrl: string | null;
                parentId: number | null;
                timeFormat: number | null;
                timeZone: string;
                weekStart: string;
                isOrganization: boolean;
                pendingPayment: boolean;
                isPlatform: boolean;
                createdByOAuthClientId: string | null;
                smsLockState: import(".prisma/client").$Enums.SMSLockState;
                smsLockReviewedByAdmin: boolean;
                bookingLimits: import(".prisma/client").Prisma.JsonValue | null;
                includeManagedEventsInLimits: boolean;
            }, "name" | "id" | "metadata" | "hideBranding" | "slug" | "logoUrl" | "bannerUrl" | "isPlatform">, "metadata"> & {
                requestedSlug: string | null;
                metadata: {
                    requestedSlug: string | null;
                    defaultConferencingApp?: {
                        appSlug?: string | undefined;
                        appLink?: string | undefined;
                    } | undefined;
                    paymentId?: string | undefined;
                    subscriptionId?: string | null | undefined;
                    subscriptionItemId?: string | null | undefined;
                    orgSeats?: number | null | undefined;
                    orgPricePerSeat?: number | null | undefined;
                    migratedToOrgFrom?: {
                        teamSlug?: string | null | undefined;
                        lastMigrationTime?: string | undefined;
                        reverted?: boolean | undefined;
                        lastRevertTime?: string | undefined;
                    } | undefined;
                    billingPeriod?: import("@calcom/prisma/zod-utils").BillingPeriod | undefined;
                };
            };
            movedFromUser: {
                id: number;
            } | null;
            id: number;
            userId: number;
            uid: string;
            createdAt: Date & string;
            updatedAt: Date & string;
            username: string;
            organizationId: number;
            upId: string;
        };
    };
    session: {
        upId: string;
        hasValidLicense: boolean;
        profileId?: number | null;
        user: import("next-auth").User;
        expires: import("next-auth").ISODateString;
    };
}, {
    id?: string | undefined;
    eventTypeId?: number | undefined;
    teamId?: number | undefined;
} | undefined, {
    id?: string | undefined;
    eventTypeId?: number | undefined;
    teamId?: number | undefined;
} | undefined, typeof import("@trpc/server/unstable-core-do-not-import").unsetMarker, typeof import("@trpc/server/unstable-core-do-not-import").unsetMarker>;
