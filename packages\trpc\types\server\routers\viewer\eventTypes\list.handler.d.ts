import type { TrpcSessionUser } from "../../../types";
type ListOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
};
export declare const listHandler: ({ ctx }: ListOptions) => Promise<{
    id: number;
    length: number;
    title: string;
    metadata: import(".prisma/client").Prisma.JsonValue;
    description: string | null;
    slug: string;
    hidden: boolean;
    schedulingType: import(".prisma/client").$Enums.SchedulingType | null;
}[]>;
export {};
