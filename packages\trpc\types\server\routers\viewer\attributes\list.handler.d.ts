import type { TrpcSessionUser } from "../../../types";
type GetOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
};
declare const listHandler: (opts: GetOptions) => Promise<({
    options: {
        id: string;
        contains: string[];
        value: string;
        slug: string;
        attributeId: string;
        isGroup: boolean;
    }[];
} & {
    name: string;
    id: string;
    type: import(".prisma/client").$Enums.AttributeType;
    createdAt: Date;
    updatedAt: Date;
    slug: string;
    teamId: number;
    enabled: boolean;
    usersCanEditRelation: boolean;
    isWeightsEnabled: boolean;
    isLocked: boolean;
})[]>;
export default listHandler;
