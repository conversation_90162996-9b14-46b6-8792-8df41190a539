import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TUpdateMembershipInputSchema } from "./updateMembership.schema";
type UpdateMembershipOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TUpdateMembershipInputSchema;
};
export declare const updateMembershipHandler: ({ ctx, input }: UpdateMembershipOptions) => Promise<{
    id: number;
    role: import(".prisma/client").$Enums.MembershipRole;
    userId: number;
    createdAt: Date | null;
    updatedAt: Date | null;
    disableImpersonation: boolean;
    teamId: number;
    accepted: boolean;
    customRoleId: string | null;
}>;
export default updateMembershipHandler;
