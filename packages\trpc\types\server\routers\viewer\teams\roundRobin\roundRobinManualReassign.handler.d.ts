import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TRoundRobinManualReassignInputSchema } from "./roundRobinManualReassign.schema";
type RoundRobinManualReassignOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TRoundRobinManualReassignInputSchema;
};
export declare const roundRobinManualReassignHandler: ({ ctx, input }: RoundRobinManualReassignOptions) => Promise<{
    success: boolean;
}>;
export default roundRobinManualReassignHandler;
