import { z } from "zod";
export declare const ZFindTeamMembersMatchingAttributeLogicOfRouteInputSchema: z.ZodObject<{
    formId: z.ZodString;
    response: z.ZodRecord<z.ZodString, z.ZodObject<{
        label: z.ZodString;
        value: z.Z<PERSON><[z.ZodString, z.<PERSON>odNum<PERSON>, z.<PERSON><z.ZodString, "many">]>;
    }, "strip", z.ZodType<PERSON>ny, {
        label: string;
        value: (string | number | string[]) & (string | number | string[] | undefined);
    }, {
        label: string;
        value: (string | number | string[]) & (string | number | string[] | undefined);
    }>>;
    route: z.ZodObject<{
        id: z.ZodString;
        name: z.ZodOptional<z.ZodString>;
        attributeIdForWeights: z.ZodOptional<z.ZodString>;
        attributeRoutingConfig: z.ZodOptional<z.ZodNullable<z.ZodObject<{
            skipContactOwner: z.Zod<PERSON>ptional<z.ZodBoolean>;
            salesforce: z.ZodOptional<z.ZodObject<{
                rrSkipToAccountLookupField: z.ZodOptional<z.ZodBoolean>;
                rrSKipToAccountLookupFieldName: z.ZodOptional<z.ZodString>;
            }, "strip", z.ZodTypeAny, {
                rrSkipToAccountLookupField?: boolean | undefined;
                rrSKipToAccountLookupFieldName?: string | undefined;
            }, {
                rrSkipToAccountLookupField?: boolean | undefined;
                rrSKipToAccountLookupFieldName?: string | undefined;
            }>>;
        }, "strip", z.ZodTypeAny, {
            skipContactOwner?: boolean | undefined;
            salesforce?: {
                rrSkipToAccountLookupField?: boolean | undefined;
                rrSKipToAccountLookupFieldName?: string | undefined;
            } | undefined;
        }, {
            skipContactOwner?: boolean | undefined;
            salesforce?: {
                rrSkipToAccountLookupField?: boolean | undefined;
                rrSKipToAccountLookupFieldName?: string | undefined;
            } | undefined;
        }>>>;
        queryValue: z.ZodBranded<z.ZodUnion<[z.ZodObject<{
            id: z.ZodOptional<z.ZodString>;
            type: z.ZodLiteral<"group">;
            children1: z.ZodOptional<z.ZodEffects<z.ZodRecord<z.ZodString, z.ZodObject<{
                type: z.ZodOptional<z.ZodString>;
                properties: z.ZodOptional<z.ZodObject<{
                    field: z.ZodOptional<z.ZodAny>;
                    operator: z.ZodOptional<z.ZodAny>;
                    value: z.ZodOptional<z.ZodAny>;
                    valueSrc: z.ZodOptional<z.ZodAny>;
                    valueError: z.ZodOptional<z.ZodArray<z.ZodUnion<[z.ZodString, z.ZodNull]>, "many">>;
                    valueType: z.ZodOptional<z.ZodAny>;
                }, "strip", z.ZodTypeAny, {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                }, {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                }>>;
            }, "strip", z.ZodTypeAny, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }>>, Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }>, Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }>>>;
            properties: z.ZodAny;
        }, "strip", z.ZodTypeAny, {
            type: "group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        }, {
            type: "group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        }>, z.ZodObject<{
            id: z.ZodOptional<z.ZodString>;
            type: z.ZodLiteral<"switch_group">;
            children1: z.ZodOptional<z.ZodEffects<z.ZodRecord<z.ZodString, z.ZodObject<{
                type: z.ZodOptional<z.ZodString>;
                properties: z.ZodOptional<z.ZodObject<{
                    field: z.ZodOptional<z.ZodAny>;
                    operator: z.ZodOptional<z.ZodAny>;
                    value: z.ZodOptional<z.ZodAny>;
                    valueSrc: z.ZodOptional<z.ZodAny>;
                    valueError: z.ZodOptional<z.ZodArray<z.ZodUnion<[z.ZodString, z.ZodNull]>, "many">>;
                    valueType: z.ZodOptional<z.ZodAny>;
                }, "strip", z.ZodTypeAny, {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                }, {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                }>>;
            }, "strip", z.ZodTypeAny, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }>>, Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }>, Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }>>>;
            properties: z.ZodAny;
        }, "strip", z.ZodTypeAny, {
            type: "switch_group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        }, {
            type: "switch_group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        }>]>, "formFieldsQueryValue">;
        attributesQueryValue: z.ZodOptional<z.ZodUnion<[z.ZodObject<{
            id: z.ZodOptional<z.ZodString>;
            type: z.ZodLiteral<"group">;
            children1: z.ZodOptional<z.ZodEffects<z.ZodRecord<z.ZodString, z.ZodObject<{
                type: z.ZodOptional<z.ZodString>;
                properties: z.ZodOptional<z.ZodObject<{
                    field: z.ZodOptional<z.ZodAny>;
                    operator: z.ZodOptional<z.ZodAny>;
                    value: z.ZodOptional<z.ZodAny>;
                    valueSrc: z.ZodOptional<z.ZodAny>;
                    valueError: z.ZodOptional<z.ZodArray<z.ZodUnion<[z.ZodString, z.ZodNull]>, "many">>;
                    valueType: z.ZodOptional<z.ZodAny>;
                }, "strip", z.ZodTypeAny, {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                }, {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                }>>;
            }, "strip", z.ZodTypeAny, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }>>, Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }>, Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }>>>;
            properties: z.ZodAny;
        }, "strip", z.ZodTypeAny, {
            type: "group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        }, {
            type: "group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        }>, z.ZodObject<{
            id: z.ZodOptional<z.ZodString>;
            type: z.ZodLiteral<"switch_group">;
            children1: z.ZodOptional<z.ZodEffects<z.ZodRecord<z.ZodString, z.ZodObject<{
                type: z.ZodOptional<z.ZodString>;
                properties: z.ZodOptional<z.ZodObject<{
                    field: z.ZodOptional<z.ZodAny>;
                    operator: z.ZodOptional<z.ZodAny>;
                    value: z.ZodOptional<z.ZodAny>;
                    valueSrc: z.ZodOptional<z.ZodAny>;
                    valueError: z.ZodOptional<z.ZodArray<z.ZodUnion<[z.ZodString, z.ZodNull]>, "many">>;
                    valueType: z.ZodOptional<z.ZodAny>;
                }, "strip", z.ZodTypeAny, {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                }, {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                }>>;
            }, "strip", z.ZodTypeAny, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }>>, Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }>, Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }>>>;
            properties: z.ZodAny;
        }, "strip", z.ZodTypeAny, {
            type: "switch_group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        }, {
            type: "switch_group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        }>]>>;
        fallbackAttributesQueryValue: z.ZodOptional<z.ZodUnion<[z.ZodObject<{
            id: z.ZodOptional<z.ZodString>;
            type: z.ZodLiteral<"group">;
            children1: z.ZodOptional<z.ZodEffects<z.ZodRecord<z.ZodString, z.ZodObject<{
                type: z.ZodOptional<z.ZodString>;
                properties: z.ZodOptional<z.ZodObject<{
                    field: z.ZodOptional<z.ZodAny>;
                    operator: z.ZodOptional<z.ZodAny>;
                    value: z.ZodOptional<z.ZodAny>;
                    valueSrc: z.ZodOptional<z.ZodAny>;
                    valueError: z.ZodOptional<z.ZodArray<z.ZodUnion<[z.ZodString, z.ZodNull]>, "many">>;
                    valueType: z.ZodOptional<z.ZodAny>;
                }, "strip", z.ZodTypeAny, {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                }, {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                }>>;
            }, "strip", z.ZodTypeAny, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }>>, Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }>, Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }>>>;
            properties: z.ZodAny;
        }, "strip", z.ZodTypeAny, {
            type: "group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        }, {
            type: "group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        }>, z.ZodObject<{
            id: z.ZodOptional<z.ZodString>;
            type: z.ZodLiteral<"switch_group">;
            children1: z.ZodOptional<z.ZodEffects<z.ZodRecord<z.ZodString, z.ZodObject<{
                type: z.ZodOptional<z.ZodString>;
                properties: z.ZodOptional<z.ZodObject<{
                    field: z.ZodOptional<z.ZodAny>;
                    operator: z.ZodOptional<z.ZodAny>;
                    value: z.ZodOptional<z.ZodAny>;
                    valueSrc: z.ZodOptional<z.ZodAny>;
                    valueError: z.ZodOptional<z.ZodArray<z.ZodUnion<[z.ZodString, z.ZodNull]>, "many">>;
                    valueType: z.ZodOptional<z.ZodAny>;
                }, "strip", z.ZodTypeAny, {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                }, {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                }>>;
            }, "strip", z.ZodTypeAny, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }>>, Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }>, Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }>>>;
            properties: z.ZodAny;
        }, "strip", z.ZodTypeAny, {
            type: "switch_group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        }, {
            type: "switch_group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        }>]>>;
        isFallback: z.ZodOptional<z.ZodBoolean>;
        action: z.ZodObject<{
            type: z.ZodNativeEnum<typeof import("@calcom/routing-forms/zod").RouteActionType>;
            eventTypeId: z.ZodOptional<z.ZodNumber>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            type: import("@calcom/routing-forms/zod").RouteActionType;
            value: string;
            eventTypeId?: number | undefined;
        }, {
            type: import("@calcom/routing-forms/zod").RouteActionType;
            value: string;
            eventTypeId?: number | undefined;
        }>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        queryValue: ({
            type: "group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        } & z.BRAND<"formFieldsQueryValue">) | ({
            type: "switch_group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        } & z.BRAND<"formFieldsQueryValue">);
        action: {
            type: import("@calcom/routing-forms/zod").RouteActionType;
            value: string;
            eventTypeId?: number | undefined;
        };
        name?: string | undefined;
        attributeIdForWeights?: string | undefined;
        attributeRoutingConfig?: {
            skipContactOwner?: boolean | undefined;
            salesforce?: {
                rrSkipToAccountLookupField?: boolean | undefined;
                rrSKipToAccountLookupFieldName?: string | undefined;
            } | undefined;
        } | null | undefined;
        attributesQueryValue?: {
            type: "group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        } | {
            type: "switch_group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        } | undefined;
        fallbackAttributesQueryValue?: {
            type: "group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        } | {
            type: "switch_group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        } | undefined;
        isFallback?: boolean | undefined;
    }, {
        id: string;
        queryValue: {
            type: "group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        } | {
            type: "switch_group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        };
        action: {
            type: import("@calcom/routing-forms/zod").RouteActionType;
            value: string;
            eventTypeId?: number | undefined;
        };
        name?: string | undefined;
        attributeIdForWeights?: string | undefined;
        attributeRoutingConfig?: {
            skipContactOwner?: boolean | undefined;
            salesforce?: {
                rrSkipToAccountLookupField?: boolean | undefined;
                rrSKipToAccountLookupFieldName?: string | undefined;
            } | undefined;
        } | null | undefined;
        attributesQueryValue?: {
            type: "group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        } | {
            type: "switch_group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        } | undefined;
        fallbackAttributesQueryValue?: {
            type: "group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        } | {
            type: "switch_group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        } | undefined;
        isFallback?: boolean | undefined;
    }>;
    isPreview: z.ZodOptional<z.ZodBoolean>;
    _enablePerf: z.ZodOptional<z.ZodBoolean>;
    _concurrency: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    response: Record<string, {
        label: string;
        value: (string | number | string[]) & (string | number | string[] | undefined);
    }>;
    formId: string;
    route: {
        id: string;
        queryValue: ({
            type: "group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        } & z.BRAND<"formFieldsQueryValue">) | ({
            type: "switch_group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        } & z.BRAND<"formFieldsQueryValue">);
        action: {
            type: import("@calcom/routing-forms/zod").RouteActionType;
            value: string;
            eventTypeId?: number | undefined;
        };
        name?: string | undefined;
        attributeIdForWeights?: string | undefined;
        attributeRoutingConfig?: {
            skipContactOwner?: boolean | undefined;
            salesforce?: {
                rrSkipToAccountLookupField?: boolean | undefined;
                rrSKipToAccountLookupFieldName?: string | undefined;
            } | undefined;
        } | null | undefined;
        attributesQueryValue?: {
            type: "group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        } | {
            type: "switch_group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        } | undefined;
        fallbackAttributesQueryValue?: {
            type: "group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        } | {
            type: "switch_group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        } | undefined;
        isFallback?: boolean | undefined;
    };
    isPreview?: boolean | undefined;
    _enablePerf?: boolean | undefined;
    _concurrency?: number | undefined;
}, {
    response: Record<string, {
        label: string;
        value: (string | number | string[]) & (string | number | string[] | undefined);
    }>;
    formId: string;
    route: {
        id: string;
        queryValue: {
            type: "group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        } | {
            type: "switch_group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        };
        action: {
            type: import("@calcom/routing-forms/zod").RouteActionType;
            value: string;
            eventTypeId?: number | undefined;
        };
        name?: string | undefined;
        attributeIdForWeights?: string | undefined;
        attributeRoutingConfig?: {
            skipContactOwner?: boolean | undefined;
            salesforce?: {
                rrSkipToAccountLookupField?: boolean | undefined;
                rrSKipToAccountLookupFieldName?: string | undefined;
            } | undefined;
        } | null | undefined;
        attributesQueryValue?: {
            type: "group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        } | {
            type: "switch_group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        } | undefined;
        fallbackAttributesQueryValue?: {
            type: "group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        } | {
            type: "switch_group";
            id?: string | undefined;
            children1?: Record<string, {
                type?: string | undefined;
                properties?: {
                    field?: any;
                    operator?: any;
                    value?: any;
                    valueSrc?: any;
                    valueError?: (string | null)[] | undefined;
                    valueType?: any;
                } | undefined;
            }> | undefined;
            properties?: any;
        } | undefined;
        isFallback?: boolean | undefined;
    };
    isPreview?: boolean | undefined;
    _enablePerf?: boolean | undefined;
    _concurrency?: number | undefined;
}>;
export type TFindTeamMembersMatchingAttributeLogicOfRouteInputSchema = z.infer<typeof ZFindTeamMembersMatchingAttributeLogicOfRouteInputSchema>;
