import { z } from "zod";
export declare const assignUserToAttributeSchema: z.ZodObject<{
    userId: z.Zod<PERSON>ber;
    attributes: z.<PERSON><PERSON><PERSON><z.ZodObject<{
        id: z.ZodString;
        options: z.ZodOptional<z.ZodArray<z.ZodObject<{
            label: z.ZodOptional<z.ZodString>;
            value: z.ZodString;
            weight: z.ZodOptional<z.ZodNumber>;
        }, "strip", z.ZodTypeAny, {
            value: string;
            label?: string | undefined;
            weight?: number | undefined;
        }, {
            value: string;
            label?: string | undefined;
            weight?: number | undefined;
        }>, "many">>;
        value: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodType<PERSON>ny, {
        id: string;
        options?: {
            value: string;
            label?: string | undefined;
            weight?: number | undefined;
        }[] | undefined;
        value?: string | undefined;
    }, {
        id: string;
        options?: {
            value: string;
            label?: string | undefined;
            weight?: number | undefined;
        }[] | undefined;
        value?: string | undefined;
    }>, "many">;
}, "strip", z.<PERSON>odType<PERSON>, {
    userId: number;
    attributes: {
        id: string;
        options?: {
            value: string;
            label?: string | undefined;
            weight?: number | undefined;
        }[] | undefined;
        value?: string | undefined;
    }[];
}, {
    userId: number;
    attributes: {
        id: string;
        options?: {
            value: string;
            label?: string | undefined;
            weight?: number | undefined;
        }[] | undefined;
        value?: string | undefined;
    }[];
}>;
export type ZAssignUserToAttribute = z.infer<typeof assignUserToAttributeSchema>;
