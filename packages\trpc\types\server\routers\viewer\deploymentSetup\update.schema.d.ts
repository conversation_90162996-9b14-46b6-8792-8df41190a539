import { z } from "zod";
export declare const ZUpdateInputSchema: z.ZodObject<{
    licenseKey: z.ZodOptional<z.ZodString>;
    signatureToken: z.ZodOptional<z.ZodString>;
}, "strip", z.<PERSON>odTypeAny, {
    licenseKey?: string | undefined;
    signatureToken?: string | undefined;
}, {
    licenseKey?: string | undefined;
    signatureToken?: string | undefined;
}>;
export type TUpdateInputSchema = z.infer<typeof ZUpdateInputSchema>;
