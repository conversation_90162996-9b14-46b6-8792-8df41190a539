import { z } from "zod";
export declare const ZRemoveHostsFromEventTypes: z.ZodObject<{
    userIds: z.<PERSON><z.ZodN<PERSON><PERSON>, "many">;
    teamId: z.ZodN<PERSON>ber;
    eventTypeIds: z.<PERSON><z.ZodNumber, "many">;
}, "strip", z.ZodType<PERSON>ny, {
    teamId: number;
    userIds: number[];
    eventTypeIds: number[];
}, {
    teamId: number;
    userIds: number[];
    eventTypeIds: number[];
}>;
export type TRemoveHostsFromEventTypes = z.infer<typeof ZRemoveHostsFromEventTypes>;
