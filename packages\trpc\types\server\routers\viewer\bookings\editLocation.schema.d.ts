import { z } from "zod";
export declare const ZEditLocationInputSchema: z.ZodObject<{
    bookingId: z.ZodNumber;
    newLocation: z.ZodEffects<z.ZodString, string, string>;
    credentialId: z.<PERSON>od<PERSON>ullable<z.ZodNumber>;
}, "strip", z.Z<PERSON>ny, {
    credentialId: number | null;
    bookingId: number;
    newLocation: string;
}, {
    credentialId: number | null;
    bookingId: number;
    newLocation: string;
}>;
export type TEditLocationInputSchema = z.infer<typeof ZEditLocationInputSchema>;
