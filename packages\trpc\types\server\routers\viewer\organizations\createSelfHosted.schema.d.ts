import type { z } from "zod";
export declare const ZCreateSelfHostedInputSchema: z.ZodObject<{
    language: z.ZodOptional<z.ZodString>;
    logo: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    bio: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    onboardingId: z.ZodString;
    invitedMembers: z.ZodOptional<z.ZodArray<z.ZodObject<{
        email: z.ZodString;
        name: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        email: string;
        name?: string | undefined;
    }, {
        email: string;
        name?: string | undefined;
    }>, "many">>;
    teams: z.<PERSON>od<PERSON>ptional<z.ZodArray<z.ZodObject<{
        id: z.ZodNumber;
        name: z.ZodString;
        isBeingMigrated: z.ZodBoolean;
        slug: z.ZodNullable<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        name: string;
        id: number;
        slug: string | null;
        isBeingMigrated: boolean;
    }, {
        name: string;
        id: number;
        slug: string | null;
        isBeingMigrated: boolean;
    }>, "many">>;
}, "strip", z.ZodTypeAny, {
    onboardingId: string;
    language?: string | undefined;
    logo?: string | null | undefined;
    bio?: string | null | undefined;
    invitedMembers?: {
        email: string;
        name?: string | undefined;
    }[] | undefined;
    teams?: {
        name: string;
        id: number;
        slug: string | null;
        isBeingMigrated: boolean;
    }[] | undefined;
}, {
    onboardingId: string;
    language?: string | undefined;
    logo?: string | null | undefined;
    bio?: string | null | undefined;
    invitedMembers?: {
        email: string;
        name?: string | undefined;
    }[] | undefined;
    teams?: {
        name: string;
        id: number;
        slug: string | null;
        isBeingMigrated: boolean;
    }[] | undefined;
}>;
export type TCreateSelfHostedInputSchema = z.infer<typeof ZCreateSelfHostedInputSchema>;
