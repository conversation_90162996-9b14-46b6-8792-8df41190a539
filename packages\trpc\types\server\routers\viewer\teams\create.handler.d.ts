import type { TrpcSessionUser } from "../../../types";
import type { TCreateInputSchema } from "./create.schema";
type CreateOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TCreateInputSchema;
};
export declare const createHandler: ({ ctx, input }: CreateOptions) => Promise<{
    url: string;
    message: string;
    team: null;
} | {
    url: string;
    message: string;
    team: {
        name: string;
        id: number;
        metadata: import(".prisma/client").Prisma.JsonValue;
        createdAt: Date;
        bio: string | null;
        timeZone: string;
        weekStart: string;
        hideBranding: boolean;
        theme: string | null;
        timeFormat: number | null;
        brandColor: string | null;
        darkBrandColor: string | null;
        smsLockState: import(".prisma/client").$Enums.SMSLockState;
        smsLockReviewedByAdmin: boolean;
        slug: string | null;
        logoUrl: string | null;
        calVideoLogo: string | null;
        appLogo: string | null;
        appIconLogo: string | null;
        hideTeamProfileLink: boolean;
        isPrivate: boolean;
        hideBookATeamMember: boolean;
        rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
        rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
        bannerUrl: string | null;
        parentId: number | null;
        isOrganization: boolean;
        pendingPayment: boolean;
        isPlatform: boolean;
        createdByOAuthClientId: string | null;
        bookingLimits: import(".prisma/client").Prisma.JsonValue;
        includeManagedEventsInLimits: boolean;
    };
}>;
export default createHandler;
