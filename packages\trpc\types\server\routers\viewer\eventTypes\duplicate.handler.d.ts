import { Prisma } from "@prisma/client";
import type { TrpcSessionUser } from "../../../types";
import type { TDuplicateInputSchema } from "./duplicate.schema";
type DuplicateOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TDuplicateInputSchema;
};
export declare const duplicateHandler: ({ ctx, input }: DuplicateOptions) => Promise<{
    eventType: {
        calVideoSettings: {
            eventTypeId: number;
            createdAt: Date;
            updatedAt: Date;
            disableRecordingForOrganizer: boolean;
            disableRecordingForGuests: boolean;
            enableAutomaticTranscription: boolean;
            enableAutomaticRecordingForOrganizer: boolean;
            redirectUrlOnExit: string | null;
            disableTranscriptionForGuests: boolean;
            disableTranscriptionForOrganizer: boolean;
        } | null;
    } & {
        id: number;
        length: number;
        title: string;
        metadata: Prisma.JsonValue;
        description: string | null;
        userId: number | null;
        timeZone: string | null;
        slug: string;
        parentId: number | null;
        bookingLimits: Prisma.JsonValue;
        teamId: number | null;
        hidden: boolean;
        interfaceLanguage: string | null;
        position: number;
        locations: Prisma.JsonValue;
        offsetStart: number;
        profileId: number | null;
        useEventLevelSelectedCalendars: boolean;
        eventName: string | null;
        bookingFields: Prisma.JsonValue;
        periodType: import(".prisma/client").$Enums.PeriodType;
        periodStartDate: Date | null;
        periodEndDate: Date | null;
        periodDays: number | null;
        periodCountCalendarDays: boolean | null;
        lockTimeZoneToggleOnBookingPage: boolean;
        lockedTimeZone: string | null;
        requiresConfirmation: boolean;
        requiresConfirmationWillBlockSlot: boolean;
        requiresConfirmationForFreeEmail: boolean;
        requiresBookerEmailVerification: boolean;
        canSendCalVideoTranscriptionEmails: boolean;
        autoTranslateDescriptionEnabled: boolean;
        recurringEvent: Prisma.JsonValue;
        disableGuests: boolean;
        hideCalendarNotes: boolean;
        hideCalendarEventDetails: boolean;
        minimumBookingNotice: number;
        beforeEventBuffer: number;
        afterEventBuffer: number;
        seatsPerTimeSlot: number | null;
        onlyShowFirstAvailableSlot: boolean;
        disableCancelling: boolean | null;
        disableRescheduling: boolean | null;
        seatsShowAttendees: boolean | null;
        seatsShowAvailabilityCount: boolean | null;
        schedulingType: import(".prisma/client").$Enums.SchedulingType | null;
        scheduleId: number | null;
        allowReschedulingCancelledBookings: boolean | null;
        price: number;
        currency: string;
        slotInterval: number | null;
        successRedirectUrl: string | null;
        forwardParamsSuccessRedirect: boolean | null;
        durationLimits: Prisma.JsonValue;
        isInstantEvent: boolean;
        instantMeetingExpiryTimeOffsetInSeconds: number;
        instantMeetingScheduleId: number | null;
        instantMeetingParameters: string[];
        assignAllTeamMembers: boolean;
        assignRRMembersUsingSegment: boolean;
        rrSegmentQueryValue: Prisma.JsonValue;
        useEventTypeDestinationCalendarEmail: boolean;
        isRRWeightsEnabled: boolean;
        maxLeadThreshold: number | null;
        includeNoShowInRRCalculation: boolean;
        allowReschedulingPastBookings: boolean;
        hideOrganizerEmail: boolean;
        maxActiveBookingsPerBooker: number | null;
        maxActiveBookingPerBookerOfferReschedule: boolean;
        customReplyToEmail: string | null;
        eventTypeColor: Prisma.JsonValue;
        rescheduleWithSameRoundRobinHost: boolean;
        secondaryEmailId: number | null;
        useBookerTimezone: boolean;
        restrictionScheduleId: number | null;
        bookingRequiresAuthentication: boolean;
    };
}>;
export {};
