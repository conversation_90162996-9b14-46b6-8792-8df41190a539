import type { TrpcSessionUser } from "../../../types";
import type { TCreateSelfHostedInputSchema } from "./createSelfHosted.schema";
type CreateSelfHostedOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TCreateSelfHostedInputSchema;
};
export declare const createSelfHostedHandler: ({ input, ctx }: CreateSelfHostedOptions) => Promise<{
    organization: {
        metadata: import(".prisma/client").Prisma.JsonValue;
        name: string;
        id: number;
        createdAt: Date;
        bio: string | null;
        timeZone: string;
        weekStart: string;
        hideBranding: boolean;
        theme: string | null;
        timeFormat: number | null;
        brandColor: string | null;
        darkBrandColor: string | null;
        smsLockState: import(".prisma/client").$Enums.SMSLockState;
        smsLockReviewedByAdmin: boolean;
        slug: string | null;
        logoUrl: string | null;
        calVideoLogo: string | null;
        appLogo: string | null;
        appIconLogo: string | null;
        hideTeamProfileLink: boolean;
        isPrivate: boolean;
        hideBookATeamMember: boolean;
        rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
        rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
        bannerUrl: string | null;
        parentId: number | null;
        isOrganization: boolean;
        pendingPayment: boolean;
        isPlatform: boolean;
        createdByOAuthClientId: string | null;
        bookingLimits: import(".prisma/client").Prisma.JsonValue;
        includeManagedEventsInLimits: boolean;
    };
}>;
export default createSelfHostedHandler;
