import { z } from "zod";
export declare const ZCalendarOverlayInputSchema: z.ZodObject<{
    loggedInUsersTz: z.ZodString;
    dateFrom: z.ZodNullable<z.ZodString>;
    dateTo: z.ZodNullable<z.ZodString>;
    calendarsToLoad: z.<PERSON><z.ZodObject<{
        credentialId: z.ZodNumber;
        externalId: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        externalId: string;
        credentialId: number;
    }, {
        externalId: string;
        credentialId: number;
    }>, "many">;
}, "strip", z.<PERSON>odTypeAny, {
    dateFrom: string | null;
    dateTo: string | null;
    loggedInUsersTz: string;
    calendarsToLoad: {
        externalId: string;
        credentialId: number;
    }[];
}, {
    dateFrom: string | null;
    dateTo: string | null;
    loggedInUsersTz: string;
    calendarsToLoad: {
        externalId: string;
        credentialId: number;
    }[];
}>;
export type TCalendarOverlayInputSchema = z.infer<typeof ZCalendarOverlayInputSchema>;
