import type { TrpcSessionUser } from "../../../types";
import type { TValidateLicenseInputSchema } from "./validateLicense.schema";
type ValidateLicenseOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TValidateLicenseInputSchema;
};
export declare const validateLicenseHandler: ({ input }: ValidateLicenseOptions) => Promise<{
    valid: boolean;
    message: string;
}>;
export {};
