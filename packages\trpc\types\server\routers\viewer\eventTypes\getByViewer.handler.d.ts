import type { PrismaClient } from "@calcom/prisma";
import type { TrpcSessionUser } from "../../../types";
import type { TEventTypeInputSchema } from "./getByViewer.schema";
type GetByViewerOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
        prisma: PrismaClient;
    };
    input: TEventTypeInputSchema;
};
export declare const getByViewerHandler: ({ ctx, input }: GetByViewerOptions) => Promise<{
    allUsersAcrossAllEventTypes: Map<number, {
        name: string | null;
        id: number;
        username: string | null;
        avatarUrl: string | null;
        timeZone: string;
    } & {
        nonProfileUsername: string | null;
        profile: import("@calcom/types/UserProfile").UserProfile;
    }>;
    eventTypeGroups: {
        eventTypes: {
            userIds: number[];
            safeDescription: string | undefined;
            metadata: {
                config?: {
                    useHostSchedulesForTeamEvent?: boolean | undefined;
                } | undefined;
                smartContractAddress?: string | undefined;
                blockchainId?: number | undefined;
                multipleDuration?: number[] | undefined;
                giphyThankYouPage?: string | undefined;
                additionalNotesRequired?: boolean | undefined;
                disableSuccessPage?: boolean | undefined;
                disableStandardEmails?: {
                    all?: {
                        host?: boolean | undefined;
                        attendee?: boolean | undefined;
                    } | undefined;
                    confirmation?: {
                        host?: boolean | undefined;
                        attendee?: boolean | undefined;
                    } | undefined;
                } | undefined;
                managedEventConfig?: {
                    unlockedFields?: {
                        users?: true | undefined;
                        children?: true | undefined;
                        length?: true | undefined;
                        title?: true | undefined;
                        metadata?: true | undefined;
                        description?: true | undefined;
                        userId?: true | undefined;
                        calVideoSettings?: true | undefined;
                        destinationCalendar?: true | undefined;
                        profile?: true | undefined;
                        team?: true | undefined;
                        schedule?: true | undefined;
                        availability?: true | undefined;
                        hashedLink?: true | undefined;
                        secondaryEmail?: true | undefined;
                        customInputs?: true | undefined;
                        timeZone?: true | undefined;
                        bookings?: true | undefined;
                        selectedCalendars?: true | undefined;
                        webhooks?: true | undefined;
                        workflows?: true | undefined;
                        hosts?: true | undefined;
                        slug?: true | undefined;
                        parentId?: true | undefined;
                        bookingLimits?: true | undefined;
                        parent?: true | undefined;
                        teamId?: true | undefined;
                        hidden?: true | undefined;
                        _count?: true | undefined;
                        interfaceLanguage?: true | undefined;
                        position?: true | undefined;
                        locations?: true | undefined;
                        offsetStart?: true | undefined;
                        profileId?: true | undefined;
                        useEventLevelSelectedCalendars?: true | undefined;
                        eventName?: true | undefined;
                        bookingFields?: true | undefined;
                        periodType?: true | undefined;
                        periodStartDate?: true | undefined;
                        periodEndDate?: true | undefined;
                        periodDays?: true | undefined;
                        periodCountCalendarDays?: true | undefined;
                        lockTimeZoneToggleOnBookingPage?: true | undefined;
                        lockedTimeZone?: true | undefined;
                        requiresConfirmation?: true | undefined;
                        requiresConfirmationWillBlockSlot?: true | undefined;
                        requiresConfirmationForFreeEmail?: true | undefined;
                        requiresBookerEmailVerification?: true | undefined;
                        canSendCalVideoTranscriptionEmails?: true | undefined;
                        autoTranslateDescriptionEnabled?: true | undefined;
                        recurringEvent?: true | undefined;
                        disableGuests?: true | undefined;
                        hideCalendarNotes?: true | undefined;
                        hideCalendarEventDetails?: true | undefined;
                        minimumBookingNotice?: true | undefined;
                        beforeEventBuffer?: true | undefined;
                        afterEventBuffer?: true | undefined;
                        seatsPerTimeSlot?: true | undefined;
                        onlyShowFirstAvailableSlot?: true | undefined;
                        disableCancelling?: true | undefined;
                        disableRescheduling?: true | undefined;
                        seatsShowAttendees?: true | undefined;
                        seatsShowAvailabilityCount?: true | undefined;
                        schedulingType?: true | undefined;
                        scheduleId?: true | undefined;
                        allowReschedulingCancelledBookings?: true | undefined;
                        price?: true | undefined;
                        currency?: true | undefined;
                        slotInterval?: true | undefined;
                        successRedirectUrl?: true | undefined;
                        forwardParamsSuccessRedirect?: true | undefined;
                        durationLimits?: true | undefined;
                        isInstantEvent?: true | undefined;
                        instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                        instantMeetingScheduleId?: true | undefined;
                        instantMeetingParameters?: true | undefined;
                        assignAllTeamMembers?: true | undefined;
                        assignRRMembersUsingSegment?: true | undefined;
                        rrSegmentQueryValue?: true | undefined;
                        useEventTypeDestinationCalendarEmail?: true | undefined;
                        isRRWeightsEnabled?: true | undefined;
                        maxLeadThreshold?: true | undefined;
                        includeNoShowInRRCalculation?: true | undefined;
                        allowReschedulingPastBookings?: true | undefined;
                        hideOrganizerEmail?: true | undefined;
                        maxActiveBookingsPerBooker?: true | undefined;
                        maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                        customReplyToEmail?: true | undefined;
                        eventTypeColor?: true | undefined;
                        rescheduleWithSameRoundRobinHost?: true | undefined;
                        secondaryEmailId?: true | undefined;
                        useBookerTimezone?: true | undefined;
                        restrictionScheduleId?: true | undefined;
                        bookingRequiresAuthentication?: true | undefined;
                        owner?: true | undefined;
                        instantMeetingSchedule?: true | undefined;
                        aiPhoneCallConfig?: true | undefined;
                        fieldTranslations?: true | undefined;
                        restrictionSchedule?: true | undefined;
                        hostGroups?: true | undefined;
                    } | undefined;
                } | undefined;
                requiresConfirmationThreshold?: {
                    time: number;
                    unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
                } | undefined;
                bookerLayouts?: {
                    enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
                    defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
                } | null | undefined;
                apps?: unknown;
            } | null;
            children: {
                users: ({
                    name: string | null;
                    id: number;
                    username: string | null;
                    avatarUrl: string | null;
                    timeZone: string;
                } & {
                    nonProfileUsername: string | null;
                    profile: import("@calcom/types/UserProfile").UserProfile;
                })[];
                id: number;
                length: number;
                title: string;
                metadata: import(".prisma/client").Prisma.JsonValue;
                description: string | null;
                userId: number | null;
                timeZone: string | null;
                slug: string;
                parentId: number | null;
                bookingLimits: import(".prisma/client").Prisma.JsonValue;
                teamId: number | null;
                hidden: boolean;
                interfaceLanguage: string | null;
                position: number;
                locations: import(".prisma/client").Prisma.JsonValue;
                offsetStart: number;
                profileId: number | null;
                useEventLevelSelectedCalendars: boolean;
                eventName: string | null;
                bookingFields: import(".prisma/client").Prisma.JsonValue;
                periodType: import(".prisma/client").$Enums.PeriodType;
                periodStartDate: Date | null;
                periodEndDate: Date | null;
                periodDays: number | null;
                periodCountCalendarDays: boolean | null;
                lockTimeZoneToggleOnBookingPage: boolean;
                lockedTimeZone: string | null;
                requiresConfirmation: boolean;
                requiresConfirmationWillBlockSlot: boolean;
                requiresConfirmationForFreeEmail: boolean;
                requiresBookerEmailVerification: boolean;
                canSendCalVideoTranscriptionEmails: boolean;
                autoTranslateDescriptionEnabled: boolean;
                recurringEvent: import(".prisma/client").Prisma.JsonValue;
                disableGuests: boolean;
                hideCalendarNotes: boolean;
                hideCalendarEventDetails: boolean;
                minimumBookingNotice: number;
                beforeEventBuffer: number;
                afterEventBuffer: number;
                seatsPerTimeSlot: number | null;
                onlyShowFirstAvailableSlot: boolean;
                disableCancelling: boolean | null;
                disableRescheduling: boolean | null;
                seatsShowAttendees: boolean | null;
                seatsShowAvailabilityCount: boolean | null;
                schedulingType: import(".prisma/client").$Enums.SchedulingType | null;
                scheduleId: number | null;
                allowReschedulingCancelledBookings: boolean | null;
                price: number;
                currency: string;
                slotInterval: number | null;
                successRedirectUrl: string | null;
                forwardParamsSuccessRedirect: boolean | null;
                durationLimits: import(".prisma/client").Prisma.JsonValue;
                isInstantEvent: boolean;
                instantMeetingExpiryTimeOffsetInSeconds: number;
                instantMeetingScheduleId: number | null;
                instantMeetingParameters: string[];
                assignAllTeamMembers: boolean;
                assignRRMembersUsingSegment: boolean;
                rrSegmentQueryValue: import(".prisma/client").Prisma.JsonValue;
                useEventTypeDestinationCalendarEmail: boolean;
                isRRWeightsEnabled: boolean;
                maxLeadThreshold: number | null;
                includeNoShowInRRCalculation: boolean;
                allowReschedulingPastBookings: boolean;
                hideOrganizerEmail: boolean;
                maxActiveBookingsPerBooker: number | null;
                maxActiveBookingPerBookerOfferReschedule: boolean;
                customReplyToEmail: string | null;
                eventTypeColor: import(".prisma/client").Prisma.JsonValue;
                rescheduleWithSameRoundRobinHost: boolean;
                secondaryEmailId: number | null;
                useBookerTimezone: boolean;
                restrictionScheduleId: number | null;
                bookingRequiresAuthentication: boolean;
            }[];
            id: number;
            length: number;
            title: string;
            description: string | null;
            userId: number | null;
            team: {
                id: number;
                members: {
                    user: {
                        timeZone: string;
                    };
                }[];
            } | null;
            hashedLink: {
                link: string;
                id: number;
                expiresAt: Date | null;
                maxUsageCount: number;
                usageCount: number;
            }[];
            timeZone: string | null;
            hosts: ({
                user: {
                    name: string | null;
                    id: number;
                    username: string | null;
                    avatarUrl: string | null;
                    timeZone: string;
                };
            } & {
                userId: number;
                eventTypeId: number;
                createdAt: Date;
                scheduleId: number | null;
                weight: number | null;
                isFixed: boolean;
                priority: number | null;
                weightAdjustment: number | null;
                groupId: string | null;
                memberId: number | null;
            })[];
            slug: string;
            parentId: number | null;
            bookingLimits: import(".prisma/client").Prisma.JsonValue;
            teamId: number | null;
            hidden: boolean;
            interfaceLanguage: string | null;
            position: number;
            locations: import(".prisma/client").Prisma.JsonValue;
            offsetStart: number;
            profileId: number | null;
            useEventLevelSelectedCalendars: boolean;
            eventName: string | null;
            bookingFields: import(".prisma/client").Prisma.JsonValue;
            periodType: import(".prisma/client").$Enums.PeriodType;
            periodStartDate: Date | null;
            periodEndDate: Date | null;
            periodDays: number | null;
            periodCountCalendarDays: boolean | null;
            lockTimeZoneToggleOnBookingPage: boolean;
            lockedTimeZone: string | null;
            requiresConfirmation: boolean;
            requiresConfirmationWillBlockSlot: boolean;
            requiresConfirmationForFreeEmail: boolean;
            requiresBookerEmailVerification: boolean;
            canSendCalVideoTranscriptionEmails: boolean;
            autoTranslateDescriptionEnabled: boolean;
            recurringEvent: import(".prisma/client").Prisma.JsonValue;
            disableGuests: boolean;
            hideCalendarNotes: boolean;
            hideCalendarEventDetails: boolean;
            minimumBookingNotice: number;
            beforeEventBuffer: number;
            afterEventBuffer: number;
            seatsPerTimeSlot: number | null;
            onlyShowFirstAvailableSlot: boolean;
            disableCancelling: boolean | null;
            disableRescheduling: boolean | null;
            seatsShowAttendees: boolean | null;
            seatsShowAvailabilityCount: boolean | null;
            schedulingType: import(".prisma/client").$Enums.SchedulingType | null;
            scheduleId: number | null;
            allowReschedulingCancelledBookings: boolean | null;
            price: number;
            currency: string;
            slotInterval: number | null;
            successRedirectUrl: string | null;
            durationLimits: import(".prisma/client").Prisma.JsonValue;
            isInstantEvent: boolean;
            instantMeetingExpiryTimeOffsetInSeconds: number;
            instantMeetingScheduleId: number | null;
            instantMeetingParameters: string[];
            assignAllTeamMembers: boolean;
            assignRRMembersUsingSegment: boolean;
            rrSegmentQueryValue: import(".prisma/client").Prisma.JsonValue;
            useEventTypeDestinationCalendarEmail: boolean;
            isRRWeightsEnabled: boolean;
            maxLeadThreshold: number | null;
            allowReschedulingPastBookings: boolean;
            hideOrganizerEmail: boolean;
            customReplyToEmail: string | null;
            eventTypeColor: import(".prisma/client").Prisma.JsonValue;
            rescheduleWithSameRoundRobinHost: boolean;
            secondaryEmailId: number | null;
            useBookerTimezone: boolean;
            restrictionScheduleId: number | null;
            owner: {
                timeZone: string;
            } | null;
            instantMeetingSchedule: {
                name: string;
                id: number;
            } | null;
            aiPhoneCallConfig: {
                id: number;
                eventTypeId: number;
                enabled: boolean;
                templateType: string;
                schedulerName: string | null;
                generalPrompt: string | null;
                yourPhoneNumber: string;
                numberToCall: string;
                guestName: string | null;
                guestEmail: string | null;
                guestCompany: string | null;
                beginMessage: string | null;
                llmId: string | null;
            } | null;
        }[];
        teamId?: number | null;
        parentId?: number | null;
        bookerUrl: string;
        membershipRole?: import("@calcom/prisma/enums").MembershipRole | null;
        profile: {
            slug: (string | null) | null;
            name: string | null;
            image: string;
            eventTypesLockedByOrg?: boolean;
        };
        metadata: {
            membershipCount: number;
            readOnly: boolean;
        };
    }[];
    profiles: {
        teamId: number | null | undefined;
        membershipRole: import("@calcom/prisma/enums").MembershipRole | null | undefined;
        membershipCount: number;
        readOnly: boolean;
        slug: (string | null) | null;
        name: string | null;
        image: string;
        eventTypesLockedByOrg?: boolean;
    }[];
}>;
export {};
