import type { TrpcSessionUser } from "@calcom/trpc/server/types";
type Props = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
};
export declare const getUserTopBannersHandler: ({ ctx }: Props) => Promise<{
    teamUpgradeBanner: ({
        team: {
            children: {
                name: string;
                id: number;
                metadata: import(".prisma/client").Prisma.JsonValue;
                createdAt: Date;
                bio: string | null;
                timeZone: string;
                weekStart: string;
                hideBranding: boolean;
                theme: string | null;
                timeFormat: number | null;
                brandColor: string | null;
                darkBrandColor: string | null;
                smsLockState: import(".prisma/client").$Enums.SMSLockState;
                smsLockReviewedByAdmin: boolean;
                slug: string | null;
                logoUrl: string | null;
                calVideoLogo: string | null;
                appLogo: string | null;
                appIconLogo: string | null;
                hideTeamProfileLink: boolean;
                isPrivate: boolean;
                hideBookATeamMember: boolean;
                rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
                rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
                bannerUrl: string | null;
                parentId: number | null;
                isOrganization: boolean;
                pendingPayment: boolean;
                isPlatform: boolean;
                createdByOAuthClientId: string | null;
                bookingLimits: import(".prisma/client").Prisma.JsonValue;
                includeManagedEventsInLimits: boolean;
            }[];
        } & {
            name: string;
            id: number;
            metadata: import(".prisma/client").Prisma.JsonValue;
            createdAt: Date;
            bio: string | null;
            timeZone: string;
            weekStart: string;
            hideBranding: boolean;
            theme: string | null;
            timeFormat: number | null;
            brandColor: string | null;
            darkBrandColor: string | null;
            smsLockState: import(".prisma/client").$Enums.SMSLockState;
            smsLockReviewedByAdmin: boolean;
            slug: string | null;
            logoUrl: string | null;
            calVideoLogo: string | null;
            appLogo: string | null;
            appIconLogo: string | null;
            hideTeamProfileLink: boolean;
            isPrivate: boolean;
            hideBookATeamMember: boolean;
            rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
            rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
            bannerUrl: string | null;
            parentId: number | null;
            isOrganization: boolean;
            pendingPayment: boolean;
            isPlatform: boolean;
            createdByOAuthClientId: string | null;
            bookingLimits: import(".prisma/client").Prisma.JsonValue;
            includeManagedEventsInLimits: boolean;
        };
    } & {
        id: number;
        role: import(".prisma/client").$Enums.MembershipRole;
        userId: number;
        createdAt: Date | null;
        updatedAt: Date | null;
        disableImpersonation: boolean;
        teamId: number;
        accepted: boolean;
        customRoleId: string | null;
    })[];
    orgUpgradeBanner: ({
        team: {
            name: string;
            id: number;
            metadata: import(".prisma/client").Prisma.JsonValue;
            createdAt: Date;
            bio: string | null;
            timeZone: string;
            weekStart: string;
            hideBranding: boolean;
            theme: string | null;
            timeFormat: number | null;
            brandColor: string | null;
            darkBrandColor: string | null;
            smsLockState: import(".prisma/client").$Enums.SMSLockState;
            smsLockReviewedByAdmin: boolean;
            slug: string | null;
            logoUrl: string | null;
            calVideoLogo: string | null;
            appLogo: string | null;
            appIconLogo: string | null;
            hideTeamProfileLink: boolean;
            isPrivate: boolean;
            hideBookATeamMember: boolean;
            rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
            rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
            bannerUrl: string | null;
            parentId: number | null;
            isOrganization: boolean;
            pendingPayment: boolean;
            isPlatform: boolean;
            createdByOAuthClientId: string | null;
            bookingLimits: import(".prisma/client").Prisma.JsonValue;
            includeManagedEventsInLimits: boolean;
        };
    } & {
        id: number;
        role: import(".prisma/client").$Enums.MembershipRole;
        userId: number;
        createdAt: Date | null;
        updatedAt: Date | null;
        disableImpersonation: boolean;
        teamId: number;
        accepted: boolean;
        customRoleId: string | null;
    })[];
    verifyEmailBanner: boolean;
    calendarCredentialBanner: boolean;
    invalidAppCredentialBanners: import("@calcom/features/users/components/InvalidAppCredentialsBanner").InvalidAppCredentialBannerProps[];
}>;
export {};
