import type { PrismaTransaction } from "@calcom/prisma";
import type { AttributeType } from "@calcom/prisma/enums";
type SimpleAttributeInput = {
    id: string;
    value: string;
};
type SelectAttributeInput = {
    id: string;
    options: {
        value: string;
    }[];
    type: AttributeType;
};
type AttributeInput = {
    id: string;
    value?: string;
    options?: {
        value: string;
    }[];
    type?: AttributeType;
};
type ProcessAttributesResult = {
    userId: number;
    success: boolean;
    message?: string;
};
export declare const processUserAttributes: (tx: PrismaTransaction, userId: number, teamId: number, attributes: AttributeInput[]) => Promise<ProcessAttributesResult>;
export declare const handleSimpleAttribute: (tx: PrismaTransaction, memberId: number, attribute: SimpleAttributeInput) => Promise<void>;
export declare const handleSelectAttribute: (tx: PrismaTransaction, memberId: number, attribute: SelectAttributeInput) => Promise<void>;
export declare const removeAttribute: (tx: PrismaTransaction, memberId: number, attributeId: string) => Promise<void>;
export {};
