import { z } from "zod";
export declare const ZListMembersInputSchema: z.ZodObject<{
    teamIds: z.ZodOptional<z.<PERSON>od<PERSON>rray<z.ZodNumber, "many">>;
    searchText: z.ZodOptional<z.ZodString>;
    limit: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
    cursor: z.Zod<PERSON>ptional<z.ZodNullable<z.ZodNumber>>;
    adminOrOwnedTeamsOnly: z.ZodOptional<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    teamIds?: number[] | undefined;
    searchText?: string | undefined;
    limit?: number | null | undefined;
    cursor?: number | null | undefined;
    adminOrOwnedTeamsOnly?: boolean | undefined;
}, {
    teamIds?: number[] | undefined;
    searchText?: string | undefined;
    limit?: number | null | undefined;
    cursor?: number | null | undefined;
    adminOrOwnedTeamsOnly?: boolean | undefined;
}>;
export declare const ZLegacyListMembersInputSchema: z.ZodObject<{
    cursor: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
    teamIds: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
    limit: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
    searchText: z.ZodOptional<z.ZodString>;
    adminOrOwnedTeamsOnly: z.ZodOptional<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    cursor?: number | null | undefined;
    teamIds?: number[] | undefined;
    limit?: number | null | undefined;
    searchText?: string | undefined;
    adminOrOwnedTeamsOnly?: boolean | undefined;
}, {
    cursor?: number | null | undefined;
    teamIds?: number[] | undefined;
    limit?: number | null | undefined;
    searchText?: string | undefined;
    adminOrOwnedTeamsOnly?: boolean | undefined;
}>;
export type TListMembersInputSchema = z.infer<typeof ZListMembersInputSchema>;
export type TLegacyListMembersInputSchema = z.infer<typeof ZLegacyListMembersInputSchema>;
