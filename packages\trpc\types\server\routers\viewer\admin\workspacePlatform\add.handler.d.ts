import type { z } from "zod";
import type { workspacePlatformCreateSchema } from "./schema";
export default function addHandler({ input, }: {
    input: z.infer<typeof workspacePlatformCreateSchema>;
}): Promise<Omit<{
    name: string;
    id: number;
    description: string;
    createdAt: Date;
    updatedAt: Date;
    slug: string;
    enabled: boolean;
    defaultServiceAccountKey: import(".prisma/client").Prisma.JsonValue;
}, "defaultServiceAccountKey"> & {
    defaultServiceAccountKey: undefined;
}>;
