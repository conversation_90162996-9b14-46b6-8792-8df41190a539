import type { TrpcSessionUser } from "../../../types";
import type { TWhitelistUserWorkflows } from "./whitelistUserWorkflows.schema";
type GetOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TWhitelistUserWorkflows;
};
export declare const whitelistUserWorkflows: ({ input }: GetOptions) => Promise<{
    whitelistWorkflows: boolean;
}>;
export default whitelistUserWorkflows;
