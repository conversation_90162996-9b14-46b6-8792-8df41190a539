import { z } from "zod";
export declare const ZUserInputSchema: z.ZodObject<{
    username: z.ZodString;
    dateFrom: z.ZodString;
    dateTo: z.ZodString;
    eventTypeId: z.Zod<PERSON>ptional<z.ZodUnion<[z.ZodEffects<z.ZodString, number, string>, z.ZodNumber]>>;
    withSource: z.Z<PERSON>ptional<z.ZodBoolean>;
}, "strip", z.ZodType<PERSON>ny, {
    username: string;
    dateFrom: string;
    dateTo: string;
    eventTypeId?: number | undefined;
    withSource?: boolean | undefined;
}, {
    username: string;
    dateFrom: string;
    dateTo: string;
    eventTypeId?: string | number | undefined;
    withSource?: boolean | undefined;
}>;
export type TUserInputSchema = z.infer<typeof ZUserInputSchema>;
