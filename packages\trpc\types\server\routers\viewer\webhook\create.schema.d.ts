import { z } from "zod";
export declare const ZCreateInputSchema: z.ZodObject<{
    id: z.ZodOptional<z.ZodString>;
    subscriberUrl: z.ZodString;
    eventTriggers: z.<PERSON><PERSON><PERSON><z.ZodEnum<["BOOKING_CANCELLED", "BOOKING_CREATED", "BOOKING_RESCHEDULED", "BOOKING_PAID", "BOOKING_PAYMENT_INITIATED", "MEETING_ENDED", "MEETING_STARTED", "BOOKING_REQUESTED", "BOOKING_REJECTED", "RECORDING_READY", "INSTANT_MEETING", "RECORDING_TRANSCRIPTION_GENERATED", "BOOKING_NO_SHOW_UPDATED", "OOO_CREATED", "AFTER_HOSTS_CAL_VIDEO_NO_SHOW", "AFTER_GUESTS_CAL_VIDEO_NO_SHOW", "FORM_SUBMITTED", "FORM_SUBMITTED_NO_EVENT"]>, "many">;
    active: z.ZodBoolean;
    payloadTemplate: z.<PERSON>odNullable<z.ZodString>;
    eventTypeId: z.ZodOptional<z.ZodNumber>;
    appId: z.ZodNullable<z.ZodOptional<z.ZodString>>;
    secret: z.ZodNullable<z.ZodOptional<z.ZodString>>;
    teamId: z.ZodOptional<z.ZodNumber>;
    platform: z.ZodOptional<z.ZodBoolean>;
    time: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
    timeUnit: z.ZodOptional<z.ZodNullable<z.ZodEnum<["DAY", "HOUR", "MINUTE"]>>>;
}, "strip", z.ZodTypeAny, {
    active: boolean;
    subscriberUrl: string;
    payloadTemplate: string | null;
    eventTriggers: ("BOOKING_CREATED" | "BOOKING_PAYMENT_INITIATED" | "BOOKING_PAID" | "BOOKING_RESCHEDULED" | "BOOKING_REQUESTED" | "BOOKING_CANCELLED" | "BOOKING_REJECTED" | "BOOKING_NO_SHOW_UPDATED" | "FORM_SUBMITTED" | "MEETING_ENDED" | "MEETING_STARTED" | "RECORDING_READY" | "INSTANT_MEETING" | "RECORDING_TRANSCRIPTION_GENERATED" | "OOO_CREATED" | "AFTER_HOSTS_CAL_VIDEO_NO_SHOW" | "AFTER_GUESTS_CAL_VIDEO_NO_SHOW" | "FORM_SUBMITTED_NO_EVENT")[];
    id?: string | undefined;
    eventTypeId?: number | undefined;
    appId?: string | null | undefined;
    secret?: string | null | undefined;
    teamId?: number | undefined;
    platform?: boolean | undefined;
    time?: number | null | undefined;
    timeUnit?: "DAY" | "HOUR" | "MINUTE" | null | undefined;
}, {
    active: boolean;
    subscriberUrl: string;
    payloadTemplate: string | null;
    eventTriggers: ("BOOKING_CREATED" | "BOOKING_PAYMENT_INITIATED" | "BOOKING_PAID" | "BOOKING_RESCHEDULED" | "BOOKING_REQUESTED" | "BOOKING_CANCELLED" | "BOOKING_REJECTED" | "BOOKING_NO_SHOW_UPDATED" | "FORM_SUBMITTED" | "MEETING_ENDED" | "MEETING_STARTED" | "RECORDING_READY" | "INSTANT_MEETING" | "RECORDING_TRANSCRIPTION_GENERATED" | "OOO_CREATED" | "AFTER_HOSTS_CAL_VIDEO_NO_SHOW" | "AFTER_GUESTS_CAL_VIDEO_NO_SHOW" | "FORM_SUBMITTED_NO_EVENT")[];
    id?: string | undefined;
    eventTypeId?: number | undefined;
    appId?: string | null | undefined;
    secret?: string | null | undefined;
    teamId?: number | undefined;
    platform?: boolean | undefined;
    time?: number | null | undefined;
    timeUnit?: "DAY" | "HOUR" | "MINUTE" | null | undefined;
}>;
export type TCreateInputSchema = z.infer<typeof ZCreateInputSchema>;
