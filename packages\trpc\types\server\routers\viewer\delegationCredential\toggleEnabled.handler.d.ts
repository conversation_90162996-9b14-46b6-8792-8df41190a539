import type { z } from "zod";
import type { DelegationCredentialToggleEnabledSchema } from "./schema";
type LoggedInUser = {
    id: number;
    email: string;
    locale: string;
    emailVerified: Date | null;
    organizationId: number | null;
};
export default function toggleEnabledHandler({ ctx, input, }: {
    ctx: {
        user: LoggedInUser;
    };
    input: z.infer<typeof DelegationCredentialToggleEnabledSchema>;
}): Promise<{
    id: string;
    workspacePlatform: {
        name: string;
        slug: string;
    };
    createdAt: Date;
    updatedAt: Date;
    organizationId: number;
    enabled: boolean;
    domain: string;
    lastEnabledAt: Date | null;
    lastDisabledAt: Date | null;
} | null>;
export declare function toggleDelegationCredentialEnabled(loggedInUser: Omit<LoggedInUser, "locale" | "emailVerified">, input: z.infer<typeof DelegationCredentialToggleEnabledSchema>): Promise<{
    id: string;
    workspacePlatform: {
        name: string;
        slug: string;
    };
    createdAt: Date;
    updatedAt: Date;
    organizationId: number;
    enabled: boolean;
    domain: string;
    lastEnabledAt: Date | null;
    lastDisabledAt: Date | null;
} | null>;
export {};
