import type { TrpcSessionUser } from "../../../types";
import type { TGetInputSchema } from "./get.schema";
type GetDataOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TGetInputSchema;
};
export declare const get: ({ ctx, input }: GetDataOptions) => Promise<{
    membership: {
        role: import(".prisma/client").$Enums.MembershipRole;
        accepted: boolean;
    };
    inviteToken: {
        identifier: string;
        expires: Date;
        token: string;
        expiresInDays: number | null;
    } | undefined;
    metadata: {
        defaultConferencingApp?: {
            appSlug?: string | undefined;
            appLink?: string | undefined;
        } | undefined;
        requestedSlug?: string | null | undefined;
        orgSeats?: number | null | undefined;
        orgPricePerSeat?: number | null | undefined;
        migratedToOrgFrom?: {
            teamSlug?: string | null | undefined;
            lastMigrationTime?: string | undefined;
            reverted?: boolean | undefined;
            lastRevertTime?: string | undefined;
        } | undefined;
        billingPeriod?: import("@calcom/prisma/zod-utils").BillingPeriod | undefined;
    };
    bookingLimits: Partial<Record<"PER_DAY" | "PER_MONTH" | "PER_WEEK" | "PER_YEAR", number | undefined>> | null;
    logo?: string | undefined;
    name: string;
    id: number;
    children: {
        name: string;
        slug: string | null;
    }[];
    bio: string | null;
    hideBranding: boolean;
    theme: string | null;
    brandColor: string | null;
    darkBrandColor: string | null;
    slug: string | null;
    logoUrl: string | null;
    hideTeamProfileLink: boolean;
    isPrivate: boolean;
    hideBookATeamMember: boolean;
    rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
    rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
    parentId: number | null;
    includeManagedEventsInLimits: boolean;
    parent: {
        name: string;
        id: number;
        metadata: import(".prisma/client").Prisma.JsonValue;
        slug: string | null;
        logoUrl: string | null;
        isPrivate: boolean;
        isOrganization: boolean;
    } | null;
    isOrganization: boolean;
}>;
export default get;
