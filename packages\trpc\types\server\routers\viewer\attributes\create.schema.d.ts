import { z } from "zod";
export declare const createAttributeSchema: z.ZodObject<{
    name: z.ZodString;
    type: z.Z<PERSON>E<PERSON><["TEXT", "NUMBER", "SINGLE_SELECT", "MULTI_SELECT"]>;
    isLocked: z.<PERSON><z.ZodBoolean>;
    options: z.<PERSON><z.ZodObject<{
        value: z.ZodString;
        isGroup: z.ZodOptional<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        value: string;
        isGroup?: boolean | undefined;
    }, {
        value: string;
        isGroup?: boolean | undefined;
    }>, "many">;
}, "strip", z.<PERSON><PERSON><PERSON><PERSON>, {
    name: string;
    type: "TEXT" | "NUMBER" | "SINGLE_SELECT" | "MULTI_SELECT";
    options: {
        value: string;
        isGroup?: boolean | undefined;
    }[];
    isLocked?: boolean | undefined;
}, {
    name: string;
    type: "TEXT" | "NUMBER" | "SINGLE_SELECT" | "MULTI_SELECT";
    options: {
        value: string;
        isGroup?: boolean | undefined;
    }[];
    isLocked?: boolean | undefined;
}>;
export type ZCreateAttributeSchema = z.infer<typeof createAttributeSchema>;
