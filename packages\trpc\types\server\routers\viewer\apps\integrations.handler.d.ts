import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TIntegrationsInputSchema } from "./integrations.schema";
type IntegrationsOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TIntegrationsInputSchema;
};
export declare const integrationsHandler: ({ ctx, input }: IntegrationsOptions) => Promise<{
    items: {
        dependencyData?: import("@calcom/app-store/_appRegistry").TDependencyData | undefined;
        userCredentialIds: number[];
        invalidCredentialIds: number[];
        teams: ({
            teamId: number;
            name: string;
            logoUrl: string | null;
            credentialId: number;
            isAdmin: boolean;
        } | null)[];
        isInstalled: boolean | undefined;
        isSetupAlready: boolean | undefined;
        credentialOwner?: import("@calcom/app-store/types").CredentialOwner | undefined;
        installed?: boolean;
        type: `${string}_calendar` | `${string}_messaging` | `${string}_payment` | `${string}_video` | `${string}_other` | `${string}_automation` | `${string}_analytics` | `${string}_crm` | `${string}_other_calendar`;
        title?: string;
        name: string;
        description: string;
        variant: "calendar" | "payment" | "conferencing" | "video" | "other" | "other_calendar" | "automation" | "crm";
        slug: string;
        category?: string;
        categories: import(".prisma/client").AppCategories[];
        extendsFeature?: "EventType" | "User";
        logo: string;
        publisher: string;
        url: string;
        docsUrl?: string;
        verified?: boolean;
        trending?: boolean;
        rating?: number;
        reviews?: number;
        isGlobal?: boolean;
        simplePath?: string;
        email: string;
        feeType?: "monthly" | "usage-based" | "one-time" | "free";
        price?: number;
        commission?: number;
        licenseRequired?: boolean;
        teamsPlanRequired?: {
            upgradeUrl: string;
        };
        appData?: import("@calcom/types/App").AppData;
        paid?: import("@calcom/types/App").PaidAppData;
        dirName?: string;
        isTemplate?: boolean;
        __template?: string;
        dependencies?: string[];
        concurrentMeetings?: boolean;
        createdAt?: string;
        isOAuth?: boolean;
        delegationCredential?: {
            workspacePlatformSlug: string;
        };
        locationOption: import("@calcom/app-store/utils").LocationOption | null;
        enabled: boolean;
    }[];
}>;
export {};
