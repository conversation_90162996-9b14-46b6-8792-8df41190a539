import type { TrpcSessionUser } from "@calcom/trpc/server/types";
type ListInvitesOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
};
export declare const listInvitesHandler: ({ ctx }: ListInvitesOptions) => Promise<{
    id: number;
    role: import(".prisma/client").$Enums.MembershipRole;
    userId: number;
    createdAt: Date | null;
    updatedAt: Date | null;
    disableImpersonation: boolean;
    teamId: number;
    accepted: boolean;
    customRoleId: string | null;
}[]>;
export default listInvitesHandler;
