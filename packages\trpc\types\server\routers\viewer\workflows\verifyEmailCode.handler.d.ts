import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TVerifyEmailCodeInputSchema } from "./verifyEmailCode.schema";
type VerifyEmailCodeOptions = {
    ctx: {
        user: Pick<NonNullable<TrpcSessionUser>, "id">;
    };
    input: TVerifyEmailCodeInputSchema;
};
export declare const verifyEmailCodeHandler: ({ ctx, input }: VerifyEmailCodeOptions) => Promise<true>;
export {};
