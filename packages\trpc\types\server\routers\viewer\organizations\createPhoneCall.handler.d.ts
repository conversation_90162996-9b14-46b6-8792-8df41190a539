import type { z } from "zod";
import type { createPhoneCallSchema } from "@calcom/features/calAIPhone/zod-utils";
import type { PrismaClient } from "@calcom/prisma";
import type { TrpcSessionUser } from "@calcom/trpc/server/types";
type CreatePhoneCallProps = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
        prisma: PrismaClient;
    };
    input: z.infer<typeof createPhoneCallSchema>;
};
declare const createPhoneCallHandler: ({ input, ctx }: CreatePhoneCallProps) => Promise<{
    callId: string;
    agentId: string | undefined;
}>;
export default createPhoneCallHandler;
