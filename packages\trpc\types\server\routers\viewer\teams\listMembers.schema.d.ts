import { z } from "zod";
export declare const ZListMembersInputSchema: z.ZodObject<{
    teamId: z.ZodNumber;
    limit: z.<PERSON>odDef<PERSON><z.ZodNumber>;
    searchTerm: z.<PERSON>odOptional<z.ZodString>;
    cursor: z.Zod<PERSON>ullable<z.ZodOptional<z.ZodNumber>>;
}, "strip", z.<PERSON>, {
    teamId: number;
    limit: number;
    searchTerm?: string | undefined;
    cursor?: number | null | undefined;
}, {
    teamId: number;
    limit?: number | undefined;
    searchTerm?: string | undefined;
    cursor?: number | null | undefined;
}>;
export type TListMembersInputSchema = z.infer<typeof ZListMembersInputSchema>;
