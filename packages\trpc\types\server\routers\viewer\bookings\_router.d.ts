export declare const bookingsRouter: import("@trpc/server/unstable-core-do-not-import").CreateRouterInner<import("@trpc/server/unstable-core-do-not-import").RootConfig<{
    ctx: import("../../../createContext").InnerContext;
    meta: object;
    errorShape: {
        message: string;
        code: number;
        data: {
            code: string;
            httpStatus: number;
            path?: string;
            [key: string]: unknown;
        };
    };
    transformer: {
        stringify: (object: any) => string;
        parse: <T = unknown>(string: string) => T;
        serialize: (object: any) => import("superjson/dist/types").SuperJSONResult;
        deserialize: <T_1 = unknown>(payload: import("superjson/dist/types").SuperJSONResult) => T_1;
        registerClass: (v: import("superjson/dist/types").Class, options?: string | import("superjson/dist/class-registry").RegisterOptions | undefined) => void;
        registerSymbol: (v: Symbol, identifier?: string | undefined) => void;
        registerCustom: <I, O extends import("superjson/dist/types").JSONValue>(transformer: Omit<import("superjson/dist/custom-transformer-registry").CustomTransfomer<I, O>, "name">, name: string) => void;
        allowErrorProps: (...props: string[]) => void;
    };
}>, {
    get: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            filters: {
                teamIds?: number[] | undefined;
                userIds?: number[] | undefined;
                status?: "past" | "upcoming" | "recurring" | "unconfirmed" | "cancelled" | undefined;
                eventTypeIds?: number[] | undefined;
                attendeeEmail?: string | {
                    type: import("@calcom/features/data-table/lib/types").ColumnFilterType.TEXT;
                    data: {
                        operator: "endsWith" | "startsWith" | "equals" | "notEquals" | "contains" | "notContains" | "isEmpty" | "isNotEmpty";
                        operand: string;
                    };
                } | undefined;
                attendeeName?: string | {
                    type: import("@calcom/features/data-table/lib/types").ColumnFilterType.TEXT;
                    data: {
                        operator: "endsWith" | "startsWith" | "equals" | "notEquals" | "contains" | "notContains" | "isEmpty" | "isNotEmpty";
                        operand: string;
                    };
                } | undefined;
                bookingUid?: string | undefined;
                afterStartDate?: string | undefined;
                beforeEndDate?: string | undefined;
                afterUpdatedDate?: string | undefined;
                beforeUpdatedDate?: string | undefined;
                afterCreatedDate?: string | undefined;
                beforeCreatedDate?: string | undefined;
            };
            limit: number;
            offset?: number | undefined;
        };
        output: {
            bookings: {
                rescheduler: string | null;
                eventType: {
                    recurringEvent: import("@calcom/types/Calendar").RecurringEvent | null;
                    eventTypeColor: {
                        lightEventTypeColor: string;
                        darkEventTypeColor: string;
                    } | null;
                    price: number;
                    currency: string;
                    metadata: {
                        config?: {
                            useHostSchedulesForTeamEvent?: boolean | undefined;
                        } | undefined;
                        smartContractAddress?: string | undefined;
                        blockchainId?: number | undefined;
                        multipleDuration?: number[] | undefined;
                        giphyThankYouPage?: string | undefined;
                        additionalNotesRequired?: boolean | undefined;
                        disableSuccessPage?: boolean | undefined;
                        disableStandardEmails?: {
                            all?: {
                                host?: boolean | undefined;
                                attendee?: boolean | undefined;
                            } | undefined;
                            confirmation?: {
                                host?: boolean | undefined;
                                attendee?: boolean | undefined;
                            } | undefined;
                        } | undefined;
                        managedEventConfig?: {
                            unlockedFields?: {
                                users?: true | undefined;
                                children?: true | undefined;
                                length?: true | undefined;
                                title?: true | undefined;
                                metadata?: true | undefined;
                                description?: true | undefined;
                                userId?: true | undefined;
                                calVideoSettings?: true | undefined;
                                destinationCalendar?: true | undefined;
                                profile?: true | undefined;
                                team?: true | undefined;
                                schedule?: true | undefined;
                                availability?: true | undefined;
                                hashedLink?: true | undefined;
                                secondaryEmail?: true | undefined;
                                customInputs?: true | undefined;
                                timeZone?: true | undefined;
                                bookings?: true | undefined;
                                selectedCalendars?: true | undefined;
                                webhooks?: true | undefined;
                                workflows?: true | undefined;
                                hosts?: true | undefined;
                                slug?: true | undefined;
                                parentId?: true | undefined;
                                bookingLimits?: true | undefined;
                                parent?: true | undefined;
                                teamId?: true | undefined;
                                hidden?: true | undefined;
                                _count?: true | undefined;
                                interfaceLanguage?: true | undefined;
                                position?: true | undefined;
                                locations?: true | undefined;
                                offsetStart?: true | undefined;
                                profileId?: true | undefined;
                                useEventLevelSelectedCalendars?: true | undefined;
                                eventName?: true | undefined;
                                bookingFields?: true | undefined;
                                periodType?: true | undefined;
                                periodStartDate?: true | undefined;
                                periodEndDate?: true | undefined;
                                periodDays?: true | undefined;
                                periodCountCalendarDays?: true | undefined;
                                lockTimeZoneToggleOnBookingPage?: true | undefined;
                                lockedTimeZone?: true | undefined;
                                requiresConfirmation?: true | undefined;
                                requiresConfirmationWillBlockSlot?: true | undefined;
                                requiresConfirmationForFreeEmail?: true | undefined;
                                requiresBookerEmailVerification?: true | undefined;
                                canSendCalVideoTranscriptionEmails?: true | undefined;
                                autoTranslateDescriptionEnabled?: true | undefined;
                                recurringEvent?: true | undefined;
                                disableGuests?: true | undefined;
                                hideCalendarNotes?: true | undefined;
                                hideCalendarEventDetails?: true | undefined;
                                minimumBookingNotice?: true | undefined;
                                beforeEventBuffer?: true | undefined;
                                afterEventBuffer?: true | undefined;
                                seatsPerTimeSlot?: true | undefined;
                                onlyShowFirstAvailableSlot?: true | undefined;
                                disableCancelling?: true | undefined;
                                disableRescheduling?: true | undefined;
                                seatsShowAttendees?: true | undefined;
                                seatsShowAvailabilityCount?: true | undefined;
                                schedulingType?: true | undefined;
                                scheduleId?: true | undefined;
                                allowReschedulingCancelledBookings?: true | undefined;
                                price?: true | undefined;
                                currency?: true | undefined;
                                slotInterval?: true | undefined;
                                successRedirectUrl?: true | undefined;
                                forwardParamsSuccessRedirect?: true | undefined;
                                durationLimits?: true | undefined;
                                isInstantEvent?: true | undefined;
                                instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                                instantMeetingScheduleId?: true | undefined;
                                instantMeetingParameters?: true | undefined;
                                assignAllTeamMembers?: true | undefined;
                                assignRRMembersUsingSegment?: true | undefined;
                                rrSegmentQueryValue?: true | undefined;
                                useEventTypeDestinationCalendarEmail?: true | undefined;
                                isRRWeightsEnabled?: true | undefined;
                                maxLeadThreshold?: true | undefined;
                                includeNoShowInRRCalculation?: true | undefined;
                                allowReschedulingPastBookings?: true | undefined;
                                hideOrganizerEmail?: true | undefined;
                                maxActiveBookingsPerBooker?: true | undefined;
                                maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                                customReplyToEmail?: true | undefined;
                                eventTypeColor?: true | undefined;
                                rescheduleWithSameRoundRobinHost?: true | undefined;
                                secondaryEmailId?: true | undefined;
                                useBookerTimezone?: true | undefined;
                                restrictionScheduleId?: true | undefined;
                                bookingRequiresAuthentication?: true | undefined;
                                owner?: true | undefined;
                                instantMeetingSchedule?: true | undefined;
                                aiPhoneCallConfig?: true | undefined;
                                fieldTranslations?: true | undefined;
                                restrictionSchedule?: true | undefined;
                                hostGroups?: true | undefined;
                            } | undefined;
                        } | undefined;
                        requiresConfirmationThreshold?: {
                            time: number;
                            unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
                        } | undefined;
                        bookerLayouts?: {
                            enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
                            defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
                        } | null | undefined;
                        apps?: unknown;
                    } | null;
                    id?: number | undefined;
                    length?: number | undefined;
                    title?: string | undefined;
                    slug?: string | undefined;
                    eventName?: string | null | undefined;
                    disableGuests?: boolean | undefined;
                    disableCancelling?: boolean | null | undefined;
                    disableRescheduling?: boolean | null | undefined;
                    seatsShowAttendees?: boolean | null | undefined;
                    seatsShowAvailabilityCount?: boolean | null | undefined;
                    allowReschedulingPastBookings?: boolean | undefined;
                    hideOrganizerEmail?: boolean | undefined;
                    customReplyToEmail?: string | null | undefined;
                    schedulingType?: import("@calcom/prisma/enums").SchedulingType | null | undefined;
                    hosts?: {
                        userId: number;
                        user: {
                            id: number;
                            email: string;
                        } | null;
                    }[] | undefined;
                    team?: {
                        id: number;
                        name: string;
                        slug: string | null;
                    } | null | undefined;
                    hostGroups?: {
                        id: string;
                        name: string;
                    }[] | undefined;
                };
                startTime: string;
                endTime: string;
                id: number;
                title: string;
                metadata: unknown;
                description: string | null;
                uid: string;
                userPrimaryEmail: string | null;
                customInputs: unknown;
                location: string | null;
                createdAt: Date;
                updatedAt: Date | null;
                paid: boolean;
                rescheduled: boolean | null;
                fromReschedule: string | null;
                recurringEventId: string | null;
                isRecorded: boolean;
                responses: import(".prisma/client").Prisma.JsonValue;
                status: import("@calcom/prisma/enums").BookingStatus;
                routedFromRoutingFormReponse: {
                    id: number;
                } | null;
                references: {
                    id: number;
                    type: string;
                    title: string;
                    metadata: unknown;
                    status: "rejected" | "cancelled" | "awaiting_host" | "pending" | "accepted";
                    description: string | null;
                    startTime: Date;
                    endTime: Date;
                    userId: number | null;
                    uid: string;
                    idempotencyKey: string | null;
                    userPrimaryEmail: string | null;
                    eventTypeId: number | null;
                    customInputs: unknown;
                    responses: unknown;
                    location: string | null;
                    createdAt: Date;
                    updatedAt: Date | null;
                    paid: boolean;
                    destinationCalendarId: number | null;
                    cancellationReason: string | null;
                    rejectionReason: string | null;
                    reassignReason: string | null;
                    reassignById: number | null;
                    dynamicEventSlugRef: string | null;
                    dynamicGroupSlugRef: string | null;
                    rescheduled: boolean | null;
                    fromReschedule: string | null;
                    recurringEventId: string | null;
                    smsReminderNumber: string | null;
                    scheduledJobs: string[];
                    isRecorded: boolean;
                    iCalUID: string | null;
                    iCalSequence: number;
                    rating: number | null;
                    ratingFeedback: string | null;
                    noShowHost: boolean | null;
                    oneTimePassword: string | null;
                    cancelledBy: string | null;
                    rescheduledBy: string | null;
                    creationSource: import("@calcom/kysely/types").CreationSource | null;
                    credentialId: number | null;
                    delegationCredentialId: string | null;
                    domainWideDelegationCredentialId: string | null;
                    deleted: boolean | null;
                    bookingId: number | null;
                    thirdPartyRecurringEventId: string | null;
                    meetingId: string | null;
                    meetingPassword: string | null;
                    meetingUrl: string | null;
                    externalCalendarId: string | null;
                }[];
                payment: {
                    paymentOption: "ON_BOOKING" | "HOLD" | null;
                    currency: string;
                    success: boolean;
                    amount: number;
                }[];
                user: {
                    id: number;
                    name: string | null;
                    email: string;
                } | null;
                attendees: {
                    name: string;
                    id: number;
                    title: string;
                    metadata: unknown;
                    status: "rejected" | "cancelled" | "awaiting_host" | "pending" | "accepted";
                    description: string | null;
                    locale: string | null;
                    startTime: Date;
                    endTime: Date;
                    userId: number | null;
                    uid: string;
                    idempotencyKey: string | null;
                    userPrimaryEmail: string | null;
                    eventTypeId: number | null;
                    customInputs: unknown;
                    responses: unknown;
                    location: string | null;
                    createdAt: Date;
                    updatedAt: Date | null;
                    paid: boolean;
                    destinationCalendarId: number | null;
                    cancellationReason: string | null;
                    rejectionReason: string | null;
                    reassignReason: string | null;
                    reassignById: number | null;
                    dynamicEventSlugRef: string | null;
                    dynamicGroupSlugRef: string | null;
                    rescheduled: boolean | null;
                    fromReschedule: string | null;
                    recurringEventId: string | null;
                    smsReminderNumber: string | null;
                    scheduledJobs: string[];
                    isRecorded: boolean;
                    iCalUID: string | null;
                    iCalSequence: number;
                    rating: number | null;
                    ratingFeedback: string | null;
                    noShowHost: boolean | null;
                    oneTimePassword: string | null;
                    cancelledBy: string | null;
                    rescheduledBy: string | null;
                    creationSource: import("@calcom/kysely/types").CreationSource | null;
                    email: string;
                    timeZone: string;
                    bookingId: number | null;
                    phoneNumber: string | null;
                    noShow: boolean | null;
                }[];
                seatsReferences: {
                    referenceUid: string;
                    attendee: {
                        email: string;
                    } | null;
                }[];
                assignmentReason: {
                    id: number;
                    title: string;
                    metadata: unknown;
                    status: "rejected" | "cancelled" | "awaiting_host" | "pending" | "accepted";
                    description: string | null;
                    startTime: Date;
                    endTime: Date;
                    userId: number | null;
                    uid: string;
                    idempotencyKey: string | null;
                    userPrimaryEmail: string | null;
                    eventTypeId: number | null;
                    customInputs: unknown;
                    responses: unknown;
                    location: string | null;
                    createdAt: Date;
                    updatedAt: Date | null;
                    paid: boolean;
                    destinationCalendarId: number | null;
                    cancellationReason: string | null;
                    rejectionReason: string | null;
                    reassignReason: string | null;
                    reassignById: number | null;
                    dynamicEventSlugRef: string | null;
                    dynamicGroupSlugRef: string | null;
                    rescheduled: boolean | null;
                    fromReschedule: string | null;
                    recurringEventId: string | null;
                    smsReminderNumber: string | null;
                    scheduledJobs: string[];
                    isRecorded: boolean;
                    iCalUID: string | null;
                    iCalSequence: number;
                    rating: number | null;
                    ratingFeedback: string | null;
                    noShowHost: boolean | null;
                    oneTimePassword: string | null;
                    cancelledBy: string | null;
                    rescheduledBy: string | null;
                    creationSource: import("@calcom/kysely/types").CreationSource | null;
                    bookingId: number;
                    reasonEnum: import("@calcom/kysely/types").AssignmentReasonEnum;
                    reasonString: string;
                }[];
            }[];
            recurringInfo: {
                recurringEventId: string | null;
                count: number;
                firstDate: Date | null;
                bookings: {
                    [key: string]: Date[];
                };
            }[];
            totalCount: number;
        };
    }>;
    requestReschedule: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            bookingId: string;
            rescheduleReason?: string | undefined;
        };
        output: void;
    }>;
    editLocation: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            bookingId: number;
            credentialId: number | null;
            newLocation: string;
        };
        output: {
            message: string;
        };
    }>;
    addGuests: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            guests: string[];
            bookingId: number;
        };
        output: {
            message: string;
        };
    }>;
    confirm: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            bookingId: number;
            confirmed: boolean;
            recurringEventId?: string | undefined;
            reason?: string | undefined;
            emailsEnabled?: boolean | undefined;
            platformClientParams?: {
                platformClientId?: string | undefined;
                platformRescheduleUrl?: string | null | undefined;
                platformCancelUrl?: string | null | undefined;
                platformBookingUrl?: string | null | undefined;
                platformBookingLocation?: string | undefined;
                areCalendarEventsEnabled?: boolean | undefined;
            } | undefined;
        };
        output: {
            message: string;
            status: "ACCEPTED" | "REJECTED";
        };
    }>;
    getBookingAttendees: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            seatReferenceUid: string;
        };
        output: number;
    }>;
    find: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            bookingUid?: string | undefined;
        };
        output: {
            booking: {
                id: number;
                status: import(".prisma/client").$Enums.BookingStatus;
                description: string | null;
                startTime: Date;
                endTime: Date;
                uid: string;
                eventTypeId: number | null;
                paid: boolean;
            } | null;
        };
    }>;
    getInstantBookingLocation: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            bookingId: number;
        };
        output: {
            booking: {
                id: number;
                metadata: import(".prisma/client").Prisma.JsonValue;
                status: import(".prisma/client").$Enums.BookingStatus;
                description: string | null;
                startTime: Date;
                endTime: Date;
                uid: string;
                eventTypeId: number | null;
                location: string | null;
            } | null;
        };
    }>;
}>;
