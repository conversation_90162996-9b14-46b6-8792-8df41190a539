import type { TDelegationCredentialGetAffectedMembersForDisableSchema } from "./schema";
export declare function getAffectedMembersForDisable({ delegationCredentialId, }: {
    delegationCredentialId: string;
}): Promise<{
    email: string;
    name: string | null;
    id: number;
}[]>;
export default function getAffectedMembersForDisableHandler({ input, }: {
    input: TDelegationCredentialGetAffectedMembersForDisableSchema;
}): Promise<{
    email: string;
    name: string | null;
    id: number;
}[]>;
