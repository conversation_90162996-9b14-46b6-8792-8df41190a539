import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TAppCredentialsByTypeInputSchema } from "./appCredentialsByType.schema";
type AppCredentialsByTypeOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TAppCredentialsByTypeInputSchema;
};
/** Used for grabbing credentials on specific app pages */
export declare const appCredentialsByTypeHandler: ({ ctx, input }: AppCredentialsByTypeOptions) => Promise<{
    credentials: ({
        id: number;
        delegatedToId: string;
        userId: number;
        user: {
            email: string;
        };
        key: {
            access_token: string;
        };
        invalid: boolean;
        teamId: null;
        team: null;
        delegationCredentialId: string;
        type: `${string}_other` | `${string}_calendar` | `${string}_messaging` | `${string}_payment` | `${string}_video` | `${string}_automation` | `${string}_analytics` | `${string}_crm` | `${string}_other_calendar`;
        appId: string;
    } | {
        user: {
            name: string | null;
        } | null;
        id: number;
        type: string;
        userId: number | null;
        team: {
            name: string;
        } | null;
        delegationCredentialId: string | null;
        teamId: number | null;
        appId: string | null;
        invalid: boolean | null;
    })[];
    userAdminTeams: number[];
}>;
export {};
