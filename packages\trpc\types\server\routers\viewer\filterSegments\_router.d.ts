export declare const filterSegmentsRouter: import("@trpc/server/unstable-core-do-not-import").CreateRouterInner<import("@trpc/server/unstable-core-do-not-import").RootConfig<{
    ctx: import("../../../createContext").InnerContext;
    meta: object;
    errorShape: {
        message: string;
        code: number;
        data: {
            code: string;
            httpStatus: number;
            path?: string;
            [key: string]: unknown;
        };
    };
    transformer: {
        stringify: (object: any) => string;
        parse: <T = unknown>(string: string) => T;
        serialize: (object: any) => import("superjson/dist/types").SuperJSONResult;
        deserialize: <T_1 = unknown>(payload: import("superjson/dist/types").SuperJSONResult) => T_1;
        registerClass: (v: import("superjson/dist/types").Class, options?: string | import("superjson/dist/class-registry").RegisterOptions | undefined) => void;
        registerSymbol: (v: Symbol, identifier?: string | undefined) => void;
        registerCustom: <I, O extends import("superjson/dist/types").JSONValue>(transformer: Omit<import("superjson/dist/custom-transformer-registry").CustomTransfomer<I, O>, "name">, name: string) => void;
        allowErrorProps: (...props: string[]) => void;
    };
}>, {
    list: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            tableIdentifier: string;
        };
        output: {
            segments: import("@calcom/features/data-table/lib/types").FilterSegmentOutput[];
            preferredSegmentId: {
                id: number;
                type: "user";
            } | {
                id: string;
                type: "system";
            } | null;
        };
    }>;
    create: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            name: string;
            perPage: number;
            tableIdentifier: string;
            teamId: number;
            scope: "TEAM";
            activeFilters?: {
                f: string;
                v?: {
                    type: import("@calcom/features/data-table/lib/types").ColumnFilterType.SINGLE_SELECT;
                    data: string | number;
                } | {
                    type: import("@calcom/features/data-table/lib/types").ColumnFilterType.MULTI_SELECT;
                    data: (string | number)[];
                } | {
                    type: import("@calcom/features/data-table/lib/types").ColumnFilterType.TEXT;
                    data: {
                        operator: "endsWith" | "startsWith" | "equals" | "notEquals" | "contains" | "notContains" | "isEmpty" | "isNotEmpty";
                        operand: string;
                    };
                } | {
                    type: import("@calcom/features/data-table/lib/types").ColumnFilterType.NUMBER;
                    data: {
                        operator: "eq" | "neq" | "gt" | "gte" | "lt" | "lte";
                        operand: number;
                    };
                } | {
                    type: import("@calcom/features/data-table/lib/types").ColumnFilterType.DATE_RANGE;
                    data: {
                        startDate: string | null;
                        endDate: string | null;
                        preset: string;
                    };
                } | undefined;
            }[] | undefined;
            sorting?: {
                id: string;
                desc: boolean;
            }[] | undefined;
            columnVisibility?: Record<string, boolean> | undefined;
            columnSizing?: Record<string, number> | undefined;
            searchTerm?: string | null | undefined;
        } | {
            name: string;
            perPage: number;
            tableIdentifier: string;
            scope: "USER";
            activeFilters?: {
                f: string;
                v?: {
                    type: import("@calcom/features/data-table/lib/types").ColumnFilterType.SINGLE_SELECT;
                    data: string | number;
                } | {
                    type: import("@calcom/features/data-table/lib/types").ColumnFilterType.MULTI_SELECT;
                    data: (string | number)[];
                } | {
                    type: import("@calcom/features/data-table/lib/types").ColumnFilterType.TEXT;
                    data: {
                        operator: "endsWith" | "startsWith" | "equals" | "notEquals" | "contains" | "notContains" | "isEmpty" | "isNotEmpty";
                        operand: string;
                    };
                } | {
                    type: import("@calcom/features/data-table/lib/types").ColumnFilterType.NUMBER;
                    data: {
                        operator: "eq" | "neq" | "gt" | "gte" | "lt" | "lte";
                        operand: number;
                    };
                } | {
                    type: import("@calcom/features/data-table/lib/types").ColumnFilterType.DATE_RANGE;
                    data: {
                        startDate: string | null;
                        endDate: string | null;
                        preset: string;
                    };
                } | undefined;
            }[] | undefined;
            sorting?: {
                id: string;
                desc: boolean;
            }[] | undefined;
            columnVisibility?: Record<string, boolean> | undefined;
            columnSizing?: Record<string, number> | undefined;
            searchTerm?: string | null | undefined;
            teamId?: undefined;
        };
        output: import("@calcom/features/data-table/lib/types").FilterSegmentOutput;
    }>;
    update: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            id: number;
            teamId: number;
            scope: "TEAM";
            name?: string | undefined;
            tableIdentifier?: string | undefined;
            activeFilters?: {
                f: string;
                v?: {
                    type: import("@calcom/features/data-table/lib/types").ColumnFilterType.SINGLE_SELECT;
                    data: string | number;
                } | {
                    type: import("@calcom/features/data-table/lib/types").ColumnFilterType.MULTI_SELECT;
                    data: (string | number)[];
                } | {
                    type: import("@calcom/features/data-table/lib/types").ColumnFilterType.TEXT;
                    data: {
                        operator: "endsWith" | "startsWith" | "equals" | "notEquals" | "contains" | "notContains" | "isEmpty" | "isNotEmpty";
                        operand: string;
                    };
                } | {
                    type: import("@calcom/features/data-table/lib/types").ColumnFilterType.NUMBER;
                    data: {
                        operator: "eq" | "neq" | "gt" | "gte" | "lt" | "lte";
                        operand: number;
                    };
                } | {
                    type: import("@calcom/features/data-table/lib/types").ColumnFilterType.DATE_RANGE;
                    data: {
                        startDate: string | null;
                        endDate: string | null;
                        preset: string;
                    };
                } | undefined;
            }[] | undefined;
            sorting?: {
                id: string;
                desc: boolean;
            }[] | undefined;
            columnVisibility?: Record<string, boolean> | undefined;
            columnSizing?: Record<string, number> | undefined;
            searchTerm?: string | null | undefined;
            perPage?: number | undefined;
        } | {
            id: number;
            scope: "USER";
            name?: string | undefined;
            tableIdentifier?: string | undefined;
            activeFilters?: {
                f: string;
                v?: {
                    type: import("@calcom/features/data-table/lib/types").ColumnFilterType.SINGLE_SELECT;
                    data: string | number;
                } | {
                    type: import("@calcom/features/data-table/lib/types").ColumnFilterType.MULTI_SELECT;
                    data: (string | number)[];
                } | {
                    type: import("@calcom/features/data-table/lib/types").ColumnFilterType.TEXT;
                    data: {
                        operator: "endsWith" | "startsWith" | "equals" | "notEquals" | "contains" | "notContains" | "isEmpty" | "isNotEmpty";
                        operand: string;
                    };
                } | {
                    type: import("@calcom/features/data-table/lib/types").ColumnFilterType.NUMBER;
                    data: {
                        operator: "eq" | "neq" | "gt" | "gte" | "lt" | "lte";
                        operand: number;
                    };
                } | {
                    type: import("@calcom/features/data-table/lib/types").ColumnFilterType.DATE_RANGE;
                    data: {
                        startDate: string | null;
                        endDate: string | null;
                        preset: string;
                    };
                } | undefined;
            }[] | undefined;
            sorting?: {
                id: string;
                desc: boolean;
            }[] | undefined;
            columnVisibility?: Record<string, boolean> | undefined;
            columnSizing?: Record<string, number> | undefined;
            searchTerm?: string | null | undefined;
            perPage?: number | undefined;
        };
        output: import("@calcom/features/data-table/lib/types").FilterSegmentOutput;
    }>;
    delete: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            id: number;
        };
        output: {
            id: number;
            message: string;
        };
    }>;
    setPreference: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            segmentId: {
                id: string;
                type: "system";
            } | {
                id: number;
                type: "user";
            } | null;
            tableIdentifier: string;
        };
        output: {
            id: number;
            segmentId: number | null;
            tableIdentifier: string;
            userId: number;
            createdAt: Date;
            updatedAt: Date;
            systemSegmentId: string | null;
        } | null;
    }>;
}>;
