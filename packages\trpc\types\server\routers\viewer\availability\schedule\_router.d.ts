export declare const scheduleRouter: import("@trpc/server/unstable-core-do-not-import").CreateRouterInner<import("@trpc/server/unstable-core-do-not-import").RootConfig<{
    ctx: import("../../../../createContext").InnerContext;
    meta: object;
    errorShape: {
        message: string;
        code: number;
        data: {
            code: string;
            httpStatus: number;
            path?: string;
            [key: string]: unknown;
        };
    };
    transformer: {
        stringify: (object: any) => string;
        parse: <T = unknown>(string: string) => T;
        serialize: (object: any) => import("superjson/dist/types").SuperJSONResult;
        deserialize: <T_1 = unknown>(payload: import("superjson/dist/types").SuperJSONResult) => T_1;
        registerClass: (v: import("superjson/dist/types").Class, options?: string | import("superjson/dist/class-registry").RegisterOptions | undefined) => void;
        registerSymbol: (v: Symbol, identifier?: string | undefined) => void;
        registerCustom: <I, O extends import("superjson/dist/types").JSONValue>(transformer: Omit<import("superjson/dist/custom-transformer-registry").CustomTransfomer<I, O>, "name">, name: string) => void;
        allowErrorProps: (...props: string[]) => void;
    };
}>, {
    get: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            scheduleId?: number | undefined;
            isManagedEventType?: boolean | undefined;
        };
        output: {
            id: number;
            name: string;
            isManaged: boolean;
            workingHours: import("@calcom/types/schedule").WorkingHours[];
            schedule: {
                id: number;
                date: Date | null;
                startTime: Date;
                endTime: Date;
                userId: number | null;
                eventTypeId: number | null;
                scheduleId: number | null;
                days: number[];
            }[];
            availability: {
                end: Date;
                userId?: number | null;
                start: Date;
            }[][];
            timeZone: string;
            dateOverrides: {
                ranges: import("@calcom/types/schedule").TimeRange[];
            }[];
            isDefault: boolean;
            isLastSchedule: boolean;
            readOnly: boolean;
            userId: number;
        };
    }>;
    create: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            name: string;
            schedule?: {
                end: Date;
                start: Date;
            }[][] | undefined;
            eventTypeId?: number | undefined;
        };
        output: {
            schedule: {
                name: string;
                id: number;
                userId: number;
                timeZone: string | null;
            };
        };
    }>;
    delete: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            scheduleId: number;
        };
        output: void;
    }>;
    update: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            scheduleId: number;
            timeZone?: string | undefined;
            name?: string | undefined;
            isDefault?: boolean | undefined;
            schedule?: {
                end: Date;
                start: Date;
            }[][] | undefined;
            dateOverrides?: {
                end: Date;
                start: Date;
            }[] | undefined;
        };
        output: {
            schedule: {
                name: string;
                id: number;
                userId: number;
            };
            isDefault: boolean;
            availability?: undefined;
            timeZone?: undefined;
            prevDefaultId?: undefined;
            currentDefaultId?: undefined;
        } | {
            schedule: {
                name: string;
                id: number;
                userId: number;
                eventType: {
                    id: number;
                    eventName: string | null;
                }[];
                availability: {
                    id: number;
                    date: Date | null;
                    startTime: Date;
                    endTime: Date;
                    userId: number | null;
                    eventTypeId: number | null;
                    scheduleId: number | null;
                    days: number[];
                }[];
                timeZone: string | null;
            };
            availability: import("@calcom/types/schedule").Schedule;
            timeZone: string;
            isDefault: boolean;
            prevDefaultId: number | null;
            currentDefaultId: number | null;
        };
    }>;
    duplicate: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            scheduleId: number;
        };
        output: {
            schedule: {
                name: string;
                id: number;
                userId: number;
                timeZone: string | null;
            };
        };
    }>;
    getScheduleByUserId: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            userId?: number | undefined;
        };
        output: {
            hasDefaultSchedule: boolean;
            id: number;
            name: string;
            isManaged: boolean;
            workingHours: import("@calcom/types/schedule").WorkingHours[];
            schedule: {
                id: number;
                date: Date | null;
                startTime: Date;
                endTime: Date;
                userId: number | null;
                eventTypeId: number | null;
                scheduleId: number | null;
                days: number[];
            }[];
            availability: {
                end: Date;
                userId?: number | null;
                start: Date;
            }[][];
            timeZone: string;
            dateOverrides: {
                ranges: import("@calcom/types/schedule").TimeRange[];
            }[];
            isDefault: boolean;
            isLastSchedule: boolean;
            readOnly: boolean;
            userId: number;
        } | {
            id: number;
            name: string;
            availability: never[][];
            dateOverrides: never[];
            timeZone: string;
            workingHours: never[];
            isDefault: boolean;
            hasDefaultSchedule: boolean;
        };
    }>;
    getAllSchedulesByUserId: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            userId: number;
        };
        output: {
            schedules: {
                isDefault: boolean;
                readOnly: boolean;
                name: string;
                id: number;
                userId: number;
            }[];
        };
    }>;
    getScheduleByEventSlug: import("@trpc/server/unstable-core-do-not-import").QueryProcedure<{
        input: {
            eventSlug: string;
        };
        output: {
            id: number;
            name: string;
            isManaged: boolean;
            workingHours: import("@calcom/types/schedule").WorkingHours[];
            schedule: {
                id: number;
                date: Date | null;
                startTime: Date;
                endTime: Date;
                userId: number | null;
                eventTypeId: number | null;
                scheduleId: number | null;
                days: number[];
            }[];
            availability: {
                end: Date;
                userId?: number | null;
                start: Date;
            }[][];
            timeZone: string;
            dateOverrides: {
                ranges: import("@calcom/types/schedule").TimeRange[];
            }[];
            isDefault: boolean;
            isLastSchedule: boolean;
            readOnly: boolean;
            userId: number;
        } | {
            id: number;
            name: string;
            availability: never[][];
            dateOverrides: never[];
            timeZone: string;
            workingHours: never[];
            isDefault: boolean;
        };
    }>;
    bulkUpdateToDefaultAvailability: import("@trpc/server/unstable-core-do-not-import").MutationProcedure<{
        input: {
            eventTypeIds: number[];
            selectedDefaultScheduleId?: number | null | undefined;
        };
        output: import("@prisma/client/runtime/library").GetBatchResult;
    }>;
}>;
