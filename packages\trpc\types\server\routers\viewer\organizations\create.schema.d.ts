import { z } from "zod";
export declare enum BillingPeriod {
    MONTHLY = "MONTHLY",
    ANNUALLY = "ANNUALLY"
}
export declare const ZCreateInputSchema: z.ZodObject<{
    name: z.ZodString;
    slug: z.ZodEffects<z.ZodString, string, string>;
    orgOwnerEmail: z.ZodString;
    language: z.ZodOptional<z.ZodString>;
    seats: z.ZodOptional<z.ZodNumber>;
    pricePerSeat: z.ZodOptional<z.ZodNumber>;
    isPlatform: z.ZodDefault<z.ZodBoolean>;
    billingPeriod: z.ZodOptional<z.ZodDefault<z.ZodNativeEnum<typeof BillingPeriod>>>;
    creationSource: z.ZodNativeEnum<{
        readonly API_V1: "API_V1";
        readonly API_V2: "API_V2";
        readonly WEBAPP: "WEBAPP";
    }>;
}, "strip", z.<PERSON>odTypeAny, {
    name: string;
    creationSource: "API_V1" | "API_V2" | "WEBAPP";
    slug: string;
    isPlatform: boolean;
    orgOwnerEmail: string;
    language?: string | undefined;
    seats?: number | undefined;
    pricePerSeat?: number | undefined;
    billingPeriod?: BillingPeriod | undefined;
}, {
    name: string;
    creationSource: "API_V1" | "API_V2" | "WEBAPP";
    slug: string;
    orgOwnerEmail: string;
    language?: string | undefined;
    seats?: number | undefined;
    pricePerSeat?: number | undefined;
    isPlatform?: boolean | undefined;
    billingPeriod?: BillingPeriod | undefined;
}>;
export type TCreateInputSchema = z.infer<typeof ZCreateInputSchema>;
