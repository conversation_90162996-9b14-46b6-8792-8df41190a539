import { z } from "zod";
export declare const ZGetRoundRobinHostsInputSchema: z.ZodObject<{
    bookingId: z.Zod<PERSON>umber;
    exclude: z.<PERSON><PERSON>al<"fixedHosts">;
    cursor: z.<PERSON>od<PERSON>ptional<z.ZodNumber>;
    limit: z.<PERSON>od<PERSON>ptional<z.ZodNumber>;
    searchTerm: z.Zod<PERSON>ptional<z.ZodString>;
}, "strip", z.<PERSON>ny, {
    bookingId: number;
    exclude: "fixedHosts";
    cursor?: number | undefined;
    limit?: number | undefined;
    searchTerm?: string | undefined;
}, {
    bookingId: number;
    exclude: "fixedHosts";
    cursor?: number | undefined;
    limit?: number | undefined;
    searchTerm?: string | undefined;
}>;
export type TGetRoundRobinHostsToReassignInputSchema = z.infer<typeof ZGetRoundRobinHostsInputSchema>;
