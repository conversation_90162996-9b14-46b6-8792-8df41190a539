import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TChangeMemberRoleInputSchema } from "./changeMemberRole.schema";
type ChangeMemberRoleOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TChangeMemberRoleInputSchema;
};
export declare const changeMemberRoleHandler: ({ ctx, input }: ChangeMemberRoleOptions) => Promise<({
    user: {
        name: string | null;
        id: number;
        role: import(".prisma/client").$Enums.UserPermissionRole;
        metadata: import(".prisma/client").Prisma.JsonValue;
        locale: string | null;
        startTime: number;
        endTime: number;
        creationSource: import(".prisma/client").$Enums.CreationSource | null;
        email: string;
        movedToProfileId: number | null;
        username: string | null;
        emailVerified: Date | null;
        bio: string | null;
        avatarUrl: string | null;
        timeZone: string;
        weekStart: string;
        bufferTime: number;
        hideBranding: boolean;
        theme: string | null;
        appTheme: string | null;
        createdDate: Date;
        trialEndsAt: Date | null;
        lastActiveAt: Date | null;
        defaultScheduleId: number | null;
        completedOnboarding: boolean;
        timeFormat: number | null;
        twoFactorSecret: string | null;
        twoFactorEnabled: boolean;
        backupCodes: string | null;
        identityProvider: import(".prisma/client").$Enums.IdentityProvider;
        identityProviderId: string | null;
        invitedTo: number | null;
        brandColor: string | null;
        darkBrandColor: string | null;
        allowDynamicBooking: boolean | null;
        allowSEOIndexing: boolean | null;
        receiveMonthlyDigestEmail: boolean | null;
        verified: boolean | null;
        disableImpersonation: boolean;
        organizationId: number | null;
        locked: boolean;
        isPlatformManaged: boolean;
        smsLockState: import(".prisma/client").$Enums.SMSLockState;
        smsLockReviewedByAdmin: boolean;
        referralLinkId: string | null;
        whitelistWorkflows: boolean;
    };
    team: {
        name: string;
        id: number;
        metadata: import(".prisma/client").Prisma.JsonValue;
        createdAt: Date;
        bio: string | null;
        timeZone: string;
        weekStart: string;
        hideBranding: boolean;
        theme: string | null;
        timeFormat: number | null;
        brandColor: string | null;
        darkBrandColor: string | null;
        smsLockState: import(".prisma/client").$Enums.SMSLockState;
        smsLockReviewedByAdmin: boolean;
        slug: string | null;
        logoUrl: string | null;
        calVideoLogo: string | null;
        appLogo: string | null;
        appIconLogo: string | null;
        hideTeamProfileLink: boolean;
        isPrivate: boolean;
        hideBookATeamMember: boolean;
        rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
        rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
        bannerUrl: string | null;
        parentId: number | null;
        isOrganization: boolean;
        pendingPayment: boolean;
        isPlatform: boolean;
        createdByOAuthClientId: string | null;
        bookingLimits: import(".prisma/client").Prisma.JsonValue;
        includeManagedEventsInLimits: boolean;
    };
} & {
    id: number;
    role: import(".prisma/client").$Enums.MembershipRole;
    userId: number;
    createdAt: Date | null;
    updatedAt: Date | null;
    disableImpersonation: boolean;
    teamId: number;
    accepted: boolean;
    customRoleId: string | null;
}) | null>;
export default changeMemberRoleHandler;
