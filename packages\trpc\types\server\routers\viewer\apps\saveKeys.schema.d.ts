import { z } from "zod";
export declare const ZSaveKeysInputSchema: z.ZodObject<{
    slug: z.ZodString;
    dirName: z.ZodString;
    type: z.ZodString;
    keys: z.ZodUnknown;
    fromEnabled: z.<PERSON>od<PERSON>ptional<z.ZodBoolean>;
}, "strip", z.Zod<PERSON>ype<PERSON>ny, {
    type: string;
    slug: string;
    dirName: string;
    keys?: unknown;
    fromEnabled?: boolean | undefined;
}, {
    type: string;
    slug: string;
    dirName: string;
    keys?: unknown;
    fromEnabled?: boolean | undefined;
}>;
export type TSaveKeysInputSchema = z.infer<typeof ZSaveKeysInputSchema>;
