import type { Booking, EventType, BookingReference, Attendee, Credential, DestinationCalendar, User } from "@prisma/client";
export declare const bookingsProcedure: import("@trpc/server/unstable-core-do-not-import").ProcedureBuilder<import("../../../createContext").InnerContext, object, {
    user: {
        avatar: string;
        organization: {
            id: number | null;
            isOrgAdmin: boolean;
            metadata: {
                defaultConferencingApp?: {
                    appSlug?: string | undefined;
                    appLink?: string | undefined;
                } | undefined;
                requestedSlug?: string | null | undefined;
                paymentId?: string | undefined;
                subscriptionId?: string | null | undefined;
                subscriptionItemId?: string | null | undefined;
                orgSeats?: number | null | undefined;
                orgPricePerSeat?: number | null | undefined;
                migratedToOrgFrom?: {
                    teamSlug?: string | null | undefined;
                    lastMigrationTime?: string | undefined;
                    reverted?: boolean | undefined;
                    lastRevertTime?: string | undefined;
                } | undefined;
                billingPeriod?: import("@calcom/prisma/zod-utils").BillingPeriod | undefined;
            } | null;
            requestedSlug: string | null;
            name?: string | undefined;
            organizationSettings?: {
                allowSEOIndexing: boolean;
                lockEventTypeCreationForUsers: boolean;
            } | null | undefined;
            hideBranding?: boolean | undefined;
            slug?: string | null | undefined;
            logoUrl?: string | null | undefined;
            isPrivate?: boolean | undefined;
            bannerUrl?: string | null | undefined;
            isPlatform?: boolean | undefined;
        };
        organizationId: number | null;
        id: number;
        email: string;
        username: string | null;
        locale: string;
        defaultBookerLayouts: {
            enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
            defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
        } | null;
        name: string | null;
        role: import(".prisma/client").$Enums.UserPermissionRole;
        metadata: import(".prisma/client").Prisma.JsonValue;
        startTime: number;
        endTime: number;
        destinationCalendar: {
            id: number;
            userId: number | null;
            eventTypeId: number | null;
            createdAt: Date | null;
            updatedAt: Date | null;
            integration: string;
            externalId: string;
            primaryEmail: string | null;
            credentialId: number | null;
            delegationCredentialId: string | null;
            domainWideDelegationCredentialId: string | null;
        } | null;
        movedToProfileId: number | null;
        emailVerified: Date | null;
        bio: string | null;
        avatarUrl: string | null;
        timeZone: string;
        weekStart: string;
        bufferTime: number;
        hideBranding: boolean;
        theme: string | null;
        appTheme: string | null;
        createdDate: Date;
        trialEndsAt: Date | null;
        defaultScheduleId: number | null;
        completedOnboarding: boolean;
        timeFormat: number | null;
        twoFactorEnabled: boolean;
        identityProvider: import(".prisma/client").$Enums.IdentityProvider;
        identityProviderId: string | null;
        brandColor: string | null;
        darkBrandColor: string | null;
        allowDynamicBooking: boolean | null;
        allowSEOIndexing: boolean | null;
        receiveMonthlyDigestEmail: boolean | null;
        disableImpersonation: boolean;
        profiles: {
            id: number;
            userId: number;
            uid: string;
            createdAt: Date;
            updatedAt: Date;
            username: string;
            organizationId: number;
        }[];
        allSelectedCalendars: {
            eventTypeId: number | null;
            updatedAt: Date | null;
            integration: string;
            externalId: string;
            googleChannelId: string | null;
        }[];
        userLevelSelectedCalendars: {
            eventTypeId: number | null;
            updatedAt: Date | null;
            integration: string;
            externalId: string;
            googleChannelId: string | null;
        }[];
        profile: import("@calcom/types/UserProfile").UserAsPersonalProfile;
    } | {
        avatar: string;
        organization: {
            id: number | null;
            isOrgAdmin: boolean;
            metadata: {
                defaultConferencingApp?: {
                    appSlug?: string | undefined;
                    appLink?: string | undefined;
                } | undefined;
                requestedSlug?: string | null | undefined;
                paymentId?: string | undefined;
                subscriptionId?: string | null | undefined;
                subscriptionItemId?: string | null | undefined;
                orgSeats?: number | null | undefined;
                orgPricePerSeat?: number | null | undefined;
                migratedToOrgFrom?: {
                    teamSlug?: string | null | undefined;
                    lastMigrationTime?: string | undefined;
                    reverted?: boolean | undefined;
                    lastRevertTime?: string | undefined;
                } | undefined;
                billingPeriod?: import("@calcom/prisma/zod-utils").BillingPeriod | undefined;
            } | null;
            requestedSlug: string | null;
            name?: string | undefined;
            organizationSettings?: {
                allowSEOIndexing: boolean;
                lockEventTypeCreationForUsers: boolean;
            } | null | undefined;
            hideBranding?: boolean | undefined;
            slug?: string | null | undefined;
            logoUrl?: string | null | undefined;
            isPrivate?: boolean | undefined;
            bannerUrl?: string | null | undefined;
            isPlatform?: boolean | undefined;
        };
        organizationId: number | null;
        id: number;
        email: string;
        username: string | null;
        locale: string;
        defaultBookerLayouts: {
            enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
            defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
        } | null;
        name: string | null;
        role: import(".prisma/client").$Enums.UserPermissionRole;
        metadata: import(".prisma/client").Prisma.JsonValue;
        startTime: number;
        endTime: number;
        destinationCalendar: {
            id: number;
            userId: number | null;
            eventTypeId: number | null;
            createdAt: Date | null;
            updatedAt: Date | null;
            integration: string;
            externalId: string;
            primaryEmail: string | null;
            credentialId: number | null;
            delegationCredentialId: string | null;
            domainWideDelegationCredentialId: string | null;
        } | null;
        movedToProfileId: number | null;
        emailVerified: Date | null;
        bio: string | null;
        avatarUrl: string | null;
        timeZone: string;
        weekStart: string;
        bufferTime: number;
        hideBranding: boolean;
        theme: string | null;
        appTheme: string | null;
        createdDate: Date;
        trialEndsAt: Date | null;
        defaultScheduleId: number | null;
        completedOnboarding: boolean;
        timeFormat: number | null;
        twoFactorEnabled: boolean;
        identityProvider: import(".prisma/client").$Enums.IdentityProvider;
        identityProviderId: string | null;
        brandColor: string | null;
        darkBrandColor: string | null;
        allowDynamicBooking: boolean | null;
        allowSEOIndexing: boolean | null;
        receiveMonthlyDigestEmail: boolean | null;
        disableImpersonation: boolean;
        profiles: {
            id: number;
            userId: number;
            uid: string;
            createdAt: Date;
            updatedAt: Date;
            username: string;
            organizationId: number;
        }[];
        allSelectedCalendars: {
            eventTypeId: number | null;
            updatedAt: Date | null;
            integration: string;
            externalId: string;
            googleChannelId: string | null;
        }[];
        userLevelSelectedCalendars: {
            eventTypeId: number | null;
            updatedAt: Date | null;
            integration: string;
            externalId: string;
            googleChannelId: string | null;
        }[];
        profile: {
            name: string | null;
            avatarUrl: string | null;
            startTime: number;
            endTime: number;
            bufferTime: number;
            username: string | null;
            upId: string;
            id: null;
            organizationId: null;
            organization: null;
        } | {
            name: string | null;
            avatarUrl: string | null;
            startTime: number;
            endTime: number;
            bufferTime: number;
            user: {
                name: string | null;
                id: number;
                locale: string | null;
                startTime: number;
                endTime: number;
                email: string;
                username: string | null;
                avatarUrl: string | null;
                bufferTime: number;
                defaultScheduleId: number | null;
                isPlatformManaged: boolean;
            };
            organization: {
                name: string;
                id: number;
                metadata: import(".prisma/client").Prisma.JsonValue;
                organizationSettings: {
                    allowSEOIndexing: boolean;
                    lockEventTypeCreationForUsers: boolean;
                } | null;
                hideBranding: boolean;
                slug: string | null;
                logoUrl: string | null;
                isPrivate: boolean;
                bannerUrl: string | null;
                isPlatform: boolean;
                members: {
                    id: number;
                    role: import(".prisma/client").$Enums.MembershipRole;
                    userId: number;
                    disableImpersonation: boolean;
                    teamId: number;
                    accepted: boolean;
                }[];
            } & Omit<Pick<{
                id: number;
                name: string;
                slug: string | null;
                logoUrl: string | null;
                calVideoLogo: string | null;
                appLogo: string | null;
                appIconLogo: string | null;
                bio: string | null;
                hideBranding: boolean;
                hideTeamProfileLink: boolean;
                isPrivate: boolean;
                hideBookATeamMember: boolean;
                createdAt: Date;
                metadata: import(".prisma/client").Prisma.JsonValue | null;
                theme: string | null;
                rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
                rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
                brandColor: string | null;
                darkBrandColor: string | null;
                bannerUrl: string | null;
                parentId: number | null;
                timeFormat: number | null;
                timeZone: string;
                weekStart: string;
                isOrganization: boolean;
                pendingPayment: boolean;
                isPlatform: boolean;
                createdByOAuthClientId: string | null;
                smsLockState: import(".prisma/client").$Enums.SMSLockState;
                smsLockReviewedByAdmin: boolean;
                bookingLimits: import(".prisma/client").Prisma.JsonValue | null;
                includeManagedEventsInLimits: boolean;
            }, "name" | "id" | "metadata" | "hideBranding" | "slug" | "logoUrl" | "bannerUrl" | "isPlatform">, "metadata"> & {
                requestedSlug: string | null;
                metadata: {
                    requestedSlug: string | null;
                    defaultConferencingApp?: {
                        appSlug?: string | undefined;
                        appLink?: string | undefined;
                    } | undefined;
                    paymentId?: string | undefined;
                    subscriptionId?: string | null | undefined;
                    subscriptionItemId?: string | null | undefined;
                    orgSeats?: number | null | undefined;
                    orgPricePerSeat?: number | null | undefined;
                    migratedToOrgFrom?: {
                        teamSlug?: string | null | undefined;
                        lastMigrationTime?: string | undefined;
                        reverted?: boolean | undefined;
                        lastRevertTime?: string | undefined;
                    } | undefined;
                    billingPeriod?: import("@calcom/prisma/zod-utils").BillingPeriod | undefined;
                };
            };
            movedFromUser: {
                id: number;
            } | null;
            id: number;
            userId: number;
            uid: string;
            createdAt: Date & string;
            updatedAt: Date & string;
            username: string;
            organizationId: number;
            upId: string;
        };
    };
    booking: {
        user: ({
            destinationCalendar: {
                id: number;
                userId: number | null;
                eventTypeId: number | null;
                createdAt: Date | null;
                updatedAt: Date | null;
                integration: string;
                externalId: string;
                primaryEmail: string | null;
                credentialId: number | null;
                delegationCredentialId: string | null;
                domainWideDelegationCredentialId: string | null;
            } | null;
            credentials: {
                key: import(".prisma/client").Prisma.JsonValue;
                id: number;
                type: string;
                userId: number | null;
                delegationCredentialId: string | null;
                teamId: number | null;
                subscriptionId: string | null;
                billingCycleStart: number | null;
                appId: string | null;
                paymentStatus: string | null;
                invalid: boolean | null;
            }[];
        } & {
            name: string | null;
            id: number;
            role: import(".prisma/client").$Enums.UserPermissionRole;
            metadata: import(".prisma/client").Prisma.JsonValue;
            locale: string | null;
            startTime: number;
            endTime: number;
            creationSource: import(".prisma/client").$Enums.CreationSource | null;
            email: string;
            movedToProfileId: number | null;
            username: string | null;
            emailVerified: Date | null;
            bio: string | null;
            avatarUrl: string | null;
            timeZone: string;
            weekStart: string;
            bufferTime: number;
            hideBranding: boolean;
            theme: string | null;
            appTheme: string | null;
            createdDate: Date;
            trialEndsAt: Date | null;
            lastActiveAt: Date | null;
            defaultScheduleId: number | null;
            completedOnboarding: boolean;
            timeFormat: number | null;
            twoFactorSecret: string | null;
            twoFactorEnabled: boolean;
            backupCodes: string | null;
            identityProvider: import(".prisma/client").$Enums.IdentityProvider;
            identityProviderId: string | null;
            invitedTo: number | null;
            brandColor: string | null;
            darkBrandColor: string | null;
            allowDynamicBooking: boolean | null;
            allowSEOIndexing: boolean | null;
            receiveMonthlyDigestEmail: boolean | null;
            verified: boolean | null;
            disableImpersonation: boolean;
            organizationId: number | null;
            locked: boolean;
            isPlatformManaged: boolean;
            smsLockState: import(".prisma/client").$Enums.SMSLockState;
            smsLockReviewedByAdmin: boolean;
            referralLinkId: string | null;
            whitelistWorkflows: boolean;
        }) | null;
        eventType: ({
            team: {
                name: string;
                id: number;
                parentId: number | null;
            } | null;
        } & {
            id: number;
            length: number;
            title: string;
            metadata: import(".prisma/client").Prisma.JsonValue;
            description: string | null;
            userId: number | null;
            timeZone: string | null;
            slug: string;
            parentId: number | null;
            bookingLimits: import(".prisma/client").Prisma.JsonValue;
            teamId: number | null;
            hidden: boolean;
            interfaceLanguage: string | null;
            position: number;
            locations: import(".prisma/client").Prisma.JsonValue;
            offsetStart: number;
            profileId: number | null;
            useEventLevelSelectedCalendars: boolean;
            eventName: string | null;
            bookingFields: import(".prisma/client").Prisma.JsonValue;
            periodType: import(".prisma/client").$Enums.PeriodType;
            periodStartDate: Date | null;
            periodEndDate: Date | null;
            periodDays: number | null;
            periodCountCalendarDays: boolean | null;
            lockTimeZoneToggleOnBookingPage: boolean;
            lockedTimeZone: string | null;
            requiresConfirmation: boolean;
            requiresConfirmationWillBlockSlot: boolean;
            requiresConfirmationForFreeEmail: boolean;
            requiresBookerEmailVerification: boolean;
            canSendCalVideoTranscriptionEmails: boolean;
            autoTranslateDescriptionEnabled: boolean;
            recurringEvent: import(".prisma/client").Prisma.JsonValue;
            disableGuests: boolean;
            hideCalendarNotes: boolean;
            hideCalendarEventDetails: boolean;
            minimumBookingNotice: number;
            beforeEventBuffer: number;
            afterEventBuffer: number;
            seatsPerTimeSlot: number | null;
            onlyShowFirstAvailableSlot: boolean;
            disableCancelling: boolean | null;
            disableRescheduling: boolean | null;
            seatsShowAttendees: boolean | null;
            seatsShowAvailabilityCount: boolean | null;
            schedulingType: import(".prisma/client").$Enums.SchedulingType | null;
            scheduleId: number | null;
            allowReschedulingCancelledBookings: boolean | null;
            price: number;
            currency: string;
            slotInterval: number | null;
            successRedirectUrl: string | null;
            forwardParamsSuccessRedirect: boolean | null;
            durationLimits: import(".prisma/client").Prisma.JsonValue;
            isInstantEvent: boolean;
            instantMeetingExpiryTimeOffsetInSeconds: number;
            instantMeetingScheduleId: number | null;
            instantMeetingParameters: string[];
            assignAllTeamMembers: boolean;
            assignRRMembersUsingSegment: boolean;
            rrSegmentQueryValue: import(".prisma/client").Prisma.JsonValue;
            useEventTypeDestinationCalendarEmail: boolean;
            isRRWeightsEnabled: boolean;
            maxLeadThreshold: number | null;
            includeNoShowInRRCalculation: boolean;
            allowReschedulingPastBookings: boolean;
            hideOrganizerEmail: boolean;
            maxActiveBookingsPerBooker: number | null;
            maxActiveBookingPerBookerOfferReschedule: boolean;
            customReplyToEmail: string | null;
            eventTypeColor: import(".prisma/client").Prisma.JsonValue;
            rescheduleWithSameRoundRobinHost: boolean;
            secondaryEmailId: number | null;
            useBookerTimezone: boolean;
            restrictionScheduleId: number | null;
            bookingRequiresAuthentication: boolean;
        }) | null;
        destinationCalendar: {
            id: number;
            userId: number | null;
            eventTypeId: number | null;
            createdAt: Date | null;
            updatedAt: Date | null;
            integration: string;
            externalId: string;
            primaryEmail: string | null;
            credentialId: number | null;
            delegationCredentialId: string | null;
            domainWideDelegationCredentialId: string | null;
        } | null;
        references: {
            id: number;
            type: string;
            uid: string;
            credentialId: number | null;
            delegationCredentialId: string | null;
            domainWideDelegationCredentialId: string | null;
            deleted: boolean | null;
            bookingId: number | null;
            thirdPartyRecurringEventId: string | null;
            meetingId: string | null;
            meetingPassword: string | null;
            meetingUrl: string | null;
            externalCalendarId: string | null;
        }[];
        attendees: {
            name: string;
            id: number;
            locale: string | null;
            email: string;
            timeZone: string;
            bookingId: number | null;
            phoneNumber: string | null;
            noShow: boolean | null;
        }[];
    } & {
        id: number;
        title: string;
        metadata: import(".prisma/client").Prisma.JsonValue;
        status: import(".prisma/client").$Enums.BookingStatus;
        description: string | null;
        startTime: Date;
        endTime: Date;
        userId: number | null;
        uid: string;
        idempotencyKey: string | null;
        userPrimaryEmail: string | null;
        eventTypeId: number | null;
        customInputs: import(".prisma/client").Prisma.JsonValue;
        responses: import(".prisma/client").Prisma.JsonValue;
        location: string | null;
        createdAt: Date;
        updatedAt: Date | null;
        paid: boolean;
        destinationCalendarId: number | null;
        cancellationReason: string | null;
        rejectionReason: string | null;
        reassignReason: string | null;
        reassignById: number | null;
        dynamicEventSlugRef: string | null;
        dynamicGroupSlugRef: string | null;
        rescheduled: boolean | null;
        fromReschedule: string | null;
        recurringEventId: string | null;
        smsReminderNumber: string | null;
        scheduledJobs: string[];
        isRecorded: boolean;
        iCalUID: string | null;
        iCalSequence: number;
        rating: number | null;
        ratingFeedback: string | null;
        noShowHost: boolean | null;
        oneTimePassword: string | null;
        cancelledBy: string | null;
        rescheduledBy: string | null;
        creationSource: import(".prisma/client").$Enums.CreationSource | null;
    };
    session: {
        upId: string;
        hasValidLicense: boolean;
        profileId?: number | null;
        user: import("next-auth").User;
        expires: import("next-auth").ISODateString;
    };
}, {
    bookingId: number;
}, {
    bookingId: number;
}, typeof import("@trpc/server/unstable-core-do-not-import").unsetMarker, typeof import("@trpc/server/unstable-core-do-not-import").unsetMarker>;
export type BookingsProcedureContext = {
    booking: Booking & {
        eventType: (EventType & {
            team?: {
                id: number;
                name: string;
                parentId?: number | null;
            } | null;
        }) | null;
        destinationCalendar: DestinationCalendar | null;
        user: (User & {
            destinationCalendar: DestinationCalendar | null;
            credentials: Credential[];
        }) | null;
        references: BookingReference[];
        attendees: Attendee[];
    };
};
