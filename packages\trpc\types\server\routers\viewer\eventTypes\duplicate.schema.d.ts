import type { z } from "zod";
export declare const ZDuplicateInputSchema: z.ZodObject<{
    id: z.ZodNumber;
    slug: z.ZodString;
    title: z.ZodString;
    description: z.ZodString;
    length: z.ZodNumber;
    teamId: z.<PERSON><z.ZodNullable<z.ZodNumber>>;
}, "strict", z.Z<PERSON>, {
    id: number;
    length: number;
    title: string;
    description: string;
    slug: string;
    teamId?: number | null | undefined;
}, {
    id: number;
    length: number;
    title: string;
    description: string;
    slug: string;
    teamId?: number | null | undefined;
}>;
export type TDuplicateInputSchema = z.infer<typeof ZDuplicateInputSchema>;
