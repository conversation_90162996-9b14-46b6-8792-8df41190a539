import type { TrpcSessionUser } from "../../../types";
import type { TPublishInputSchema } from "./publish.schema";
type PublishOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TPublishInputSchema;
};
export declare const publishHandler: ({ ctx, input }: PublishOptions) => Promise<{
    url: string;
    status: "REQUIRES_PAYMENT" | "REQUIRES_UPGRADE" | "SUCCESS";
    message?: undefined;
} | {
    url: string;
    status?: undefined;
    message?: undefined;
} | {
    url: string;
    message: string;
    status?: undefined;
}>;
export default publishHandler;
