import type { z } from "zod";
export declare const ZReserveSlotInputSchema: z.ZodEffects<z.ZodObject<{
    eventTypeId: z.ZodNumber;
    slotUtcStartDate: z.ZodString;
    slotUtcEndDate: z.ZodString;
    _isDryRun: z.<PERSON>od<PERSON>ptional<z.ZodBoolean>;
}, "strip", z.ZodType<PERSON>ny, {
    eventTypeId: number;
    slotUtcStartDate: string;
    slotUtcEndDate: string;
    _isDryRun?: boolean | undefined;
}, {
    eventTypeId: number;
    slotUtcStartDate: string;
    slotUtcEndDate: string;
    _isDryRun?: boolean | undefined;
}>, {
    eventTypeId: number;
    slotUtcStartDate: string;
    slotUtcEndDate: string;
    _isDryRun?: boolean | undefined;
}, {
    eventTypeId: number;
    slotUtcStartDate: string;
    slotUtcEndDate: string;
    _isDryRun?: boolean | undefined;
}>;
export type TReserveSlotInputSchema = z.infer<typeof ZReserveSlotInputSchema>;
