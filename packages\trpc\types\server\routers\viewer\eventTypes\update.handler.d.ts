import { Prisma } from "@prisma/client";
import type { NextApiResponse, GetServerSidePropsContext } from "next";
import type { PrismaClient } from "@calcom/prisma";
import type { TrpcSessionUser } from "../../../types";
import type { TUpdateInputSchema } from "./update.schema";
type SessionUser = NonNullable<TrpcSessionUser>;
type User = {
    id: SessionUser["id"];
    username: SessionUser["username"];
    profile: {
        id: SessionUser["profile"]["id"] | null;
    };
    userLevelSelectedCalendars: SessionUser["userLevelSelectedCalendars"];
    organizationId: number | null;
    email: SessionUser["email"];
    locale: string;
};
type UpdateOptions = {
    ctx: {
        user: User;
        res?: NextApiResponse | GetServerSidePropsContext["res"];
        prisma: PrismaClient;
    };
    input: TUpdateInputSchema;
};
export type UpdateEventTypeReturn = Awaited<ReturnType<typeof updateHandler>>;
export declare const updateHandler: ({ ctx, input }: UpdateOptions) => Promise<{
    eventType: {
        children: {
            userId: number | null;
        }[];
        title: string;
        description: string | null;
        calVideoSettings: {
            disableRecordingForOrganizer: boolean;
            disableRecordingForGuests: boolean;
            enableAutomaticTranscription: boolean;
            enableAutomaticRecordingForOrganizer: boolean;
            redirectUrlOnExit: string | null;
            disableTranscriptionForGuests: boolean;
            disableTranscriptionForOrganizer: boolean;
        } | null;
        team: {
            name: string;
            id: number;
            slug: string | null;
            rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
            parentId: number | null;
            members: {
                user: {
                    name: string | null;
                    id: number;
                    email: string;
                    eventTypes: {
                        slug: string;
                    }[];
                };
                role: import(".prisma/client").$Enums.MembershipRole;
                accepted: boolean;
            }[];
            parent: {
                slug: string | null;
            } | null;
        } | null;
        workflows: {
            workflowId: number;
        }[];
        hosts: {
            userId: number;
            weight: number | null;
            isFixed: boolean;
            priority: number | null;
        }[];
        locations: Prisma.JsonValue;
        recurringEvent: Prisma.JsonValue;
        seatsPerTimeSlot: number | null;
        isRRWeightsEnabled: boolean;
        maxActiveBookingsPerBooker: number | null;
        aiPhoneCallConfig: {
            enabled: boolean;
            generalPrompt: string | null;
            beginMessage: string | null;
            llmId: string | null;
        } | null;
        fieldTranslations: {
            field: import(".prisma/client").$Enums.EventTypeAutoTranslatedField;
        }[];
        hostGroups: {
            name: string;
            id: string;
        }[];
    };
}>;
export {};
