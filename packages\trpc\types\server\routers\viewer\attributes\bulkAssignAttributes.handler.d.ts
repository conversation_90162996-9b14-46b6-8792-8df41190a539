import type { TrpcSessionUser } from "../../../types";
import type { ZBulkAssignAttributes } from "./bulkAssignAttributes.schema";
type GetOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: ZBulkAssignAttributes;
};
declare const bulkAssignAttributesHandler: ({ input, ctx }: GetOptions) => Promise<{
    success: boolean;
    message: string;
    results: {
        userId: number;
        success: boolean;
        message?: string;
    }[];
}>;
export default bulkAssignAttributesHandler;
