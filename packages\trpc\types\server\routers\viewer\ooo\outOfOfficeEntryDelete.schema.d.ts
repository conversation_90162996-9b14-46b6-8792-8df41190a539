import { z } from "zod";
export declare const ZOutOfOfficeDelete: z.ZodObject<{
    outOfOfficeUid: z.ZodString;
    userId: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
}, "strip", z.<PERSON>ny, {
    outOfOfficeUid: string;
    userId?: number | null | undefined;
}, {
    outOfOfficeUid: string;
    userId?: number | null | undefined;
}>;
export type TOutOfOfficeDelete = z.infer<typeof ZOutOfOfficeDelete>;
