import type { TrpcSessionUser } from "@calcom/trpc/server/types";
import type { TSkipTeamTrialsInputSchema } from "./skipTeamTrials.schema";
type SkipTeamTrialsOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TSkipTeamTrialsInputSchema;
};
export declare const skipTeamTrialsHandler: ({ ctx }: SkipTeamTrialsOptions) => Promise<{
    success: boolean;
    error?: undefined;
} | {
    success: boolean;
    error: string;
}>;
export default skipTeamTrialsHandler;
