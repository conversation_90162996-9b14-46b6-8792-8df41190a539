import type { TUserEmailVerificationRequiredSchema } from "./checkIfUserEmailVerificationRequired.schema";
export declare const userWithEmailHandler: ({ input }: {
    input: TUserEmailVerificationRequiredSchema;
}) => Promise<boolean>;
export declare const checkEmailVerificationRequired: ({ userSessionEmail, email, }: {
    userSessionEmail?: string;
    email: string;
}) => Promise<boolean>;
export default userWithEmailHandler;
