import type { TrpcSessionUser } from "../../../types";
import type { TGetListSchema } from "./list.schema";
type ListOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TGetListSchema;
};
export declare const listHandler: ({ ctx, input }: ListOptions) => Promise<{
    metadata: {
        defaultConferencingApp?: {
            appSlug?: string | undefined;
            appLink?: string | undefined;
        } | undefined;
        requestedSlug?: string | null | undefined;
        paymentId?: string | undefined;
        subscriptionId?: string | null | undefined;
        subscriptionItemId?: string | null | undefined;
        orgSeats?: number | null | undefined;
        orgPricePerSeat?: number | null | undefined;
        migratedToOrgFrom?: {
            teamSlug?: string | null | undefined;
            lastMigrationTime?: string | undefined;
            reverted?: boolean | undefined;
            lastRevertTime?: string | undefined;
        } | undefined;
        billingPeriod?: import("@calcom/prisma/zod-utils").BillingPeriod | undefined;
    } | null;
    inviteToken: {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        teamId: number | null;
        secondaryEmailId: number | null;
        identifier: string;
        expires: Date;
        token: string;
        expiresInDays: number | null;
    } | undefined;
    name: string;
    id: number;
    slug: string | null;
    logoUrl: string | null;
    parentId: number | null;
    isOrganization: boolean;
    parent: {
        name: string;
        id: number;
        metadata: import(".prisma/client").Prisma.JsonValue;
        createdAt: Date;
        bio: string | null;
        timeZone: string;
        weekStart: string;
        hideBranding: boolean;
        theme: string | null;
        timeFormat: number | null;
        brandColor: string | null;
        darkBrandColor: string | null;
        smsLockState: import(".prisma/client").$Enums.SMSLockState;
        smsLockReviewedByAdmin: boolean;
        slug: string | null;
        logoUrl: string | null;
        calVideoLogo: string | null;
        appLogo: string | null;
        appIconLogo: string | null;
        hideTeamProfileLink: boolean;
        isPrivate: boolean;
        hideBookATeamMember: boolean;
        rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
        rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
        bannerUrl: string | null;
        parentId: number | null;
        isOrganization: boolean;
        pendingPayment: boolean;
        isPlatform: boolean;
        createdByOAuthClientId: string | null;
        bookingLimits: import(".prisma/client").Prisma.JsonValue;
        includeManagedEventsInLimits: boolean;
    } | null;
    role: import(".prisma/client").$Enums.MembershipRole;
    accepted: boolean;
}[]>;
export default listHandler;
