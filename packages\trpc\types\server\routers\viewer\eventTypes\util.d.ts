import type { EventTypeRepository } from "@calcom/lib/server/repository/eventTypeRepository";
import { PeriodType } from "@calcom/prisma/enums";
import type { CustomInputSchema } from "@calcom/prisma/zod-utils";
import type { TUpdateInputSchema } from "./types";
type EventType = Awaited<ReturnType<EventTypeRepository["findAllByUpId"]>>[number];
export declare const eventOwnerProcedure: import("@trpc/server/unstable-core-do-not-import").ProcedureBuilder<import("../../../createContext").InnerContext, object, {
    user: {
        avatar: string;
        organization: {
            id: number | null;
            isOrgAdmin: boolean;
            metadata: {
                defaultConferencingApp?: {
                    appSlug?: string | undefined;
                    appLink?: string | undefined;
                } | undefined;
                requestedSlug?: string | null | undefined;
                paymentId?: string | undefined;
                subscriptionId?: string | null | undefined;
                subscriptionItemId?: string | null | undefined;
                orgSeats?: number | null | undefined;
                orgPricePerSeat?: number | null | undefined;
                migratedToOrgFrom?: {
                    teamSlug?: string | null | undefined;
                    lastMigrationTime?: string | undefined;
                    reverted?: boolean | undefined;
                    lastRevertTime?: string | undefined;
                } | undefined;
                billingPeriod?: import("@calcom/prisma/zod-utils").BillingPeriod | undefined;
            } | null;
            requestedSlug: string | null;
            name?: string | undefined;
            organizationSettings?: {
                allowSEOIndexing: boolean;
                lockEventTypeCreationForUsers: boolean;
            } | null | undefined;
            hideBranding?: boolean | undefined;
            slug?: string | null | undefined;
            logoUrl?: string | null | undefined;
            isPrivate?: boolean | undefined;
            bannerUrl?: string | null | undefined;
            isPlatform?: boolean | undefined;
        };
        organizationId: number | null;
        id: number;
        email: string;
        username: string | null;
        locale: string;
        defaultBookerLayouts: {
            enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
            defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
        } | null;
        name: string | null;
        role: import(".prisma/client").$Enums.UserPermissionRole;
        metadata: import(".prisma/client").Prisma.JsonValue;
        startTime: number;
        endTime: number;
        destinationCalendar: {
            id: number;
            userId: number | null;
            eventTypeId: number | null;
            createdAt: Date | null;
            updatedAt: Date | null;
            integration: string;
            externalId: string;
            primaryEmail: string | null;
            credentialId: number | null;
            delegationCredentialId: string | null;
            domainWideDelegationCredentialId: string | null;
        } | null;
        movedToProfileId: number | null;
        emailVerified: Date | null;
        bio: string | null;
        avatarUrl: string | null;
        timeZone: string;
        weekStart: string;
        bufferTime: number;
        hideBranding: boolean;
        theme: string | null;
        appTheme: string | null;
        createdDate: Date;
        trialEndsAt: Date | null;
        defaultScheduleId: number | null;
        completedOnboarding: boolean;
        timeFormat: number | null;
        twoFactorEnabled: boolean;
        identityProvider: import(".prisma/client").$Enums.IdentityProvider;
        identityProviderId: string | null;
        brandColor: string | null;
        darkBrandColor: string | null;
        allowDynamicBooking: boolean | null;
        allowSEOIndexing: boolean | null;
        receiveMonthlyDigestEmail: boolean | null;
        disableImpersonation: boolean;
        profiles: {
            id: number;
            userId: number;
            uid: string;
            createdAt: Date;
            updatedAt: Date;
            username: string;
            organizationId: number;
        }[];
        allSelectedCalendars: {
            eventTypeId: number | null;
            updatedAt: Date | null;
            integration: string;
            externalId: string;
            googleChannelId: string | null;
        }[];
        userLevelSelectedCalendars: {
            eventTypeId: number | null;
            updatedAt: Date | null;
            integration: string;
            externalId: string;
            googleChannelId: string | null;
        }[];
        profile: import("@calcom/types/UserProfile").UserAsPersonalProfile;
    } | {
        avatar: string;
        organization: {
            id: number | null;
            isOrgAdmin: boolean;
            metadata: {
                defaultConferencingApp?: {
                    appSlug?: string | undefined;
                    appLink?: string | undefined;
                } | undefined;
                requestedSlug?: string | null | undefined;
                paymentId?: string | undefined;
                subscriptionId?: string | null | undefined;
                subscriptionItemId?: string | null | undefined;
                orgSeats?: number | null | undefined;
                orgPricePerSeat?: number | null | undefined;
                migratedToOrgFrom?: {
                    teamSlug?: string | null | undefined;
                    lastMigrationTime?: string | undefined;
                    reverted?: boolean | undefined;
                    lastRevertTime?: string | undefined;
                } | undefined;
                billingPeriod?: import("@calcom/prisma/zod-utils").BillingPeriod | undefined;
            } | null;
            requestedSlug: string | null;
            name?: string | undefined;
            organizationSettings?: {
                allowSEOIndexing: boolean;
                lockEventTypeCreationForUsers: boolean;
            } | null | undefined;
            hideBranding?: boolean | undefined;
            slug?: string | null | undefined;
            logoUrl?: string | null | undefined;
            isPrivate?: boolean | undefined;
            bannerUrl?: string | null | undefined;
            isPlatform?: boolean | undefined;
        };
        organizationId: number | null;
        id: number;
        email: string;
        username: string | null;
        locale: string;
        defaultBookerLayouts: {
            enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
            defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
        } | null;
        name: string | null;
        role: import(".prisma/client").$Enums.UserPermissionRole;
        metadata: import(".prisma/client").Prisma.JsonValue;
        startTime: number;
        endTime: number;
        destinationCalendar: {
            id: number;
            userId: number | null;
            eventTypeId: number | null;
            createdAt: Date | null;
            updatedAt: Date | null;
            integration: string;
            externalId: string;
            primaryEmail: string | null;
            credentialId: number | null;
            delegationCredentialId: string | null;
            domainWideDelegationCredentialId: string | null;
        } | null;
        movedToProfileId: number | null;
        emailVerified: Date | null;
        bio: string | null;
        avatarUrl: string | null;
        timeZone: string;
        weekStart: string;
        bufferTime: number;
        hideBranding: boolean;
        theme: string | null;
        appTheme: string | null;
        createdDate: Date;
        trialEndsAt: Date | null;
        defaultScheduleId: number | null;
        completedOnboarding: boolean;
        timeFormat: number | null;
        twoFactorEnabled: boolean;
        identityProvider: import(".prisma/client").$Enums.IdentityProvider;
        identityProviderId: string | null;
        brandColor: string | null;
        darkBrandColor: string | null;
        allowDynamicBooking: boolean | null;
        allowSEOIndexing: boolean | null;
        receiveMonthlyDigestEmail: boolean | null;
        disableImpersonation: boolean;
        profiles: {
            id: number;
            userId: number;
            uid: string;
            createdAt: Date;
            updatedAt: Date;
            username: string;
            organizationId: number;
        }[];
        allSelectedCalendars: {
            eventTypeId: number | null;
            updatedAt: Date | null;
            integration: string;
            externalId: string;
            googleChannelId: string | null;
        }[];
        userLevelSelectedCalendars: {
            eventTypeId: number | null;
            updatedAt: Date | null;
            integration: string;
            externalId: string;
            googleChannelId: string | null;
        }[];
        profile: {
            name: string | null;
            avatarUrl: string | null;
            startTime: number;
            endTime: number;
            bufferTime: number;
            username: string | null;
            upId: string;
            id: null;
            organizationId: null;
            organization: null;
        } | {
            name: string | null;
            avatarUrl: string | null;
            startTime: number;
            endTime: number;
            bufferTime: number;
            user: {
                name: string | null;
                id: number;
                locale: string | null;
                startTime: number;
                endTime: number;
                email: string;
                username: string | null;
                avatarUrl: string | null;
                bufferTime: number;
                defaultScheduleId: number | null;
                isPlatformManaged: boolean;
            };
            organization: {
                name: string;
                id: number;
                metadata: import(".prisma/client").Prisma.JsonValue;
                organizationSettings: {
                    allowSEOIndexing: boolean;
                    lockEventTypeCreationForUsers: boolean;
                } | null;
                hideBranding: boolean;
                slug: string | null;
                logoUrl: string | null;
                isPrivate: boolean;
                bannerUrl: string | null;
                isPlatform: boolean;
                members: {
                    id: number;
                    role: import(".prisma/client").$Enums.MembershipRole;
                    userId: number;
                    disableImpersonation: boolean;
                    teamId: number;
                    accepted: boolean;
                }[];
            } & Omit<Pick<{
                id: number;
                name: string;
                slug: string | null;
                logoUrl: string | null;
                calVideoLogo: string | null;
                appLogo: string | null;
                appIconLogo: string | null;
                bio: string | null;
                hideBranding: boolean;
                hideTeamProfileLink: boolean;
                isPrivate: boolean;
                hideBookATeamMember: boolean;
                createdAt: Date;
                metadata: import(".prisma/client").Prisma.JsonValue | null;
                theme: string | null;
                rrResetInterval: import(".prisma/client").$Enums.RRResetInterval | null;
                rrTimestampBasis: import(".prisma/client").$Enums.RRTimestampBasis;
                brandColor: string | null;
                darkBrandColor: string | null;
                bannerUrl: string | null;
                parentId: number | null;
                timeFormat: number | null;
                timeZone: string;
                weekStart: string;
                isOrganization: boolean;
                pendingPayment: boolean;
                isPlatform: boolean;
                createdByOAuthClientId: string | null;
                smsLockState: import(".prisma/client").$Enums.SMSLockState;
                smsLockReviewedByAdmin: boolean;
                bookingLimits: import(".prisma/client").Prisma.JsonValue | null;
                includeManagedEventsInLimits: boolean;
            }, "name" | "id" | "metadata" | "hideBranding" | "slug" | "logoUrl" | "bannerUrl" | "isPlatform">, "metadata"> & {
                requestedSlug: string | null;
                metadata: {
                    requestedSlug: string | null;
                    defaultConferencingApp?: {
                        appSlug?: string | undefined;
                        appLink?: string | undefined;
                    } | undefined;
                    paymentId?: string | undefined;
                    subscriptionId?: string | null | undefined;
                    subscriptionItemId?: string | null | undefined;
                    orgSeats?: number | null | undefined;
                    orgPricePerSeat?: number | null | undefined;
                    migratedToOrgFrom?: {
                        teamSlug?: string | null | undefined;
                        lastMigrationTime?: string | undefined;
                        reverted?: boolean | undefined;
                        lastRevertTime?: string | undefined;
                    } | undefined;
                    billingPeriod?: import("@calcom/prisma/zod-utils").BillingPeriod | undefined;
                };
            };
            movedFromUser: {
                id: number;
            } | null;
            id: number;
            userId: number;
            uid: string;
            createdAt: Date & string;
            updatedAt: Date & string;
            username: string;
            organizationId: number;
            upId: string;
        };
    };
    session: {
        upId: string;
        hasValidLicense: boolean;
        profileId?: number | null;
        user: import("next-auth").User;
        expires: import("next-auth").ISODateString;
    };
}, {
    id?: number | undefined;
    eventTypeId?: number | undefined;
    users?: number[] | undefined;
}, {
    users: number[];
    id?: number | undefined;
    eventTypeId?: number | undefined;
}, typeof import("@trpc/server/unstable-core-do-not-import").unsetMarker, typeof import("@trpc/server/unstable-core-do-not-import").unsetMarker>;
export declare function isPeriodType(keyInput: string): keyInput is PeriodType;
export declare function handlePeriodType(periodType: string | undefined): PeriodType | undefined;
export declare function handleCustomInputs(customInputs: CustomInputSchema[], eventTypeId: number): {
    deleteMany: {
        eventTypeId: number;
        NOT: {
            id: {
                in: number[];
            };
        };
    };
    createMany: {
        data: {
            type: "TEXT" | "TEXTLONG" | "NUMBER" | "BOOL" | "RADIO" | "PHONE";
            label: string;
            required: boolean;
            placeholder: string;
            options: {
                type: string;
                label: string;
            }[] | undefined;
        }[];
    };
    update: {
        data: {
            type: "TEXT" | "TEXTLONG" | "NUMBER" | "BOOL" | "RADIO" | "PHONE";
            label: string;
            required: boolean;
            placeholder: string;
            options: {
                type: string;
                label: string;
            }[] | undefined;
        };
        where: {
            id: number;
        };
    }[];
};
export declare function ensureUniqueBookingFields(fields: TUpdateInputSchema["bookingFields"]): void;
export declare function ensureEmailOrPhoneNumberIsPresent(fields: TUpdateInputSchema["bookingFields"]): void;
export declare const mapEventType: (eventType: EventType) => Promise<{
    safeDescription: string | undefined;
    users: ({
        name: string | null;
        id: number;
        username: string | null;
        avatarUrl: string | null;
        timeZone: string;
    } & {
        nonProfileUsername: string | null;
        profile: import("@calcom/types/UserProfile").UserProfile;
    })[];
    metadata: {
        config?: {
            useHostSchedulesForTeamEvent?: boolean | undefined;
        } | undefined;
        smartContractAddress?: string | undefined;
        blockchainId?: number | undefined;
        multipleDuration?: number[] | undefined;
        giphyThankYouPage?: string | undefined;
        additionalNotesRequired?: boolean | undefined;
        disableSuccessPage?: boolean | undefined;
        disableStandardEmails?: {
            all?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
            confirmation?: {
                host?: boolean | undefined;
                attendee?: boolean | undefined;
            } | undefined;
        } | undefined;
        managedEventConfig?: {
            unlockedFields?: {
                users?: true | undefined;
                children?: true | undefined;
                length?: true | undefined;
                title?: true | undefined;
                metadata?: true | undefined;
                description?: true | undefined;
                userId?: true | undefined;
                calVideoSettings?: true | undefined;
                destinationCalendar?: true | undefined;
                profile?: true | undefined;
                team?: true | undefined;
                schedule?: true | undefined;
                availability?: true | undefined;
                hashedLink?: true | undefined;
                secondaryEmail?: true | undefined;
                customInputs?: true | undefined;
                timeZone?: true | undefined;
                bookings?: true | undefined;
                selectedCalendars?: true | undefined;
                webhooks?: true | undefined;
                workflows?: true | undefined;
                hosts?: true | undefined;
                slug?: true | undefined;
                parentId?: true | undefined;
                bookingLimits?: true | undefined;
                parent?: true | undefined;
                teamId?: true | undefined;
                hidden?: true | undefined;
                _count?: true | undefined;
                interfaceLanguage?: true | undefined;
                position?: true | undefined;
                locations?: true | undefined;
                offsetStart?: true | undefined;
                profileId?: true | undefined;
                useEventLevelSelectedCalendars?: true | undefined;
                eventName?: true | undefined;
                bookingFields?: true | undefined;
                periodType?: true | undefined;
                periodStartDate?: true | undefined;
                periodEndDate?: true | undefined;
                periodDays?: true | undefined;
                periodCountCalendarDays?: true | undefined;
                lockTimeZoneToggleOnBookingPage?: true | undefined;
                lockedTimeZone?: true | undefined;
                requiresConfirmation?: true | undefined;
                requiresConfirmationWillBlockSlot?: true | undefined;
                requiresConfirmationForFreeEmail?: true | undefined;
                requiresBookerEmailVerification?: true | undefined;
                canSendCalVideoTranscriptionEmails?: true | undefined;
                autoTranslateDescriptionEnabled?: true | undefined;
                recurringEvent?: true | undefined;
                disableGuests?: true | undefined;
                hideCalendarNotes?: true | undefined;
                hideCalendarEventDetails?: true | undefined;
                minimumBookingNotice?: true | undefined;
                beforeEventBuffer?: true | undefined;
                afterEventBuffer?: true | undefined;
                seatsPerTimeSlot?: true | undefined;
                onlyShowFirstAvailableSlot?: true | undefined;
                disableCancelling?: true | undefined;
                disableRescheduling?: true | undefined;
                seatsShowAttendees?: true | undefined;
                seatsShowAvailabilityCount?: true | undefined;
                schedulingType?: true | undefined;
                scheduleId?: true | undefined;
                allowReschedulingCancelledBookings?: true | undefined;
                price?: true | undefined;
                currency?: true | undefined;
                slotInterval?: true | undefined;
                successRedirectUrl?: true | undefined;
                forwardParamsSuccessRedirect?: true | undefined;
                durationLimits?: true | undefined;
                isInstantEvent?: true | undefined;
                instantMeetingExpiryTimeOffsetInSeconds?: true | undefined;
                instantMeetingScheduleId?: true | undefined;
                instantMeetingParameters?: true | undefined;
                assignAllTeamMembers?: true | undefined;
                assignRRMembersUsingSegment?: true | undefined;
                rrSegmentQueryValue?: true | undefined;
                useEventTypeDestinationCalendarEmail?: true | undefined;
                isRRWeightsEnabled?: true | undefined;
                maxLeadThreshold?: true | undefined;
                includeNoShowInRRCalculation?: true | undefined;
                allowReschedulingPastBookings?: true | undefined;
                hideOrganizerEmail?: true | undefined;
                maxActiveBookingsPerBooker?: true | undefined;
                maxActiveBookingPerBookerOfferReschedule?: true | undefined;
                customReplyToEmail?: true | undefined;
                eventTypeColor?: true | undefined;
                rescheduleWithSameRoundRobinHost?: true | undefined;
                secondaryEmailId?: true | undefined;
                useBookerTimezone?: true | undefined;
                restrictionScheduleId?: true | undefined;
                bookingRequiresAuthentication?: true | undefined;
                owner?: true | undefined;
                instantMeetingSchedule?: true | undefined;
                aiPhoneCallConfig?: true | undefined;
                fieldTranslations?: true | undefined;
                restrictionSchedule?: true | undefined;
                hostGroups?: true | undefined;
            } | undefined;
        } | undefined;
        requiresConfirmationThreshold?: {
            time: number;
            unit: "days" | "months" | "years" | "hours" | "minutes" | "milliseconds" | "seconds" | "dates";
        } | undefined;
        bookerLayouts?: {
            enabledLayouts: import("@calcom/prisma/zod-utils").BookerLayouts[];
            defaultLayout: import("@calcom/prisma/zod-utils").BookerLayouts;
        } | null | undefined;
        apps?: unknown;
    } | null;
    children: {
        users: ({
            name: string | null;
            id: number;
            username: string | null;
            avatarUrl: string | null;
            timeZone: string;
        } & {
            nonProfileUsername: string | null;
            profile: import("@calcom/types/UserProfile").UserProfile;
        })[];
        id: number;
        length: number;
        title: string;
        metadata: import(".prisma/client").Prisma.JsonValue;
        description: string | null;
        userId: number | null;
        timeZone: string | null;
        slug: string;
        parentId: number | null;
        bookingLimits: import(".prisma/client").Prisma.JsonValue;
        teamId: number | null;
        hidden: boolean;
        interfaceLanguage: string | null;
        position: number;
        locations: import(".prisma/client").Prisma.JsonValue;
        offsetStart: number;
        profileId: number | null;
        useEventLevelSelectedCalendars: boolean;
        eventName: string | null;
        bookingFields: import(".prisma/client").Prisma.JsonValue;
        periodType: import(".prisma/client").$Enums.PeriodType;
        periodStartDate: Date | null;
        periodEndDate: Date | null;
        periodDays: number | null;
        periodCountCalendarDays: boolean | null;
        lockTimeZoneToggleOnBookingPage: boolean;
        lockedTimeZone: string | null;
        requiresConfirmation: boolean;
        requiresConfirmationWillBlockSlot: boolean;
        requiresConfirmationForFreeEmail: boolean;
        requiresBookerEmailVerification: boolean;
        canSendCalVideoTranscriptionEmails: boolean;
        autoTranslateDescriptionEnabled: boolean;
        recurringEvent: import(".prisma/client").Prisma.JsonValue;
        disableGuests: boolean;
        hideCalendarNotes: boolean;
        hideCalendarEventDetails: boolean;
        minimumBookingNotice: number;
        beforeEventBuffer: number;
        afterEventBuffer: number;
        seatsPerTimeSlot: number | null;
        onlyShowFirstAvailableSlot: boolean;
        disableCancelling: boolean | null;
        disableRescheduling: boolean | null;
        seatsShowAttendees: boolean | null;
        seatsShowAvailabilityCount: boolean | null;
        schedulingType: import(".prisma/client").$Enums.SchedulingType | null;
        scheduleId: number | null;
        allowReschedulingCancelledBookings: boolean | null;
        price: number;
        currency: string;
        slotInterval: number | null;
        successRedirectUrl: string | null;
        forwardParamsSuccessRedirect: boolean | null;
        durationLimits: import(".prisma/client").Prisma.JsonValue;
        isInstantEvent: boolean;
        instantMeetingExpiryTimeOffsetInSeconds: number;
        instantMeetingScheduleId: number | null;
        instantMeetingParameters: string[];
        assignAllTeamMembers: boolean;
        assignRRMembersUsingSegment: boolean;
        rrSegmentQueryValue: import(".prisma/client").Prisma.JsonValue;
        useEventTypeDestinationCalendarEmail: boolean;
        isRRWeightsEnabled: boolean;
        maxLeadThreshold: number | null;
        includeNoShowInRRCalculation: boolean;
        allowReschedulingPastBookings: boolean;
        hideOrganizerEmail: boolean;
        maxActiveBookingsPerBooker: number | null;
        maxActiveBookingPerBookerOfferReschedule: boolean;
        customReplyToEmail: string | null;
        eventTypeColor: import(".prisma/client").Prisma.JsonValue;
        rescheduleWithSameRoundRobinHost: boolean;
        secondaryEmailId: number | null;
        useBookerTimezone: boolean;
        restrictionScheduleId: number | null;
        bookingRequiresAuthentication: boolean;
    }[];
    id: number;
    length: number;
    title: string;
    description: string | null;
    userId: number | null;
    team: {
        id: number;
        members: {
            user: {
                timeZone: string;
            };
        }[];
    } | null;
    hashedLink: {
        link: string;
        id: number;
        expiresAt: Date | null;
        maxUsageCount: number;
        usageCount: number;
    }[];
    timeZone: string | null;
    hosts: ({
        user: {
            name: string | null;
            id: number;
            username: string | null;
            avatarUrl: string | null;
            timeZone: string;
        };
    } & {
        userId: number;
        eventTypeId: number;
        createdAt: Date;
        scheduleId: number | null;
        weight: number | null;
        isFixed: boolean;
        priority: number | null;
        weightAdjustment: number | null;
        groupId: string | null;
        memberId: number | null;
    })[];
    slug: string;
    parentId: number | null;
    bookingLimits: import(".prisma/client").Prisma.JsonValue;
    teamId: number | null;
    hidden: boolean;
    interfaceLanguage: string | null;
    position: number;
    locations: import(".prisma/client").Prisma.JsonValue;
    offsetStart: number;
    profileId: number | null;
    useEventLevelSelectedCalendars: boolean;
    eventName: string | null;
    bookingFields: import(".prisma/client").Prisma.JsonValue;
    periodType: import(".prisma/client").$Enums.PeriodType;
    periodStartDate: Date | null;
    periodEndDate: Date | null;
    periodDays: number | null;
    periodCountCalendarDays: boolean | null;
    lockTimeZoneToggleOnBookingPage: boolean;
    lockedTimeZone: string | null;
    requiresConfirmation: boolean;
    requiresConfirmationWillBlockSlot: boolean;
    requiresConfirmationForFreeEmail: boolean;
    requiresBookerEmailVerification: boolean;
    canSendCalVideoTranscriptionEmails: boolean;
    autoTranslateDescriptionEnabled: boolean;
    recurringEvent: import(".prisma/client").Prisma.JsonValue;
    disableGuests: boolean;
    hideCalendarNotes: boolean;
    hideCalendarEventDetails: boolean;
    minimumBookingNotice: number;
    beforeEventBuffer: number;
    afterEventBuffer: number;
    seatsPerTimeSlot: number | null;
    onlyShowFirstAvailableSlot: boolean;
    disableCancelling: boolean | null;
    disableRescheduling: boolean | null;
    seatsShowAttendees: boolean | null;
    seatsShowAvailabilityCount: boolean | null;
    schedulingType: import(".prisma/client").$Enums.SchedulingType | null;
    scheduleId: number | null;
    allowReschedulingCancelledBookings: boolean | null;
    price: number;
    currency: string;
    slotInterval: number | null;
    successRedirectUrl: string | null;
    durationLimits: import(".prisma/client").Prisma.JsonValue;
    isInstantEvent: boolean;
    instantMeetingExpiryTimeOffsetInSeconds: number;
    instantMeetingScheduleId: number | null;
    instantMeetingParameters: string[];
    assignAllTeamMembers: boolean;
    assignRRMembersUsingSegment: boolean;
    rrSegmentQueryValue: import(".prisma/client").Prisma.JsonValue;
    useEventTypeDestinationCalendarEmail: boolean;
    isRRWeightsEnabled: boolean;
    maxLeadThreshold: number | null;
    allowReschedulingPastBookings: boolean;
    hideOrganizerEmail: boolean;
    customReplyToEmail: string | null;
    eventTypeColor: import(".prisma/client").Prisma.JsonValue;
    rescheduleWithSameRoundRobinHost: boolean;
    secondaryEmailId: number | null;
    useBookerTimezone: boolean;
    restrictionScheduleId: number | null;
    owner: {
        timeZone: string;
    } | null;
    instantMeetingSchedule: {
        name: string;
        id: number;
    } | null;
    aiPhoneCallConfig: {
        id: number;
        eventTypeId: number;
        enabled: boolean;
        templateType: string;
        schedulerName: string | null;
        generalPrompt: string | null;
        yourPhoneNumber: string;
        numberToCall: string;
        guestName: string | null;
        guestEmail: string | null;
        guestCompany: string | null;
        beginMessage: string | null;
        llmId: string | null;
    } | null;
}>;
export {};
