import type { TrpcSessionUser } from "../../../types";
type ListOptions = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
};
export declare const listHandler: ({ ctx }: ListOptions) => Promise<{
    id: string;
    note: string | null;
    userId: number;
    createdAt: Date;
    teamId: number | null;
    appId: string | null;
    expiresAt: Date | null;
    lastUsedAt: Date | null;
    hashedKey: string;
}[]>;
export {};
