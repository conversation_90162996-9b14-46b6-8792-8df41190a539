import type { TrpcSessionUser } from "../../../types";
import type { TAddMembersToTeams } from "./addMembersToTeams.schema";
type AddBulkTeamsHandler = {
    ctx: {
        user: NonNullable<TrpcSessionUser>;
    };
    input: TAddMembersToTeams;
};
export declare function addTeamsHandler({ ctx, input }: AddBulkTeamsHandler): Promise<{
    success: boolean;
    invitedTotalUsers: number;
}>;
export default addTeamsHandler;
