import { z } from "zod";
export declare const ZUpdateAppCredentialsInputSchema: z.ZodObject<{
    credentialId: z.ZodN<PERSON>ber;
    key: z.ZodObject<{}, "passthrough", z.<PERSON>, z.objectOutputType<{}, z.ZodType<PERSON>ny, "passthrough">, z.objectInputType<{}, z.Z<PERSON>, "passthrough">>;
}, "strip", z.ZodT<PERSON>, {
    key: {} & {
        [k: string]: unknown;
    };
    credentialId: number;
}, {
    key: {} & {
        [k: string]: unknown;
    };
    credentialId: number;
}>;
export type TUpdateAppCredentialsInputSchema = z.infer<typeof ZUpdateAppCredentialsInputSchema>;
