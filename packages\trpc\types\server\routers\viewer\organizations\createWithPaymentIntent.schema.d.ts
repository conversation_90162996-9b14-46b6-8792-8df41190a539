import type { z } from "zod";
export declare enum BillingPeriod {
    MONTHLY = "MONTHLY",
    ANNUALLY = "ANNUALLY"
}
export declare const ZCreateWithPaymentIntentInputSchema: z.ZodObject<{
    language: z.ZodOptional<z.ZodString>;
    logo: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    bio: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    onboardingId: z.ZodString;
    invitedMembers: z.ZodOptional<z.ZodArray<z.ZodObject<{
        email: z.ZodString;
        name: z.<PERSON>odOptional<z.ZodString>;
    }, "strip", z.ZodType<PERSON>ny, {
        email: string;
        name?: string | undefined;
    }, {
        email: string;
        name?: string | undefined;
    }>, "many">>;
    teams: z.ZodOptional<z.ZodArray<z.ZodObject<{
        id: z.ZodN<PERSON>;
        name: z.ZodString;
        isBeingMigrated: z.ZodBoolean;
        slug: z.ZodNullable<z.ZodString>;
    }, "strip", z.Zod<PERSON>ype<PERSON>ny, {
        name: string;
        id: number;
        slug: string | null;
        isBeingMigrated: boolean;
    }, {
        name: string;
        id: number;
        slug: string | null;
        isBeingMigrated: boolean;
    }>, "many">>;
}, "strip", z.ZodTypeAny, {
    onboardingId: string;
    language?: string | undefined;
    logo?: string | null | undefined;
    bio?: string | null | undefined;
    invitedMembers?: {
        email: string;
        name?: string | undefined;
    }[] | undefined;
    teams?: {
        name: string;
        id: number;
        slug: string | null;
        isBeingMigrated: boolean;
    }[] | undefined;
}, {
    onboardingId: string;
    language?: string | undefined;
    logo?: string | null | undefined;
    bio?: string | null | undefined;
    invitedMembers?: {
        email: string;
        name?: string | undefined;
    }[] | undefined;
    teams?: {
        name: string;
        id: number;
        slug: string | null;
        isBeingMigrated: boolean;
    }[] | undefined;
}>;
export type TCreateWithPaymentIntentInputSchema = z.infer<typeof ZCreateWithPaymentIntentInputSchema>;
